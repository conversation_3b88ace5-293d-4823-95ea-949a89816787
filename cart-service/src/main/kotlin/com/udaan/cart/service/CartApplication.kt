package com.udaan.cart.service

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.google.protobuf.util.ProtoJacksonModule
import com.udaan.cart.service.resources.v1.CartResource
import com.udaan.cart.service.resources.v1.LegacyCartResource
import com.udaan.common.server.DropwizardApplication
import com.udaan.common.server.register
import com.udaan.resources.lifecycle.LifeCycleObjectRepo
import io.dropwizard.forms.MultiPartBundle
import io.dropwizard.setup.Bootstrap
import io.dropwizard.setup.Environment
import io.dropwizard.jersey.jackson.JsonProcessingExceptionMapper
import io.dropwizard.jersey.setup.JerseyEnvironment


class CartApplication : DropwizardApplication<CartConfiguration>() {

    override fun getGuiceModules(configuration: CartConfiguration, environment: Environment) = listOf(
        CartServiceModule()
    )

    override fun getDropwizardBundles() = listOf(MultiPartBundle())

    override fun getJacksonModules() = listOf(JavaTimeModule())

    override fun getResourceClasses() = listOf(
        CartResource::class.java,
        LegacyCartResource::class.java,
    )

    override fun initializeAdditional(bootstrap: Bootstrap<CartConfiguration>) {
        bootstrap.objectMapper
            .configure(SerializationFeature.WRITE_DATE_TIMESTAMPS_AS_NANOSECONDS, false)
            .configure(DeserializationFeature.READ_DATE_TIMESTAMPS_AS_NANOSECONDS, false)
    }

    override fun runAdditional(configuration: CartConfiguration, environment: Environment) {
        environment.objectMapper
            .registerKotlinModule()
            .registerModules(ProtoJacksonModule())
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        environment.register(LifeCycleObjectRepo.global())

        environment.jersey().registerExceptionMappers()
    }

    private fun JerseyEnvironment.registerExceptionMappers() {
        register(JsonProcessingExceptionMapper(true))
    }
}

fun main(args: Array<String>) {
    CartApplication().startWithArgs(args)
}
