package com.udaan.cart.service.resources.v1

import com.google.inject.Inject
import com.udaan.cart.core.common.helpers.Validator
import com.udaan.cart.core.metrics.CartEventTracker
import com.udaan.cart.core.service.v1.LegacyCartService
import com.udaan.cart.models.BaseRequestDto
import com.udaan.cart.models.default.MarkPlacedReqDto
import com.udaan.cart.models.default.FetchCartReqDto
import com.udaan.cart.models.default.LegacyMultiSellerPlaceReqDto
import com.udaan.cart.service.resources.utils.withInstrumentedEither
import com.udaan.error.trace.annotations.Severity
import com.udaan.error.trace.annotations.UDErrorMonitored
import com.udaan.error.trace.annotations.UDErrorMonitoredApi
import javax.ws.rs.POST
import javax.ws.rs.Path
import javax.ws.rs.Produces
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.MediaType

@Path("/cart/apps/v1/legacy")
@UDErrorMonitored("2")
@Produces(MediaType.APPLICATION_JSON)
class LegacyCartResource @Inject constructor(
    private val legacyCartService: LegacyCartService,
    private val eventTracker: CartEventTracker,
) {
    private val preValidations = listOf<Validator<BaseRequestDto, Boolean>>()

    @Path("/horeca/payment-methods")
    @UDErrorMonitoredApi("1", Severity.LOW, true)
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    fun fetchHorecaOrderPaymentMethods(
        request: FetchCartReqDto,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.withInstrumentedEither(
        request.platformId.name,
        request,
        preValidations,
        eventTracker::trackPaymentMethods
    ) {
        legacyCartService.fetchHorecaPaymentMethods(request = request)
    }

    @Path("/pacman/checkout")
    @UDErrorMonitoredApi("4", Severity.LOW, true)
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    fun pacmanCheckout(
        request: LegacyMultiSellerPlaceReqDto,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.withInstrumentedEither(
        request.platformId.name,
        request,
        preValidations,
        eventTracker::trackCheckout
    ) {
        legacyCartService.pacmanCheckout(request = request)
    }

    @Path("/mario/payment-methods")
    @UDErrorMonitoredApi("2", Severity.LOW, true)
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    fun fetchMarioOrderPaymentMethods(
        request: FetchCartReqDto,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.withInstrumentedEither(
        request.platformId.name,
        request,
        preValidations,
        eventTracker::trackPaymentMethods
    ) {
        legacyCartService.fetchMarioPaymentMethods(request = request)
    }

    @Path("/cart/mark-completed")
    @UDErrorMonitoredApi("3", Severity.LOW, true)
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    fun markCartComplete(
        request: MarkPlacedReqDto,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.withInstrumentedEither(
        request.platformId.name,
        request,
        preValidations,
        eventTracker::trackConfirm
    ) {
        legacyCartService.markPlaced(request = request)
    }
}
