package com.udaan.cart.service.resources.v1

import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.cart.core.common.helpers.Validator
import com.udaan.cart.core.metrics.CartEventTracker
import com.udaan.cart.core.service.v1.CartService
import com.udaan.cart.models.BaseRequestDto
import com.udaan.cart.models.BaseResponseDto
import com.udaan.cart.models.default.*
import com.udaan.cart.service.resources.utils.withInstrumentedEither
import com.udaan.error.trace.annotations.Severity
import com.udaan.error.trace.annotations.UDErrorMonitored
import com.udaan.error.trace.annotations.UDErrorMonitoredApi
import com.udaan.proto.models.ModelV1
import javax.ws.rs.POST
import javax.ws.rs.PUT
import javax.ws.rs.Path
import javax.ws.rs.Produces
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.MediaType

@Path("/cart/app/v1")
@UDErrorMonitored("1")
@Produces(MediaType.APPLICATION_JSON)
class CartResource @Inject constructor(
    @Named("default_cart_service")
    private val cartService: CartService<BaseRequestDto, BaseResponseDto>,
    private val eventTracker: CartEventTracker,
) {

    private val preValidations = listOf<Validator<BaseRequestDto, Boolean>>(
    )

    @Path("/fetch")
    @UDErrorMonitoredApi("1", Severity.LOW, true)
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    fun findCart(
        request: FetchCartReqDto,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.withInstrumentedEither(
        ModelV1.SellingPlatform.UDAAN_MARKETPLACE.name,
        request,
        preValidations,
        eventTracker::trackFetchById
    ) {
        cartService.find(request)
    }

    @Path("/fetch-multiple")
    @UDErrorMonitoredApi("2", Severity.LOW, true)
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    fun findMultipleCarts(
        request: FetchCartReqDto,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.withInstrumentedEither(
        request.platformId.name,
        request,
        preValidations,
        eventTracker::trackFetchMultiple
    ) {
        cartService.findAll(request)
    }

    @Path("/delete")
    @UDErrorMonitoredApi("4", Severity.LOW, true)
    @PUT
    @Produces(MediaType.APPLICATION_JSON)
    fun deleteCart(
        request: DeleteCartReqDto,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.withInstrumentedEither(
        ModelV1.SellingPlatform.UDAAN_MARKETPLACE.name,
        request,
        preValidations,
        eventTracker::trackDelete
    ) {
        cartService.delete(request)
    }

    @Path("/edit")
    @UDErrorMonitoredApi("5", Severity.LOW, true)
    @PUT
    @Produces(MediaType.APPLICATION_JSON)
    fun editCart(
        request: EditCartReqDto,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.withInstrumentedEither(
        ModelV1.SellingPlatform.UDAAN_MARKETPLACE.name,
        request,
        preValidations,
        eventTracker::trackEdit
    ) {
        cartService.edit(request)
    }

    @Path("/delivery-slots")
    @UDErrorMonitoredApi("6", Severity.LOW, true)
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    fun fetchDeliverySlots(
        request: FetchCartReqDto,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.withInstrumentedEither(
        ModelV1.SellingPlatform.UDAAN_MARKETPLACE.name,
        request,
        preValidations,
        eventTracker::trackDeliverySlots
    ) {
        cartService.fetchDeliverySlots(request)
    }

    @Path("/payment-methods")
    @UDErrorMonitoredApi("7", Severity.LOW, true)
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    fun fetchPaymentMethods(
        request: PaymentMethodsReqDto,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.withInstrumentedEither(
        ModelV1.SellingPlatform.UDAAN_MARKETPLACE.name,
        request,
        preValidations,
        eventTracker::trackPaymentMethods
    ) {
        cartService.fetchPaymentMethods(request)
    }

    @Path("/checkout")
    @UDErrorMonitoredApi("8", Severity.LOW, true)
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    fun checkoutCart(
        request: CheckoutRequestDto,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.withInstrumentedEither(
        ModelV1.SellingPlatform.UDAAN_MARKETPLACE.name,
        request,
        preValidations,
        eventTracker::trackCheckout
    ) {
        cartService.checkout(request)
    }

    @Path("/updateCreditLine")
    @UDErrorMonitoredApi("9", Severity.LOW, true)
    @PUT
    @Produces(MediaType.APPLICATION_JSON)
    fun updateCreditLine(
        request: UpdateCreditLineReqDto,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.withInstrumentedEither(
        ModelV1.SellingPlatform.UDAAN_MARKETPLACE.name,
        request,
        preValidations,
        eventTracker::trackCreditLineUpdate
    ) {
        cartService.updateCreditLine(request)
    }
}
