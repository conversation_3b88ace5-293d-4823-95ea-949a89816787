package com.udaan.cart.service.resources.utils

import arrow.core.Either
import arrow.core.continuations.either
import com.udaan.cart.core.common.helpers.Validator
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.metrics.TrackerContext
import com.udaan.cart.models.BaseRequestDto
import com.udaan.common.utils.kotlin.logger
import com.udaan.resources.with
import com.udaan.service.api.UdaanServiceError
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.core.Response
import kotlin.time.measureTimedValue

fun <E, T> AsyncResponse.withInstrumentedEither(
    platformId: String,
    requestDto: BaseRequestDto,
    validators: List<Validator<BaseRequestDto, Boolean>>,
    trackEvent: suspend (context: TrackerContext) -> Unit,
    block: suspend () -> Either<E, T>
) = with {
    val logger by logger()
    val initData = either<OpError, Boolean> { Either.Right(true).bind() }
    val validations = validators.fold(initData) { acc, validator ->
        when (acc) {
            is Either.Left -> acc
            is Either.Right ->
                validator.validate(requestDto)
        }
    }
    when (validations) {
        is Either.Left -> {
            Response.status(Response.Status.BAD_REQUEST).entity(
                UdaanServiceError(validations.value.message)
            ).build()
        }
        is Either.Right -> {
            val (result, timeTaken) = measureTimedValue {
                block()
            }
            trackEvent(TrackerContext(platformId, requestDto, timeTaken))
            result.fold(
                ifLeft = {
                    logger.error("Failed for $requestDto with Error: ${it as String}")
                    Response.status(Response.Status.BAD_REQUEST).entity(
                        UdaanServiceError(it as String)
                    ).build()
                },
                ifRight = { it }
            )
        }
    }
}
