package com.udaan.cart.service

import com.google.inject.Guice
import com.google.inject.Key
import com.google.inject.TypeLiteral
import com.google.inject.name.Names
import com.udaan.cart.core.service.v1.CartService
import com.udaan.cart.models.BaseRequestDto
import com.udaan.cart.models.BaseResponseDto
import com.udaan.cart.models.common.CategoryGroupSelection
import com.udaan.cart.models.common.Requester
import com.udaan.cart.models.default.FetchCartReqDto
import com.udaan.proto.models.ModelV1
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.joda.time.DateTime
import kotlin.system.exitProcess

class CartNativeApplication {
    suspend fun run() {
        val injector = Guice.createInjector(CartServiceModule())
        val start = DateTime.now()
        val end = start.plusMinutes(30)
        while (DateTime.now().isBefore(end)) {
            val cartService =
                injector.getInstance(
                    Key.get(
                        object : TypeLiteral<CartService<BaseRequestDto, BaseResponseDto>>() {},
                        Names.named("default_cart_service"),
                    )
                )
            val carts = cartService.findAll(
                FetchCartReqDto(
                    buyerId = "ORGNX5DX102QFCKXFNJ724DSNTS5T",
                    platformId = ModelV1.SellingPlatform.UDAAN_MARKETPLACE,
                    requester = Requester(
                        userId = "USRF0QR92MZPXD9FGRX1XEYE9WXXL",
                        orgId = "ORGNX5DX102QFCKXFNJ724DSNTS5T"
                    ),
                    cartId = null,
                    cartSelection = CategoryGroupSelection(
                        categoryGroupId = "pharma",
                    ),
                )
            )
            println("${DateTime.now()} - Carts: $carts")
            delay(60 * 1000)
        }
    }
}

fun main() {
    runBlocking {
        CartNativeApplication().run()
        exitProcess(0)
    }
}
