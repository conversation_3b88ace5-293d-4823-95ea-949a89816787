user-service:
  baseUrl: "http://svc-hack.prod.udaan.io/user-service"
  httpClientConfig:
    requestTimeout: 10000

resilient-catalog-read-path:
  baseUrl: "https://svc-hack.prod.udaan.io/catalog-service"
  httpClientConfig:
    requestTimeout: 10000

credit:
  baseUrl: "https://svc-hack.prod.udaan.io/credit-service"
  httpClientConfig:
    requestTimeout: 20000

resiliency-price-read-path:
  baseUrl: "https://svc-hack.prod.udaan.io/pricing-service"
  httpClientConfig:
    requestTimeout: 15000

orderform-service:
  baseUrl: "https://svc-hack.prod.udaan.io/orderform-service"
  httpClientConfig:
    requestTimeout: 30000

pacman-orderform-service:
  baseUrl: "https://svc-hack.prod.udaan.io/pacman-orderform-service"
  httpClientConfig:
    requestTimeout: 30000

#orderform-service:
#  baseUrl: "https://svc-hack.prod.udaan.io/orderform-service"
#  httpClientConfig:
#    requestTimeout: 10000

orchestrator-service:
  baseUrl: "https://svc-hack.prod.udaan.io/orchestrator-service"
  httpClientConfig:
    requestTimeout: 10000

chat:
  baseUrl: "https://svc-hack.prod.udaan.io/chat-service"
  httpClientConfig:
    requestTimeout: 10000
    threadPoolName: AsyncHttpClient-chat

compliance-service:
  baseUrl: "https://svc-hack.prod.udaan.io/compliance-service"
  httpClientConfig:
    requestTimeout: 20000

constraint-service:
  baseUrl: "https://svc-hack.prod.udaan.io/constraint-service"
  httpClientConfig:
    requestTimeout: 10000

promotions-service:
  baseUrl: "https://svc-hack.prod.udaan.io/promotions-service"
  httpClientConfig:
    requestTimeout: 20000

dropslot-service:
  baseUrl: "https://svc-hack.prod.udaan.io/dropslot-service"
  httpClientConfig:
    requestTimeout: 10000

fulfillment-service:
  baseUrl: "https://svc-hack.prod.udaan.io/fulfilment-service"
  httpClientConfig:
    requestTimeout: 10000

trade-quality-service:
  baseUrl: "https://svc-hack.prod.udaan.io/trade-quality-service"
  httpClientConfig:
    requestTimeout: 10000

config-service:
  baseUrl: "https://svc-hack.prod.udaan.io/config-service"
  httpClientConfig:
    requestTimeout: 10000

rewards-service:
  baseUrl: "https://svc-hack.prod.udaan.io/rewards-service"
  httpClientConfig:
    requestTimeout: 10000

order-read-service:
  baseUrl: "http://svc-hack.prod.udaan.io/order-read-service"
  httpClientConfig:
    requestTimeout: 10000

trade-credit-service:
  baseUrl: "https://svc-hack.prod.udaan.io/trade-credit-service"
  httpClientConfig:
    requestTimeout: 30000
