prometheus: udaan-app
endpointSpec:
  path: /prometheus-metrics
  interval: "15s"
alert-spec:
  groups:
    - name: ./cart-service.rules
      rules:
        - alert: CartHttpErrors5xx
          expr: (sum(rate(http_request_duration_seconds_count{namespace="prod", service="cart-service", code=~"5.."}[5m]))/sum (rate(http_request_duration_seconds_count{namespace="prod", service="cart-service"}[5m])) * 100 > 2.5)
          for: 5m
          labels:
            severity: error
            instance: cart-service
            trigger_route: alerting-service
            service_slug: orderform-service
            status_code: 5
            slack_channel: order-alerts
          annotations:
            summary: "HTTP errors 5xx"
            description: "CART SERVICE: HTTP requests with status 5xx (> 2.5%)\n  failures_percent = {{$value}}\n"
        - alert: CartHttpErrors5xx
          expr: (sum(rate(http_request_duration_seconds_count{service="cart-service", path="/cart/app", code=~"5.."}[5m]))/sum (rate(http_request_duration_seconds_count{service="cart-service", path="/cart/app"}[5m])) * 100 > 2.5)
          for: 5m
          labels:
            severity: error
            instance: cart-service
            trigger_route: alerting-service
            service_slug: orderform-service
            status_code: 5
            slack_channel: order-alerts
          annotations:
            summary: "Buyer Cart HTTP errors 5xx"
            description: "CART SERVICE, /cart/app: HTTP requests with status 5xx (> 2.5%)\n,  FAILURES_PERCENTAGE = {{$value}}\n"
        - alert: LegacyCartHttpErrors5xx
          expr: (sum(rate(http_request_duration_seconds_count{service="cart-service", path="/cart/apps", code=~"5.."}[5m]))/sum (rate(http_request_duration_seconds_count{service="cart-service", path="/cart/app"}[5m])) * 100 > 2.5)
          for: 5m
          labels:
            severity: error
            instance: cart-service
            trigger_route: alerting-service
            service_slug: orderform-service
            status_code: 5
            slack_channel: order-alerts
          annotations:
            summary: "Legacy Cart Operations HTTP errors 5xx"
            description: "CART SERVICE, /cart/apps: HTTP requests with status 5xx (> 2.5%)\n,  FAILURES_PERCENTAGE = {{$value}}\n"
        - alert: CartResponseTimeDegradation
          expr: histogram_quantile(0.90, sum(rate(http_request_duration_seconds_bucket{job="cart-service", path="/cart/app"}[5m])) by (le)) > 2
          for: 5m
          labels:
            severity: error
            instance: cart-service
            slack_channel: 3f-alerts
          annotations:
            summary: "Buyer Cart Response Time Degradation"
            description: "CART SERVICE, /cart/app: HTTP p90 response time (> 2s)\n,  RESPONSE_TIME = {{$value}}\n"
        - alert: LegacyCartResponseTimeDegradation
          expr: histogram_quantile(0.90, sum(rate(http_request_duration_seconds_bucket{job="cart-service", path="/cart/apps"}[5m])) by (le)) > 2
          for: 5m
          labels:
            severity: error
            instance: cart-service
            slack_channel: 3f-alerts
          annotations:
            summary: "Legacy Cart Operations Response Time Degradation"
            description: "CART SERVICE, /cart/apps: HTTP p90 response time (> 2s)\n,  RESPONSE_TIME = {{$value}}\n"
        - alert: CartAPIErrors5xx
          expr: (sort_desc(sum(rate(http_request_duration_seconds_count{namespace="prod", service="cart-service", code=~"5.."}[5m])) by (resource)/sum (rate(http_request_duration_seconds_count{namespace="prod", service="cart-service"}[5m])) by (resource)) * 100 > 1) and (sum(rate(http_request_duration_seconds_count{namespace="prod", code=~"5..", service="cart-service"}[5m])) by (resource) > 0.01)
          for: 5m
          labels:
            severity: error
            instance: cart-service
            trigger_route: alerting-service
            service_slug: orderform-service
            status_code: 5
            slack_channel: order-alerts
          annotations:
            summary: "API errors 5xx"
            description: "cart-service: APIs with status 5xx (>1% and >3)\n APIs: {{$value}}\n Labels = {{$labels}}\n"
        - alert: CartDependencyErrors5xx
          expr: (sort_desc(sum(rate(http_dependency_call_total{namespace="prod",job="cart-service",code=~"5.."}[5m])) by (target) / sum(rate(http_dependency_call_total{namespace="prod",job="cart-service"}[5m])) by (target)) * 100 > 1) and (sum(rate(http_dependency_call_total{namespace="prod",job="cart-service",code=~"5.."}[5m])) by (target) > 0.033)
          for: 3m
          labels:
            severity: error
            instance: cart-service
            slack_channel: order-alerts
          annotations:
            summary: "Dependency HTTP errors 5xx"
            description: "cart-service: Dependencies with status 5xx (>1% and >=10)\n Dependencies: {{$value}}\n Labels: {{$labels}}"
        - alert: CartPodIncreaseInWaitingThreads
          expr: sort_desc(jvm_threads_state{namespace="prod",service="cart-service",state="WAITING"}) > 400
          for: 5m
          labels:
            severity: error
            instance: cart-service
            trigger_route: alerting-service
            service_slug: orderform-service
            slack_channel: order-alerts
          annotations:
            summary: Cart service pod(s) waiting thread count is increasing
            description: Blocking call has blocked threads. Capture the thread dump of the pod and kill the pod "{{$value}}"

        - record: prod_cart_service:http_request_duration_seconds_count:sum_rate5m
          expr: sum (rate(http_request_duration_seconds_count{namespace="prod", service="cart-service"}[5m]))

        - record: prod_cart_service:error_http_request_duration_seconds_count:sum_rate5m
          expr: sum(rate(http_request_duration_seconds_count{namespace="prod", service="cart-service", code=~"5.."}[5m]))

        - record: prod_cart_service:http_request_duration_seconds_count:sum1m
          expr: sum(increase(http_request_duration_seconds_count{namespace="prod", service="cart-service"}[1m]))

        - record: prod_cart_service:error_http_request_duration_seconds_count:sum1m
          expr: sum(increase(http_request_duration_seconds_count{namespace="prod", service="cart-service", code=~"5.."}[1m]))

        - record: prod_cart_service:http_request_duration_seconds_count:sum5m
          expr: sum(increase(http_request_duration_seconds_count{namespace="prod", service="cart-service"}[5m]))

        - record: prod_cart_service:error_http_request_duration_seconds_count:sum5m
          expr: sum(increase(http_request_duration_seconds_count{namespace="prod", service="cart-service", code=~"5.."}[5m]))

        - record: prod_cart_service:http_request_duration_seconds_bucket_by_le_50:histogram5m
          expr: histogram_quantile(0.50, sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="cart-service"}[5m])) by (le))

        - record: prod_cart_service:http_request_duration_seconds_bucket_by_le_95:histogram5m
          expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="cart-service"}[5m])) by (le))

        - record: prod_cart_service:http_request_duration_seconds_bucket_by_le_99:histogram5m
          expr: histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="cart-service"}[5m])) by (le))

        - record: prod_cart_service:http_request_duration_seconds_count:sum_rate15m
          expr: sum(rate(http_request_duration_seconds_count{namespace="prod", service="cart-service"}[15m]))

        - record: prod_cart_service:http_request_duration_seconds_bucket:le0dot01_sum_rate15m
          expr: (sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="cart-service", le="0.01"}[15m])))

        - record: prod_cart_service:http_request_duration_seconds_bucket:le0dot05_sum_rate15m
          expr: (sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="cart-service", le="0.05"}[15m])))

        - record: prod_cart_service:http_request_duration_seconds_bucket:le0dot1_sum_rate15m
          expr: (sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="cart-service", le="0.1"}[15m])))

        - record: prod_cart_service:http_request_duration_seconds_bucket:le0dot5_sum_rate15m
          expr: (sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="cart-service", le="0.5"}[15m])))

        - record: prod_cart_service:http_request_duration_seconds_bucket:le1_sum_rate15m
          expr: (sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="cart-service", le="1.0"}[15m])))

        - record: prod_cart_service:http_request_duration_seconds_bucket:le2_sum_rate15m
          expr: (sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="cart-service", le="2.0"}[15m])))

        - record: prod_cart_service:http_request_duration_seconds_bucket:le5_sum_rate15m
          expr: (sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="cart-service", le="5.0"}[15m])))

        - record: prod_cart_service:http_request_duration_seconds_bucket:leInf_sum_rate15m
          expr: (sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="cart-service", le="+Inf"}[15m])))

        - record: prod_cart_service:http_request_duration_seconds_sum:sum_rate5m
          expr: sum(rate(http_request_duration_seconds_sum{namespace="prod", service="cart-service"}[5m]))

        - record: prod_cart_service:http_request_duration_seconds_sum:sum_rate15m
          expr: sum(rate(http_request_duration_seconds_sum{namespace="prod", service="cart-service"}[15m]))