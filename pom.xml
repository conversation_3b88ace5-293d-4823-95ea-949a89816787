<?xml version="1.0" encoding="UTF-8"?>
<project
        xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.udaan</groupId>
        <artifactId>udaan-parent</artifactId>
        <version>[3.0,4.0)</version>
    </parent>

    <groupId>com.udaan.cart</groupId>
    <artifactId>cart-parent</artifactId>
    <version>2.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>Udaan cart</name>

    <profiles>
        <profile>
            <id>default</id>
            <activation>
                <property>
                    <name>!exclude-default</name>
                </property>
            </activation>
            <modules>
                <module>cart-api</module>
            </modules>
        </profile>
    </profiles>

    <modules>
        <module>cart-core</module>
        <module>cart-service</module>
        <module>pharma-insti-cart-service</module>
        <module>cart-jobs</module>
    </modules>

    <properties>
        <versions.udaan-common>[2.0,3.0)</versions.udaan-common>
        <versions.udaan-user>[2.0,3.0)</versions.udaan-user>
        <versions.udaan-catalog>[2.0,3.0)</versions.udaan-catalog>
        <versions.udaan-catalog-common>[1.0,2.0)</versions.udaan-catalog-common>
        <versions.order-mgt-model>[4.0,5.0)</versions.order-mgt-model>
        <versions.udaan-compliance>[1.0,2.0)</versions.udaan-compliance>
        <versions.udaan-incentives>[2.0,3.0)</versions.udaan-incentives>
        <versions.dropwizard-proto>[2.0,3.0)</versions.dropwizard-proto>
        <arrow.version>1.1.2</arrow.version>
        <versions.orderform>[3.0,4.0)</versions.orderform>
        <versions.udaan-listing>[1.0,2.0)</versions.udaan-listing>
        <versions.constraint>[1.0,2.0)</versions.constraint>
        <versions.udaan-invoice>[2.0,3.0)</versions.udaan-invoice>
        <versions.dropslot>[1.0,2.0)</versions.dropslot>
        <versions.udaan-config>[1.0,2.0)</versions.udaan-config>
        <versions.tradequality>[1.0,2.0)</versions.tradequality>
        <versions.udaan-sc-network>[1.0, 2.0)</versions.udaan-sc-network>
        <versions.order-read>[1.0,2.0)</versions.order-read>
        <versions.tradecredit>[1.0,2.0)</versions.tradecredit>
        <detekt.phase>verify</detekt.phase>
        <klint.phase>none</klint.phase>
        <kotlin.compiler.incremental>true</kotlin.compiler.incremental>
    </properties>

    <dependencyManagement>
        <dependencies>

            <!-- Udaan cart -->
            <dependency>
                <groupId>com.udaan.cart</groupId>
                <artifactId>cart-models</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.cart</groupId>
                <artifactId>cart-core</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Udaan Common -->
            <dependency>
                <groupId>com.udaan.compliance</groupId>
                <artifactId>client</artifactId>
                <version>${versions.udaan-compliance}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.common</groupId>
                <artifactId>common-api</artifactId>
                <version>${versions.udaan-common}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.common</groupId>
                <artifactId>common-utils</artifactId>
                <version>${versions.udaan-common}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.common</groupId>
                <artifactId>common-server</artifactId>
                <version>${versions.udaan-common}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.common</groupId>
                <artifactId>common-client</artifactId>
                <version>${versions.udaan-common}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.common</groupId>
                <artifactId>common-slack</artifactId>
                <version>${versions.udaan-common}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.common</groupId>
                <artifactId>common-jdbi</artifactId>
                <version>${versions.udaan-common}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.dropwizard</groupId>
                <artifactId>dropwizard-proto</artifactId>
                <version>${versions.dropwizard-proto}</version>
            </dependency>

            <!-- Udaan Service Clients etc. -->
            <dependency>
                <groupId>com.udaan.config</groupId>
                <artifactId>config-client</artifactId>
                <version>${versions.udaan-config}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.users</groupId>
                <artifactId>user-client</artifactId>
                <version>${versions.udaan-user}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.users</groupId>
                <artifactId>user-model</artifactId>
                <version>${versions.udaan-user}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.catalog</groupId>
                <artifactId>catalog-model</artifactId>
                <version>${versions.udaan-catalog}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.catalog</groupId>
                <artifactId>catalog-client</artifactId>
                <version>${versions.udaan-catalog}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.catalog</groupId>
                <artifactId>catalog-common</artifactId>
                <version>${versions.udaan-catalog-common}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.order-mgt</groupId>
                <artifactId>order-mgt-model</artifactId>
                <version>${versions.order-mgt-model}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.udaan.incentives</groupId>
                        <artifactId>promotions-model</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.itextpdf</groupId>
                        <artifactId>itextpdf</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.udaan.udaan-orderform</groupId>
                <artifactId>orderform-common</artifactId>
                <version>${versions.orderform}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.udaan.incentives</groupId>
                        <artifactId>promotions-model</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.itextpdf</groupId>
                        <artifactId>itextpdf</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.udaan.incentives</groupId>
                <artifactId>promotions-client</artifactId>
                <version>${versions.udaan-incentives}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.udaan.targeting</groupId>
                        <artifactId>targeting-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.udaan.udaan-orderform</groupId>
                <artifactId>orderform-client</artifactId>
                <version>${versions.orderform}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.udaan-orderform</groupId>
                <artifactId>orderform-models</artifactId>
                <version>${versions.orderform}</version>
            </dependency>

            <dependency>
                <groupId>com.udaan.constraint</groupId>
                <artifactId>constraint-models</artifactId>
                <version>${versions.constraint}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.constraint</groupId>
                <artifactId>constraint-client</artifactId>
                <version>${versions.constraint}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.udaan.credit</groupId>
                        <artifactId>credit-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.udaan.invoicing</groupId>
                <artifactId>invoicing-math</artifactId>
                <version>${versions.udaan-invoice}</version>
            </dependency>

            <dependency>
                <groupId>com.udaan.invoicing</groupId>
                <artifactId>invoicing-client</artifactId>
                <version>${versions.udaan-invoice}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.listing</groupId>
                <artifactId>listing-builder</artifactId>
                <version>${versions.udaan-listing}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.udaan.catalog</groupId>
                        <artifactId>catalog-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.udaan.buyeridentity</groupId>
                        <artifactId>buyer-identity-models</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.reactivestreams</groupId>
                        <artifactId>reactive-streams</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-resolver</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.udaan.order-mgt</groupId>
                        <artifactId>order-mgt-model</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-handler-proxy</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.codehaus.woodstox</groupId>
                        <artifactId>stax2-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.udaan.users</groupId>
                        <artifactId>user-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.udaan.console</groupId>
                        <artifactId>console-one-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.udaan.insights</groupId>
                        <artifactId>insights-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.udaan.recommendations</groupId>
                        <artifactId>recommendations-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.udaan.dropslot</groupId>
                <artifactId>dropslot-client</artifactId>
                <version>${versions.dropslot}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.tradequality</groupId>
                <artifactId>trade-quality-client</artifactId>
                <version>${versions.tradequality}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.udaan.common</groupId>
                        <artifactId>common-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.udaan.order-service</groupId>
                <artifactId>order-read-client</artifactId>
                <version>${versions.order-read}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.tradecredit</groupId>
                <artifactId>trade-credit-client</artifactId>
                <version>${versions.tradecredit}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.scnetwork</groupId>
                <artifactId>sc-network-client</artifactId>
                <version>${versions.udaan-sc-network}</version>
            </dependency>

            <!-- Kotlin & language related -->
            <dependency>
                <groupId>io.arrow-kt</groupId>
                <artifactId>arrow-core</artifactId>
                <version>${arrow.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito.kotlin</groupId>
                <artifactId>mockito-kotlin</artifactId>
                <version>3.2.0</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>net.bytebuddy</groupId>
                        <artifactId>byte-buddy-agent</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <executions>
                    <execution>
                        <id>default-compile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>default-testCompile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>testCompile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <jvmTarget>1.8</jvmTarget>
                    <args>
                        <arg>-Xopt-in=kotlin.time.ExperimentalTime</arg>
                    </args>
                </configuration>
            </plugin>
            <!--klint-->
            <plugin>
                <groupId>com.github.gantsign.maven</groupId>
                <artifactId>ktlint-maven-plugin</artifactId>
                <version>1.4.2</version>
                <executions>
                    <execution>
                        <phase>${klint.phase}</phase>
                        <id>format</id>
                        <goals>
                            <goal>format</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
