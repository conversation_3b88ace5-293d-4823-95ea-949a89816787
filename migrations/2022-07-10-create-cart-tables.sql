CREATE TABLE carts
(
    id                  varchar(64) primary key,
    platform_id         varchar(64)  not null,
    type                varchar(64)  not null,
    buyer_id            varchar(128) not null,
    selection           jsonb        not null,
    state               varchar(128) not null,
    current_active      bool         not null,
    order_info          jsonb null,
    created_at          timestamp    not null,
    updated_at          timestamp    not null
);

CREATE TABLE cart_items
(
    id                       varchar(64) primary key,
    cart_id                  varchar(64) not null,
    quantity                 integer     not null,
    product                  jsonb       not null,
    per_unit_amount_in_paisa float       not null,
    price_details            jsonb       not null,
    properties               jsonb       not null,
    current_active           bool        not null,
    order_info               jsonb       null,
    created_at               timestamp   not null,
    updated_at               timestamp   not null,
    CONSTRAINT fk_cart_items
        FOREIGN KEY(cart_id)
            REFERENCES carts(id)
);

create index idx_cart_buyer_id on carts(buyer_id);
create index idx_cart_items_cart_id on cart_items(cart_id);