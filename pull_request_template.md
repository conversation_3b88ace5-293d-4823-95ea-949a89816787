<!--- Provide a general summary of your changes in the Title above -->

## Motivation and Context
<!--- Why is this change required? What problem does it solve? Link to PRD, TRD?-->
<!--- If it fixes an open issue, please link to the issue here. Add the Notion issue number. -->

## Description
<!--- Describe your changes in detail -->


## Types of changes
<!--- What types of changes does your code introduce? Put an `x` in all the boxes that apply: -->
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to change)
- [ ] Non-breaking Change (Tests, Scripts, etc. which do not impact production code)
- [ ] Config Change (YAML)

## Impact
<!--- Does it impact existing API, client, etc.? Mention them. Have you checked your dependent systems?  -->
- [ ] API
- [ ] Client
- [ ] Models
- [ ] DB
- [ ] Cronjob
- [ ] No Impact On Prod

## Testing [TEST CASES](https://www.notion.so/udaantech/Test-Cases-b50ec5941da44bb98e08237f673b7abf?pvs=4)
<!--- Please describe in detail how you tested your changes. -->
<!--- Include details of your testing environment, and the tests you ran to -->
<!--- see how your change affects other areas of the code, etc. -->

- [ ] Unit Tested
- [ ] Functionally Tested

<!--- Use the run with coverage option in intellij to find the coverage -->
**Unit Test Coverage % :**

**Functional Test Cases:**
<!--- Eg. Case 1: Tested the API via Postman for invalid user-id -->
<!--- Eg. Case 2: Tested the API via Postman for valid user-id -->

## Screenshots (if appropriate):

## Checklist:
<!--- Go over all the following points, and put an `x` in all the boxes that apply. -->
<!--- If you're unsure about any of these, don't hesitate to ask. We're here to help! -->
- [ ] Server is running & not crash-looping
- [ ] Required configs are added
- [ ] Required db-migration has been done
- [ ] [coding guidelines document](https://www.notion.so/udaantech/Code-Guidelines-a8160ddf7d3a4715a242392ad7a65449
  ) is followed

## Query performance Analysis
**Please fill this if any new query introduced or if any existing query has been modified in the code.**
### Query Statement:
<!--- Example: SELECT * FROM users where id = <user-id> -->

### Predicted execution count per hour:

## Database analysis: [How-to-QPA](https://www.notion.so/udaantech/Performing-Query-Plan-Analysis-817c4d0fdf33478585ac31e549287c95)
<!--- The query has to be run on db-test with two sample datasets(small and large). -->
<!--- Make sure that schema of test and prod are in sync -->
<!--- In case there isn't enough data in dev, prod can be used ONLY for read queries analysis -->


### Index information of all tables used
**Screenshots**
(SQL : Find index details using `exec sp_helpindex {{table_name}}`)

### **Execution 1 : Small Data Set**:
<!--- Execute with a small dataset Example : SELECT * FROM users where id in ('USR001', 'USR002', 'USR003') -->

**Query plan screenshot:**


---

### **Execution 2: Large Data Set**
<!--- Execute with a large dataset Example : SELECT * FROM users where id in ('USR001', 'USR002', 'USR003' ... 'USR600') -->

**Query plan Screenshot:**

<!--- Canary details to be published after the canary -->
## Canary Details [GUIDELINES](https://www.notion.so/udaantech/Canary-Monitoring-Guidelines-0ad0e9d389664890817cc23e8a937b42)
[#bat-eye](https://dataexplorer.azure.com/dashboards/3d9c160f-0109-4973-a3bf-9959b8e6a789?p-_startTime=30minutes&p-_endTime=now&p-_containerName=v-preorder-service&p-_resourceAPIs=v-GET.ContentResource.getContentsForScreen&p-_exclude5XX=v-true&p-platform=all&p-_bin_timestamp=v-1m#2fce0655-2c75-4e24-851d-ba5bc27c9cc0)

**Duration With % Breakdown:**
<!--- Example: Total 3 Hours, 1 hour at 5% and then 2 hours at 25% -->

**Details, with screenshots:**
<!--- Example: Use Canary Page SS from [#bat-eye](https://dataexplorer.azure.com/dashboards/3d9c160f-0109-4973-a3bf-9959b8e6a789?p-_startTime=30minutes&p-_endTime=now&p-_containerName=v-preorder-service&p-_resourceAPIs=v-GET.ContentResource.getContentsForScreen&p-_exclude5XX=v-true&p-platform=all&p-_bin_timestamp=v-1m#2fce0655-2c75-4e24-851d-ba5bc27c9cc0) -->