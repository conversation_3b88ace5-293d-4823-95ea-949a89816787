package com.udaan.cart.core.response.builders

import arrow.core.Either
import arrow.core.right
import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.cart.core.common.providers.DeliverySlotProvider
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.cart.core.response.DeliverySlotResponseBuilder
import com.udaan.cart.models.BaseResponseDto
import com.udaan.cart.models.common.CategoryDeliverySlot
import com.udaan.cart.models.common.DeliverySlotDetails
import com.udaan.cart.models.common.DeliverySlotDto
import com.udaan.common.utils.kotlin.logger
import com.udaan.listing.builder.enrichments.catalog.CategoryConfigHelper
import org.joda.time.DateTime

class DefaultDeliverySlotResBuilder @Inject constructor(
    @Named("delivery_slot_provider")
    private val deliverySlotProvider: DeliverySlotProvider,
    private val categoryConfigHelper: CategoryConfigHelper,
) : DeliverySlotResponseBuilder {

    companion object {
        private val logger by logger()
    }

    override suspend fun build(
        cartContext: CartContext,
        buyerOrgUnitId: String?
    ): Either<String, BaseResponseDto> {
        return cartContext.getCart()?.let { cart ->
            val itemsBySeller = cart.items.groupBy { item ->
                val productItem = item.product as ListingProductItem
                productItem.sellerId
            }
            cartContext.getBuyerOrgUnit(buyerOrgUnitId)?.let { buyerOrgUnit ->
                val slotsBySeller = itemsBySeller.map { (sellerId, items) ->
                    val item = items.first().product as ListingProductItem
                    val category = categoryConfigHelper.getCategory(
                        listingId = item.listingId,
                        vertical = null,
                        sellerOrg = null,
                    )
                    val slots = deliverySlotProvider.getDeliverySlots(
                        sellerId = sellerId,
                        buyerId = cart.buyerId,
                        buyerOrgUnitId = buyerOrgUnit.orgUnitId,
                        platform = cart.platformId,
                    )
                    logger.info("Delivery slots for $sellerId, ${cart.buyerId}, ${buyerOrgUnit.orgUnitId} $slots")
                    val today = DateTime.now().withTimeAtStartOfDay()
                    val slotDetails = slots.map { slot ->
                        DeliverySlotDetails(
                            slotId = slot.slotId,
                            available = slot.slotAvailable,
                            currentActive = slot.currentActive,
                            delayedSla = slot.delayedSla,
                            startTimestamp = today.plusMinutes(
                                slot.deliveryDay * 24 * 60 + slot.startMinuteOfDay
                            ).millis,
                            endTimestamp = today.plusMinutes(
                                slot.deliveryDay * 24 * 60 + slot.endMinuteOfDay
                            ).millis,
                        )
                    }
                    CategoryDeliverySlot(
                        category = category ?: "",
                        slots = slotDetails,
                    )
                }
                DeliverySlotDto(
                    deliverySlots = slotsBySeller,
                ).right()
            }
        } ?: DeliverySlotDto(deliverySlots = emptyList()).right()
    }
}
