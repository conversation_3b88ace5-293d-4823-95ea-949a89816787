package com.udaan.cart.core.operations.v1.contexts

import com.udaan.cart.core.common.providers.CheckoutDetails
import com.udaan.cart.core.context.BaseOperationContext
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.domain.models.Cart
import com.udaan.cart.core.selectors.CartSelector
import com.udaan.cart.core.selectors.data.SelectorData

data class CheckoutCartContext<R>(
    val request: R,
    val selectorData: SelectorData,
    val cartSelector: CartSelector<Cart>,
): BaseOperationContext() {
    companion object {
        const val CHECKOUT_DETAILS = "CHECKOUT_DETAILS"
    }

    private var cartContext: CartContext? = null
    override suspend fun getCartContext(): CartContext? {
        return cartContext
    }

    fun setCartContext(cartContext: CartContext) {
        this.cartContext = cartContext
    }
}