package com.udaan.cart.core.utils

import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.catalog.client.CategoryGroupV2
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.listing.builder.enrichments.catalog.CategoryConfigHelper
import com.udaan.tradecredit.client.TradeCreditClient
import com.udaan.tradecredit.models.dto.TradeCreditLineInfo
import com.udaan.tradecredit.models.enum.TradeCreditLineType
import com.udaan.user.client.OrgInternalIdentityCacheClient
import kotlinx.coroutines.withTimeout
import javax.inject.Inject

class CartContextUtils @Inject constructor(
    private val categoryConfigHelper: CategoryConfigHelper,
    private val orgInternalIdentityCacheClient: OrgInternalIdentityCacheClient,
    private val traceCreditClient: TradeCreditClient
) {

    companion object {
        private val log by logger()
        // For Client - getTradeCreditLineForLineIdAndBuyerOrgId
        // P95: 58ms | P:99: 125ms | P99.9: 751ms
        const val GET_TC_LINE_CALL_TIMEOUT = 750L
    }

    suspend fun isCPODEnabled(cartContext: CartContext): Boolean {
        val buyerOrg = cartContext.getCart()?.buyerId ?: run {
            log.info("CPOD flag = false | reason = Buyer org not found ${cartContext.getCartId()}")
            return false
        }

        val cpodBuyer = getCreditLineInfo(cartContext, buyerOrg)?.cpodBuyer ?: run {
            log.info("CPOD flag = false | reason = creditLineInfo not found for buyerOrg: $buyerOrg")
            return false
        }

        log.info("CPOD flag = $cpodBuyer | reason = creditLineInfo.cpodBuyer is $cpodBuyer for buyerOrg: $buyerOrg")
        return cpodBuyer
    }


    private suspend fun getCreditLineInfo(cartContext: CartContext, buyerOrg: String): TradeCreditLineInfo? {
        return (cartContext.getCart()?.items?.first()?.product as? ListingProductItem)?.creditLineId?.let {
            runCatching {
                withTimeout(GET_TC_LINE_CALL_TIMEOUT) {
                    traceCreditClient.getTradeCreditLineForLineIdAndBuyerOrgId(it, buyerOrg).executeAwait()
                }
            }.onFailure {
                log.error("CartContextUtils.getCreditLineInfo : Error fetching credit line info " +
                        "from buyer $buyerOrg", it)
            }.getOrNull()
        }
    }
}
