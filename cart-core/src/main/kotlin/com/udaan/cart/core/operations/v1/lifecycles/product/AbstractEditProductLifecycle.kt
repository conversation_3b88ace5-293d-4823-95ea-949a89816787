package com.udaan.cart.core.operations.v1.lifecycles.product

import arrow.core.Either
import arrow.core.continuations.either
import com.udaan.cart.core.common.helpers.Lifecycle
import com.udaan.cart.core.common.helpers.LifecycleStage
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.BaseContext
import com.udaan.cart.core.operations.v1.contexts.EditCartContext
import com.udaan.cart.core.operations.v1.hooks.EditProductHookCollection

abstract class AbstractEditProductLifecycle<R> {
    protected abstract suspend fun setupHooks(
        hooks: EditProductHookCollection<OpError, EditCartContext<R>>,
        context: EditCartContext<R>
    ): EditProductHookCollection<OpError, EditCartContext<R>>

    protected abstract suspend fun doLoadProducts(
        context: EditCartContext<R>
    ): Either<OpError, EditCartContext<R>>

    protected abstract suspend fun doLoadPrices(
        context: EditCartContext<R>
    ): Either<OpError, EditCartContext<R>>

    protected abstract suspend fun doEditCart(
        context: EditCartContext<R>
    ): Either<OpError, EditCartContext<R>>

    @Suppress("UNCHECKED_CAST")
    suspend fun <T : BaseContext> execute(
        context: EditCartContext<R>,
        hooks: EditProductHookCollection<OpError, T>
    ): Either<OpError, EditCartContext<R>> {
        val hook = setupHooks(
            hooks as EditProductHookCollection<OpError, EditCartContext<R>>,
            context
        )
        val stages = listOf(
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doLoadProducts(currentContext)
                },
                post = hook.doPostProductFetch,
            ),
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doLoadPrices(currentContext)
                },
                post = hook.doPostPriceFetch,
            ),
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doEditCart(currentContext)
                },
                post = hook.doPostEditHooks,
            ),
        )
        return either {
            Lifecycle(stages = stages)
                .execute(context)
                .bind()
        }
    }
}
