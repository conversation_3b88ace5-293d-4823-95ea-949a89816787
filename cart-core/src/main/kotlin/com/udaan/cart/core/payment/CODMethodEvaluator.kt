package com.udaan.cart.core.payment

import com.google.inject.Inject
import com.google.inject.Injector
import com.google.inject.Singleton
import com.udaan.cart.core.domain.models.PaymentCollectionType
import com.udaan.cart.core.payment.policies.*
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap

@Singleton
class CODMethodEvaluator @Inject constructor(
    injector: Injector
) : PaymentMethodEvaluator {

    companion object {
        private val defaultCodOption = PaymentEvalResult(
            collectionType = PaymentCollectionType.COD,
            allowOtherMethods = true,
            isValidMethod = true,
            metaData = emptyMap(),
            payableAmountInPaise = 0L,
            remainingAmountInPaise = 0L,
        )
        private val logger by logger()
    }

    private val prePaymentPolicies = listOf(
        injector.getInstance(PrepaidOnlyListingPolicy::class.java),
        injector.getInstance(HighValuePaymentPolicy::class.java),
        injector.getInstance(TQCODLimitPolicy::class.java),
        injector.getInstance(MaxCODLimitPaymentPolicy::class.java),
        injector.getInstance(FraudAndAbusePolicy::class.java),
        injector.getInstance(OrgUnitBasedPrepaymentPolicy::class.java),
        injector.getInstance(CODLimitForDayPaymentPolicy::class.java),
    )

    override suspend fun evaluate(context: PaymentEvalContext): PaymentEvalResult {
        val prePaymentPolicyResults = prePaymentPolicies.parallelMap { policy ->
            policy.evaluate(context)
        }.filter { it.isApplicable && it.bps > 0 }.sortedByDescending { it.bps }
        return prePaymentPolicyResults.firstOrNull()?.let { result ->
            val collectionType = if (result.bps == HUNDRED_PERCENT_BPS.toLong()) {
                PaymentCollectionType.ADVANCE
            } else {
                PaymentCollectionType.TOKEN
            }
            PaymentEvalResult(
                collectionType = collectionType,
                allowOtherMethods = true,
                isValidMethod = true,
                metaData = emptyMap(),
                paymentBps = result.bps,
                payableAmountInPaise = result.payableAmountInPaise,
                remainingAmountInPaise = result.remainingAmountInPaise,
                userPaymentDetails = UserPaymentDetails(
                    description = result.description,
                    instruction = result.instruction
                )
            )
        } ?: defaultCodOption
    }
}
