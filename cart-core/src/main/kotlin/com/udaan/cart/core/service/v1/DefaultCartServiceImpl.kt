package com.udaan.cart.core.service.v1

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.google.inject.Inject
import com.google.inject.Injector
import com.google.inject.name.Named
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.context.CartContextKey
import com.udaan.cart.core.context.MultiCartContext
import com.udaan.cart.core.exceptions.ExceptionHandler
import com.udaan.cart.core.exceptions.NoCartFoundException
import com.udaan.cart.core.exceptions.NoContextException
import com.udaan.cart.core.operations.v1.contexts.*
import com.udaan.cart.core.operations.v1.hooks.*
import com.udaan.cart.core.response.ResponseDataBuilder
import com.udaan.cart.core.response.setPaymentMethodsContext
import com.udaan.cart.core.selectors.data.DefaultSelectorData
import com.udaan.cart.models.BaseRequestDto
import com.udaan.cart.models.BaseResponseDto
import com.udaan.cart.models.common.Requester
import com.udaan.cart.models.common.ResponseFlag
import com.udaan.cart.models.common.ResponseFlags
import com.udaan.cart.models.common.ResponseFlagsDataBuilder
import com.udaan.cart.models.default.*
import com.udaan.common.utils.kotlin.logger
import com.udaan.proto.models.ModelV1.SellingPlatform
import kotlin.time.DurationUnit
import kotlin.time.measureTimedValue

class DefaultCartServiceImpl @Inject constructor(
    injector: Injector,
    private val serviceSelectorFactory: CartServiceSelectorFactory,
    @Named("default_exception_handler")
    private val exceptionHandler: ExceptionHandler,
) : CartService<BaseRequestDto, BaseResponseDto> {
    companion object {
        private val logger by logger()
    }

    override suspend fun edit(request: BaseRequestDto): Either<String, BaseResponseDto> {
        request as EditCartReqDto
        val selectorData = DefaultSelectorData(
            buyerId = request.buyerId,
            platformId = request.platformId,
            cartSelection = request.cartSelection,
        )
        val editCartContext = EditCartContext(
            request = request,
            selectorData = selectorData,
            cartSelector = serviceSelectorFactory.getCartSelector(request.platformId),
        )
        val (editResponse, duration) = measureTimedValue {
            serviceSelectorFactory.getEditLifecycle(request).execute(
                context = editCartContext,
                hooks = EditCartHookCollection(
                    doValidationHooks = emptyList(),
                    doPostCartFetch = emptyList(),
                    doPostEditHooks = emptyList(),
                ),
            )
        }
        logger.info("Edit operation duration: ${duration.toDouble(DurationUnit.MILLISECONDS)}")

        return when (editResponse) {
            is Either.Left -> editResponse.value.message.left()
            is Either.Right -> {
                editResponse.value.getCartContext()?.let { cartContext ->
                    request.buyerOrgUnitId?.let { buyerOrgUnitId ->
                        cartContext.setValue(CartContextKey.BUYER_ORG_UNIT_ID, buyerOrgUnitId)
                    }
                    cartContext.setValue(CartContextKey.COUPON_IDS, request.couponIds)
                    buildResponse(request.responseFlags, request.platformId, cartContext)
                } ?: exceptionHandler.handle(NoContextException("Unable to produce response")).left()
            }
        }
    }

    override suspend fun delete(request: BaseRequestDto): Either<String, BaseResponseDto> {
        request as DeleteCartReqDto

        val selectorData = DefaultSelectorData(
            buyerId = request.buyerId,
            platformId = request.platformId,
            cartSelection = request.cartSelection,
            cartId = request.cartId
        )
        val deleteCartContext = DeleteCartContext(
            request = request,
            selectorData = selectorData,
            cartSelector = serviceSelectorFactory.getCartSelector(request.platformId),
        )

        val deletedCartRes = serviceSelectorFactory
            .getDeleteLifecycle(request)
            .execute(
                context = deleteCartContext,
                hooks = DeleteCartHookCollection(
                    doPostCartFetch = emptyList(),
                ),
            )
        return when (deletedCartRes) {
            is Either.Left -> deletedCartRes.value.message.left()
            is Either.Right -> Response<Any>(status = true, data = null).right()
        }
    }

    override suspend fun find(request: BaseRequestDto): Either<String, BaseResponseDto> {
        request as FetchCartReqDto

        return when (val fetchResponse = fetchCart(request)) {
            is Either.Left -> fetchResponse.value.message.left()
            is Either.Right -> {
                kotlin.runCatching {
                    fetchResponse.value.getCartContext()?.let { cartContext ->
                        request.buyerOrgUnitId?.let { buyerOrgUnitId ->
                            cartContext.setValue(CartContextKey.BUYER_ORG_UNIT_ID, buyerOrgUnitId)
                        }
                        cartContext.setValue(CartContextKey.COUPON_IDS, request.couponIds)
                        when (val response = buildResponse(request.responseFlags, request.platformId, cartContext)) {
                            is Either.Left -> response.value.left()
                            is Either.Right -> Response(status = true, data = response.value).right()
                        }
                    } ?: Response(status = true, data = null).right()
                }.getOrElse {
                    when (it) {
                        is NoCartFoundException -> Response(status = true, data = null).right()
                        else -> {
                            (it.message ?: "Failed to build cart response").left()
                        }
                    }
                }
            }
        }
    }

    override suspend fun findAll(request: BaseRequestDto): Either<String, BaseResponseDto> {
        request as FetchCartReqDto

        return when (val fetchResponse = fetchCarts(request)) {
            is Either.Left -> fetchResponse.value.message.left()
            is Either.Right -> {
                kotlin.runCatching {
                    fetchResponse.value.getMultiCartContext()?.let { cartsContext ->
                        when (val response = buildMultiResponse(request.platformId, cartsContext)) {
                            is Either.Left -> response.value.left()
                            is Either.Right -> Response(status = true, data = response.value).right()
                        }
                    } ?: Response(status = true, data = null).right()
                }.getOrElse {
                    when (it) {
                        is NoCartFoundException -> Response(status = true, data = null).right()
                        else -> {
                            (it.message ?: "Failed to build cart response").left()
                        }
                    }
                }
            }
        }
    }

    override suspend fun fetchDeliverySlots(request: BaseRequestDto): Either<String, BaseResponseDto> {
        request as FetchCartReqDto
        return when (val fetchResponse = fetchCart(request)) {
            is Either.Left -> fetchResponse.value.message.left()
            is Either.Right -> {
                kotlin.runCatching {
                    fetchResponse.value.getCartContext()?.let { cartContext ->
                        val responseBuilder = serviceSelectorFactory.getDeliverySlotBuilder(request.platformId)
                        when (val response = responseBuilder.build(cartContext, request.buyerOrgUnitId)) {
                            is Either.Left -> response.value.left()
                            is Either.Right -> {
                                logger.info("Delivery slots res: ${response.value}")
                                Response(status = true, data = response.value).right()
                            }
                        }
                    } ?: Response(status = true, data = null).right()
                }.getOrElse {
                    when (it) {
                        is NoCartFoundException -> Response(status = true, data = null).right()
                        else -> {
                            it.printStackTrace()
                            (it.message ?: "Failed to build delivery slots response").left()
                        }
                    }
                }
            }
        }
    }

    override suspend fun fetchPaymentMethods(request: BaseRequestDto): Either<String, BaseResponseDto> {
        request as PaymentMethodsReqDto
        val paymentsLifecycle = serviceSelectorFactory.getPaymentMethodsLifecycle(request)
        val selectorData = DefaultSelectorData(
            buyerId = request.buyerId,
            platformId = request.platformId,
            cartSelection = request.cartSelection,
            cartId = request.cartId,
        )
        val paymentMethodsContext = PaymentMethodsContext(
            request = request,
            selectorData = selectorData,
            cartSelector = serviceSelectorFactory.getCartSelector(request.platformId),
        )
        val paymentRes = paymentsLifecycle.execute(
            context = paymentMethodsContext,
            hooks = FetchPaymentMethodsHookCollection(
                doValidationHooks = emptyList(),
                postCartFetchHooks = emptyList(),
                postPaymentMethods = emptyList(),
                preFindPaymentMethodsHooks = emptyList()
            )
        )
        return when (paymentRes) {
            is Either.Left -> paymentRes.value.message.left()
            is Either.Right -> {
                paymentRes.value.getCartContext()?.let { cartContext ->
                    val responseBuilder = serviceSelectorFactory.getPaymentMethodsResponseBuilder(request.platformId)
                    val response = responseBuilder.build(
                        ResponseDataBuilder.create().setPaymentMethodsContext(paymentRes.value),
                        responseFlags = ResponseFlagsDataBuilder.emptyFlags(),
                        cartContext = cartContext,
                        buyerOrgUnitId = request.buyerOrgUnitId,
                    )
                    logger.info("Payments response: $response")
                    when (response) {
                        is Either.Left -> response.value.left()
                        is Either.Right -> Response(status = true, data = response.value).right()
                    }
                } ?: Response(status = true, data = null).right()
            }
        }
    }

    override suspend fun checkout(request: BaseRequestDto): Either<String, BaseResponseDto> {
        request as CheckoutRequestDto
        logger.info("Checkout lifecycle request: $request")
        val checkoutLifecycle = serviceSelectorFactory.getCheckoutLifecycle(request)
        val selectorData = DefaultSelectorData(
            buyerId = request.buyerId,
            platformId = request.platformId,
            cartSelection = request.cartSelection,
            cartId = request.cartId,
        )
        val checkoutContext = CheckoutCartContext(
            request = request,
            selectorData = selectorData,
            cartSelector = serviceSelectorFactory.getCartSelector(request.platformId),
        )
        val checkoutRes = checkoutLifecycle.execute(
            checkoutContext, CheckoutCartHookCollection(
                doPostCartFetch = emptyList()
            )
        )
        return when (checkoutRes) {
            is Either.Left -> checkoutRes.value.message.left()
            is Either.Right -> {
                logger.info("Checkout lifecycle response: ${checkoutRes.value}")
                val responseBuilder = serviceSelectorFactory.getCheckoutResponseBuilder(request.platformId)
                val response = responseBuilder.build(
                    checkoutRes.value
                )
                logger.info("Checkout response: $response")
                when (response) {
                    is Either.Left -> response.value.left()
                    is Either.Right -> Response(status = true, data = response.value).right()
                }
            }
        }
    }

    private suspend fun fetchCart(
        request: FetchCartReqDto
    ): Either<OpError, FetchCartContext<FetchCartReqDto>> {
        val selectorData = DefaultSelectorData(
            buyerId = request.buyerId,
            platformId = request.platformId,
            cartSelection = request.cartSelection,
            cartId = request.cartId,
        )

        val fetchContext = FetchCartContext(
            request = request,
            selectorData = selectorData,
            cartSelector = serviceSelectorFactory.getCartSelector(request.platformId),
        )

        return serviceSelectorFactory.getFetchLifecycle(request).execute(
            fetchContext,
            FetchCartHookCollection(
                postCartFetchHooks = emptyList(),
            )
        )
    }

    private suspend fun fetchCarts(
        request: FetchCartReqDto
    ): Either<OpError, FetchCartsContext<FetchCartReqDto>> {
        val selectorData = DefaultSelectorData(
            buyerId = request.buyerId,
            platformId = request.platformId,
            cartSelection = request.cartSelection,
        )

        val fetchContext = FetchCartsContext(
            request = request,
            selectorData = selectorData,
            cartSelector = serviceSelectorFactory.getMultiCartSelector(request.platformId),
        )

        return serviceSelectorFactory.getFetchMultipleLifecycle(request).execute(fetchContext)
    }

    private suspend fun buildResponse(
        responseFlags: ResponseFlags,
        platformId: SellingPlatform,
        cartContext: CartContext,
    ): Either<String, BaseResponseDto> {
        return serviceSelectorFactory.getResponseBuilder(platformId)
            .build(
                responseBuilderData = ResponseDataBuilder.create(),
                responseFlags = responseFlags,
                cartContext = cartContext,
                buyerOrgUnitId = cartContext.getValue<String>(CartContextKey.BUYER_ORG_UNIT_ID)
            )
    }

    private suspend fun buildMultiResponse(
        platformId: SellingPlatform,
        cartContext: MultiCartContext,
    ): Either<String, BaseResponseDto> {
        return serviceSelectorFactory.getMultiCartResponseBuilder(platformId)
            .build(
                responseBuilderData = ResponseDataBuilder.create(),
                responseFlags = ResponseFlagsDataBuilder.emptyFlags(),
                cartContext = cartContext,
                buyerOrgUnitId = cartContext.getCartContexts().firstOrNull()
                    ?.getValue<String>(CartContextKey.BUYER_ORG_UNIT_ID),
            )
    }

    override suspend fun updateCreditLine(request: BaseRequestDto): Either<String, BaseResponseDto> {
        request as UpdateCreditLineReqDto
        val selectorData = DefaultSelectorData(
            buyerId = request.buyerId,
            platformId = request.platformId,
            cartSelection = request.cartSelection
        )
        val updateCreditLineContext = UpdateCreditLineContext(
            request = request,
            selectorData = selectorData,
            cartSelector = serviceSelectorFactory.getMultiCartSelector(request.platformId),
        )

        logger.info("#### selectorData = $selectorData")
        val response = serviceSelectorFactory.getUpdateCreditLineLifecycle(request).execute(updateCreditLineContext)
        logger.info("#### response = $response")

        return when (response) {
            is Either.Left -> response.value.message.left()
            is Either.Right -> Response(status = true, data = null).right()
        }
    }
}
