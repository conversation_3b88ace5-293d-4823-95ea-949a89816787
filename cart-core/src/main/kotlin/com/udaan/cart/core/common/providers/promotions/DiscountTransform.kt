package com.udaan.cart.core.common.providers.promotions

import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.context.CartContextKey
import com.udaan.cart.core.domain.models.CartItem
import com.udaan.common.utils.kotlin.logger
import com.udaan.promotions.dto.promotion.RewardClass
import com.udaan.promotions.dto.v2.promotion.PromotionDetails
import com.udaan.promotions.dto.v2.promotion.PromotionLevel
import com.udaan.promotions.dto.v2.promotion.PromotionStatus
import com.udaan.promotions.dto.v2.promotion.getDiscountDetails

internal class DiscountTransform : Transform {
    companion object {
        private val logger by logger()
    }

    override suspend fun apply(
        promoContext: PromoContext,
        cartContext: CartContext
    ): PromoContext {
        return cartContext.getDetailedCart(
            orgUnitId = cartContext.getValue(CartContextKey.BUYER_ORG_UNIT_ID),
            skipOOSItems = true
        )?.let { detailedCart ->
            val cart = detailedCart.cart
            val promosResponse = promoContext.promosResponse
            val itemLevelPromos = promosResponse.promotions.filter {
                it.status == PromotionStatus.APPLIED &&
                        it.level == PromotionLevel.CART &&
                        it.rewardDetails?.rewardClass == RewardClass.DISCOUNT
            }
            val cartPromoDetails = promosResponse.promotions.filter {
                it.status == PromotionStatus.APPLIED &&
                        it.level == PromotionLevel.ITEM &&
                        it.rewardDetails?.rewardClass == RewardClass.DISCOUNT
            }
            val discountedInfo = promoContext.getTransformedItems().map { item ->
                // apply item level discounts
                applyPromo(
                    item = item,
                    promoDetails = itemLevelPromos,
                )
            }.map { (item, discountsMap) ->
                // apply cart level discounts
                val (updatedItem, newDiscountMap) = applyPromo(
                    item = item,
                    promoDetails = cartPromoDetails,
                )
                Triple(item.id, updatedItem, newDiscountMap.plus(discountsMap))
            }
            val discountedItems = discountedInfo.map { it.second }
            val itemToDiscountMap = discountedInfo.associate {
                it.first to it.third
            }
            promoContext.updateTransformedItems(discountedItems)
            promoContext.updateItemToDiscountMap(itemToDiscountMap)
            promoContext
        } ?: promoContext
    }

    private fun applyPromo(
        item: CartItem,
        promoDetails: List<PromotionDetails>,
    ): Pair<CartItem, Map<String, DiscountDetails>> {
        val discountMap = mutableMapOf<String, DiscountDetails>()
        val perUnitSpPaise = promoDetails.fold(item.perUnitAmountInPaise) { amount, details ->
            val isEligible = details.applicableLineIds.contains(item.id)
            if (isEligible) {
                val discountDetails = details.getDiscountDetails()?.bpsInDouble?.let { discountBps ->
                    val amount = item.perUnitAmountInPaise.times(discountBps).div(10000).toLong()
                    DiscountDetails(
                        discountBps = discountBps,
                        discountInPaise = amount
                    )
                } ?: DiscountDetails(0.0, 0L)
                if (discountDetails.discountInPaise > 0) {
                    discountMap[details.id] = discountDetails
                }
                amount - discountDetails.discountInPaise
            } else amount
        }.toLong()

        return item.copy(perUnitAmountInPaise = perUnitSpPaise) to discountMap
    }
}
