package com.udaan.cart.core.metrics

import com.google.inject.Inject
import com.udaan.instrumentation.MetricsManager
import com.udaan.instrumentation.Telemetry
import com.udaan.instrumentation.TelemetryScope

interface EventTracker {
    suspend fun trackEvent(
        name: String,
        properties: Map<String, String>,
        measures: Map<String, Double>
    )

    suspend fun incrementCounter(
        name: String,
        labels: Map<String, String>
    )
}

class EventTrackerImpl @Inject constructor() : EventTracker {
    override suspend fun trackEvent(
        name: String,
        properties: Map<String, String>,
        measures: Map<String, Double>
    ) {
        Telemetry.trackEvent(
            name,
            properties,
            measures
        )
    }

    override suspend fun incrementCounter(
        name: String,
        labels: Map<String, String>
    ) {
        TelemetryScope.launch {
            MetricsManager.incrementCounter(
                name,
                *labels.map { it.key to it.value }.toTypedArray()
            )
        }
    }
}
