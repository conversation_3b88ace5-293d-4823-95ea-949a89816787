package com.udaan.cart.core.operations.v1.lifecycles.impl

import arrow.core.*
import com.google.inject.Inject
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.CartContextFactory
import com.udaan.cart.core.exceptions.NoCartFoundException
import com.udaan.cart.core.operations.v1.contexts.FetchCartContext
import com.udaan.cart.core.operations.v1.hooks.FetchCartHookCollection
import com.udaan.cart.core.operations.v1.lifecycles.AbstractFetchCartLifecycle
import com.udaan.cart.models.default.FetchCartReqDto
import com.udaan.common.utils.kotlin.logger

class DefaultFetchCartLifecycle @Inject constructor(
    private val cartContextFactory: CartContextFactory,
): AbstractFetchCartLifecycle<FetchCartReqDto>() {
    companion object {
        private val logger by logger()
    }

    override suspend fun setupHooks(
        hooks: FetchCartHookCollection<OpError, FetchCartContext<FetchCartReqDto>>,
        context: FetchCartContext<FetchCartReqDto>
    ): FetchCartHookCollection<OpError, FetchCartContext<FetchCartReqDto>> {
        return hooks
    }

    override suspend fun doFetch(
        context: FetchCartContext<FetchCartReqDto>
    ): Either<OpError, FetchCartContext<FetchCartReqDto>> {
        return kotlin.runCatching {
            val cart = context.cartSelector.find(context.selectorData)
            if (cart != null) {
                val cartContext = cartContextFactory.createCartContext(cart.id, cart)
                context.setCartContext(cartContext)
            } else {
                if (context.request.cartId.isNullOrBlank().not()) {
                    throw NoCartFoundException("[Fetch cart] No active cart exists for cartId - " +
                        "${context.request.cartId}")
                }
            }
            context.right()
        }.getOrElse { ex ->
            logger.error("Failed to fetch cart", ex)
            OpError(message = "Failed to fetch cart").left()
        }
    }
}