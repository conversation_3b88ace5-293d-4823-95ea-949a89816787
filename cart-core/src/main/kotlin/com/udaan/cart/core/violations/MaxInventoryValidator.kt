package com.udaan.cart.core.violations

import arrow.core.*
import com.google.inject.Inject
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.context.CartContextKey
import com.udaan.cart.core.domain.models.DetailedCart
import com.udaan.cart.core.domain.models.ItemSummary
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.common.utils.kotlin.logger
import com.udaan.orchestrator.models.maxQuantity.MeasureUnit
import com.udaan.orderform.common.models.ListingItemData
import com.udaan.orderform.common.providers.InventoryProvider

class MaxInventoryValidator @Inject constructor(
    private val inventoryProvider: InventoryProvider
) : ViolationValidator {

  companion object {
    private val logger by logger()
  }

  override suspend fun validate(
      cartContext: CartContext,
      detailedCart: DetailedCart?
  ): Either<NonEmptyList<String>, List<ViolationData>> {
    return kotlin.runCatching {
      val finalCart = detailedCart?.cart ?: cartContext.getCart()
      (finalCart?.let { cart ->
        val buyerOrgUnit = cartContext.getBuyerOrgUnit(cartContext.getValue(CartContextKey.BUYER_ORG_UNIT_ID))
        val availableItems = cartContext.getAvailableItems(
            existingCart = cart,
            buyerOrgUnitId = buyerOrgUnit?.orgUnitId,
            forceFetch = false
        )
        if (availableItems.isNotEmpty()) {
          buyerOrgUnit?.let {
            val listingItemsData = cart.items.map { item ->
              item.product as ListingProductItem
              ListingItemData(
                  listingId = item.product.listingId,
                  salesUnitId = item.product.salesUnitId,
                  quantity = item.quantity,
                  itemId = item.id
              )
            }
            val groupInventory = inventoryProvider.fetchMaxAllowedInventory(
                buyerOrgUnitId = buyerOrgUnit.orgUnitId,
                deliveryPincode = buyerOrgUnit.unitAddress.pincode,
                listingItemsData = listingItemsData,
                platform = cart.platformId,
            )
            groupInventory.itemsInventory.filter { !it.hasValidQuantity }
                .mapNotNull { maxItemInventory ->
                  when (maxItemInventory.measureUnit) {
                    MeasureUnit.UNITS -> {
                      maxItemInventory.listingItemsData.firstOrNull()?.let { listingItemData ->
                        MaxUnitInventoryData(
                            itemSummary = ItemSummary(
                                itemId = listingItemData.itemId ?: "",
                                listingId = listingItemData.listingId,
                                salesUnitId = listingItemData.salesUnitId,
                            ),
                            maxUnits = maxItemInventory.maxAllowedQuantity.toInt(),
                        )
                      }
                    }

                    MeasureUnit.WEIGHT_KG -> {
                      MaxGroupInventoryData(
                          items = maxItemInventory.listingItemsData.map { listingItemData ->
                            ItemSummary(
                                itemId = listingItemData.itemId ?: "",
                                listingId = listingItemData.listingId,
                                salesUnitId = listingItemData.salesUnitId,
                            )
                          },
                          maxUnits = maxItemInventory.maxAllowedQuantity,
                          measureUnit = maxItemInventory.measureUnit,
                          groupType = maxItemInventory.groupType,
                      )
                    }
                    else -> null
                  }
                }
          }
        } else emptyList()
      } ?: emptyList()).right()
    }.getOrElse { e ->
      logger.error("Failed to check max quantity eligible: ${e.message}", e)
      nonEmptyListOf("Unable to validate max units").left()
    }
  }
}
