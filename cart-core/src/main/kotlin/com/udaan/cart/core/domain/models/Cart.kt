package com.udaan.cart.core.domain.models

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.cart.models.common.*
import com.udaan.catalog.client.CategoryGroupV2
import com.udaan.common.utils.idgen.UUIDUtils
import com.udaan.proto.models.ModelV1
import java.time.Instant
import java.util.*

enum class CreationStrategy {
    CART_CREATE_UNIQUE,
    CART_CREATE_DUPLICATE,
}

enum class CartState {
    ACTIVE,
    ORDER_IN_PROGRESS,
    ORDER_PLACED,
    ARCHIVED
}

enum class CartType {
    FORWARD // represents order's forward
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class CartOrderInfo(val orderIds: List<String>)

@JsonIgnoreProperties(ignoreUnknown = true)
data class CartItemOrderInfo(
    val orderId: String,
    val orderLineId: String
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ItemPUInfo(
    val userPU: String
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ItemPriceDetails(
    val unitPrice: Long,
    val puInfo: ItemPUInfo? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ItemProperties(
    val inventoryAvailable: Boolean = true
)

data class Cart(
    val id: String = generateId(),
    val type: CartType,
    val buyerId: String,
    val platformId: ModelV1.SellingPlatform,
    val selection: CartSelection,
    val items: List<CartItem>,
    val state: CartState,
    val currentActive: Boolean = true,
    val orderInfo: CartOrderInfo? = null,
    val createdAt: Date = Date.from(Instant.now()),
    val updatedAt: Date = Date.from(Instant.now()),
) {
    companion object {
        private fun generateId(): String = UUIDUtils.uuidBase32("CT")
        private val CommonPacmanCartKeys = listOf("horeca")
    }

    fun getCategoryGroupId(): String {
        return when (selection) {
            is CategoryGroupSelection -> selection.categoryGroupId
            is CategoryGroupSellerSelection -> selection.categoryGroupId
            is CategoryGroupCategorySelection -> selection.categoryGroupId
            is CategoryGroupOrgUnitSelection -> selection.categoryGroupId
            is CategoryGroupBrandSelection -> selection.categoryGroupId
            is CategoryGroupKeyBasedSelection -> selection.categoryGroupId
            is KeyBasedSelection -> {
                if (CommonPacmanCartKeys.contains(selection.key)) CategoryGroupV2.FoodAndFMCG.id
                else this.getCategoryGroupFromItem()
            }

            else -> this.getCategoryGroupFromItem()
        }
    }

    private fun Cart.getCategoryGroupFromItem(): String {
        return this.items.map { cartItem ->
            cartItem.product as ListingProductItem
            cartItem.product.categoryGroupId
        }.toSet().firstOrNull() ?: ""
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class CartItem(
    val id: String = generateId(),
    val cartId: String,
    val quantity: Int,
    val product: ProductItem,
    val perUnitAmountInPaise: Long,
    val priceDetails: ItemPriceDetails,
    val properties: ItemProperties,
    val currentActive: Boolean = true,
    val orderInfo: CartItemOrderInfo? = null,
    val createdAt: Date = Date.from(Instant.now()),
    val updatedAt: Date = Date.from(Instant.now()),
) {
    companion object {
        private fun generateId(): String = UUIDUtils.uuidBase32("CI")
    }

    fun isFreebie(): Boolean = (this.product as ListingProductItem).freebieInfo != null
}

data class DetailedCart(
    val cart: Cart,
    val leviesMap: Map<String, List<ItemLevy>>,
    val prePromoCart: Cart? = null,
    val prePromoLeviesMap: Map<String, List<ItemLevy>> = emptyMap(),
    val itemsWithInvalidPrice: List<CartItem> = emptyList(),
    val listingIdToPricingAuditIds: Map<String, String> = emptyMap()
) {

    fun totalAmount(): Long {
        return cart.items.sumOf { item ->
            val levies = leviesMap[item.id] ?: emptyList()
            item.perUnitAmountInPaise * item.quantity + levies.sumOf { it.levyAmountPaise }
        }
    }

    fun totalAmountForItems(cartItems: List<CartItem>): Long {
        return cartItems.sumOf { cartItem ->
            getItemTotalWithTax(cartItem)
        }
    }

    fun getItemTotalWithTax(item: CartItem): Long =
        (leviesMap[item.id]?.sumOf { it.levyAmountPaise } ?: 0) + item.totalAmountInPaise

    fun getPrePromoItemTotalWithTax(item: CartItem): Long =
        (prePromoLeviesMap[item.id]?.sumOf { it.levyAmountPaise } ?: 0) + item.totalAmountInPaise

    fun getItemTax(item: CartItem): Long = leviesMap[item.id]?.sumOf { it.levyAmountPaise } ?: 0

    fun getPrePromoItemTax(item: CartItem): Long = prePromoLeviesMap[item.id]?.sumOf { it.levyAmountPaise } ?: 0

    fun getCategoryGroupId(): String = cart.getCategoryGroupId()

    fun getCartItems(skipItemsWithPriceIssues: Boolean = false): List<CartItem> {
        return if (skipItemsWithPriceIssues) {
            val cartItemIdsWithInvalidPrice = itemsWithInvalidPrice.map { it.id }
                .plus(
                    cart.items.filter {
                        val isNotFreebie = (it.product as ListingProductItem).freebieInfo == null
                        val isNegativePrice = it.perUnitAmountInPaise < 0
                        val isZeroPrice = it.perUnitAmountInPaise == 0L
                        isNegativePrice || (isNotFreebie && isZeroPrice)
                    }.map { it.id }
                )
            cart.items.filterNot { it.id in cartItemIdsWithInvalidPrice }
        } else {
            cart.items
        }
    }
}

data class ItemSummary(
    val itemId: String,
    val listingId: String,
    val salesUnitId: String,
)

val CartItem.totalAmountInPaise: Long
    get() = this.perUnitAmountInPaise * this.quantity
