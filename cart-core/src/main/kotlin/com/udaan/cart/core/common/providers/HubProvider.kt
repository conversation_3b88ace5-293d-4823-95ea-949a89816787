package com.udaan.cart.core.common.providers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.catalog.client.helpers.Category
import com.udaan.common.utils.kotlin.logger
import com.udaan.constraint.models.ExternalException
import com.udaan.proto.models.ModelV1
import com.udaan.scnetwork.client.userFacilityEdge.BuyerHubMappingCacheClient
import com.udaan.scnetwork.models.category.NetworkCategory
import kotlinx.coroutines.future.await
import kotlin.time.measureTimedValue

interface HubProvider {
  suspend fun getHubOrgUnitIdForBuyer(
      category: String,
      platform: ModelV1.SellingPlatform,
      pincode: String,
      buyerOrgUnitId: String?
  ): String?
}

@Singleton
class HubProviderImpl @Inject constructor(
    private val buyerHubMappingClient: BuyerHubMappingCacheClient
): HubProvider {
  companion object {
    private val logger by logger()
  }

  override suspend fun getHubOrgUnitIdForBuyer(
      category: String,
      platform: ModelV1.SellingPlatform,
      pincode: String,
      buyerOrgUnitId: String?
  ): String? {
    return kotlin.runCatching {
      val networkCategory = when (category) {
        Category.Fresh.name.lowercase() -> NetworkCategory.FRESH
        Category.Meat.name.lowercase() -> NetworkCategory.MEAT
        Category.Pharma.name.lowercase() -> NetworkCategory.PHARMA
        else -> NetworkCategory.FOOD
      }

      val (response, duration) = measureTimedValue {
        buyerHubMappingClient.getHubForBuyer(networkCategory, platform, pincode, buyerOrgUnitId).await()
      }
      val hubOrgUnitId = response?.facility?.orgUnitId
      logger.info("[HubProvider] For network category $networkCategory platform ${platform.name} " +
          "pincode $pincode and BuyerOrgUnitId $buyerOrgUnitId hubOrgUnit = $hubOrgUnitId  Duration : $duration")
      hubOrgUnitId
    }.getOrElse { exception ->
      logger.error("[HubProvider] Exception while fetching getHubForOrgUnit " +
          "for category $category platform ${platform.name} pincode $pincode & buyerOrgUnitId " +
          "$buyerOrgUnitId : $exception")
      throw ExternalException()
    }
  }

}
