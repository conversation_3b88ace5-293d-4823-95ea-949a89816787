package com.udaan.cart.core.operations.v1.hooks.impl

import arrow.core.Either
import arrow.core.right
import com.google.inject.Singleton
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.cart.core.hooks.BaseOperationHook
import com.udaan.cart.core.operations.v1.contexts.EditCartContext
import com.udaan.cart.core.operations.v1.lifecycles.impl.EditCartItemsHolder
import com.udaan.cart.models.default.EditCartReqDto
import com.udaan.common.utils.kotlin.logger

// This Hook is supposed to handle cases if the parent listing is deleted from the cart, the associated freebie
// listing also needs to be removed from the cart
@Singleton
class FreebieDeletionHook : BaseOperationHook<OpError, EditCartContext<EditCartReqDto>> {

    companion object {
        private val logger by logger()
    }

    override suspend fun execute(
        context: EditCartContext<EditCartReqDto>
    ): Either<OpError, EditCartContext<EditCartReqDto>> {
        val deletedItems = context.getEditedItems()?.discardedItems ?: emptyList()
        val cartItems = context.getCartContext()?.getCart()?.items ?: emptyList()

        //if there are no deleted items or if cart is empty return back
        if (deletedItems.isEmpty() || cartItems.isEmpty()) {
            return context.right()
        }
        val parentFreebieItemIdMap = fetchParentItemIdFreebieItemIdMap(cartItems)
        val parentCartItemIds = parentFreebieItemIdMap.keys
        val deletedCartItemIds = deletedItems.map { it.id }
        val parentDeletedItemIds = parentCartItemIds.intersect(deletedCartItemIds)

        // If none of the deleted items are parent Ids, return without any changes
        if (parentDeletedItemIds.isEmpty()) {
            return context.right()
        }
        val initial = context.getEditedItems()!!
        val editedCartInfo = parentDeletedItemIds.fold(initial) { currentState, parentCartItemId ->
            val freebieItemId = parentFreebieItemIdMap.get(parentCartItemId)
            val freebieItem = cartItems.first { it.id == freebieItemId }
            currentState.addDiscardedItem(freebieItem)
        }
        context.setEditItemHolder(
            EditCartItemsHolder(
                newItems = editedCartInfo.newItems,
                editedItems = editedCartInfo.editedItems,
                discardedItems = editedCartInfo.discardedItems
            )
        )
        return context.right()
    }

    private fun fetchParentItemIdFreebieItemIdMap(
        cartItems: List<com.udaan.cart.core.domain.models.CartItem>
    ): Map<String, String> {
        val freebieItems = cartItems.filter { cartItem ->
            cartItem.isFreebie()
        }
        return freebieItems.map { freebieItem ->
            freebieItem.product as ListingProductItem
            val parentCartItemId = freebieItem.product.freebieInfo?.parentCartLineId!!
            parentCartItemId to freebieItem.id
        }.toMap()
    }
}
