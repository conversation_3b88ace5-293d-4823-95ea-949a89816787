package com.udaan.cart.core.utils

import java.time.Instant
import java.util.*

object IdUtils {
    private val random = Random(Instant.now().epochSecond)

    private fun getUUID(): String {
        return UUID.randomUUID().toString()
    }

    fun getCartId(): String {
        val uuid = getUUID().replace("-".toRegex(), "")
        return "CT$uuid"
    }

    fun getCartItemId(): String {
        val uuid = getUUID().replace("-".toRegex(), "")
        return "CI$uuid"
    }
}
