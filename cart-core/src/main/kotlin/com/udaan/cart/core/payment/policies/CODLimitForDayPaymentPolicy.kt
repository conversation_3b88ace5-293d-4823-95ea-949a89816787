package com.udaan.cart.core.payment.policies

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.common.providers.OrderReadProvider
import com.udaan.cart.core.metrics.CartEventTracker
import com.udaan.cart.core.payment.PaymentEvalContext
import com.udaan.cart.core.utils.DropslotOrderFetcher
import com.udaan.catalog.client.CategoryGroupV2
import com.udaan.catalog.client.helpers.Category
import com.udaan.common.utils.parallelMap
import com.udaan.listing.builder.enrichments.catalog.CategoryConfigHelper

@Singleton
class CODLimitForDayPaymentPolicy @Inject constructor(
    private val policyExemptionValidator: PolicyExemptionValidator,
    private val categoryConfigHelper: CategoryConfigHelper,
    private val dropslotOrderFetcher: DropslotOrderFetcher,
    private val orderReadProvider: OrderReadProvider,
    private val eventTracker: CartEventTracker,
) : AbstractPrePaymentPolicy() {

    companion object {
        private val POLICY_NAME = this::class.java.simpleName
    }

    override fun getName(): String = POLICY_NAME

    override suspend fun evaluate(paymentContext: PaymentEvalContext): PrePaymentResult {
        val buyerId = paymentContext.detailedCart.cart.buyerId
        return if (policyExemptionValidator.isExempted(POLICY_NAME, buyerId)) {
            defaultResult()
        } else {
            prepaymentEvaluation(paymentContext)
        }
    }

    private suspend fun prepaymentEvaluation(paymentContext: PaymentEvalContext): PrePaymentResult {
        val categoryGroup = paymentContext.detailedCart.getCategoryGroupId()
        return if (categoryGroup == CategoryGroupV2.FoodAndFMCG.id) {
            prepaymentEvaluationForPacman(paymentContext)
        } else {
            defaultResult()
        }
    }

    private suspend fun prepaymentEvaluationForPacman(paymentContext: PaymentEvalContext): PrePaymentResult {
        val activeOrderIds = dropslotOrderFetcher.fetchActiveOrdersInDropSlot(
            buyerId = paymentContext.detailedCart.cart.buyerId,
            buyerOrgUnitId = paymentContext.buyerOrgUnit?.orgUnitId ?: getHeadOrgUnitId(paymentContext),
            listingsMap = paymentContext.cartContext.getListingsMap()
        )
        if (activeOrderIds.isEmpty()) {
            return defaultResult()
        }
        val codOrdersWithAmountMap = orderReadProvider.getCodOrdersWithOrderValue(activeOrderIds)
        val codAmountOfAlreadyPlacedOrders = codOrdersWithAmountMap.values.sum()
        val currentOrderAmount = paymentContext.detailedCart.totalAmount()
        return MaxCollectibleUtil.evaluateCollectibleInfo(currentOrderAmount, codAmountOfAlreadyPlacedOrders)
            ?.let { collectibleInfo ->
                eventTracker.trackBuyerCodOrderAboveLimit(
                    buyerId = paymentContext.detailedCart.cart.buyerId,
                    cartId = paymentContext.cartContext.getCartId(),
                    cartValue = currentOrderAmount,
                    totalCodValue = codAmountOfAlreadyPlacedOrders,
                    existingOrders = codOrdersWithAmountMap.keys.toList()
                )
                prepareResult(
                    bps = collectibleInfo.bps,
                    description = null,
                    instruction = collectibleInfo.message,
                    totalAmount = currentOrderAmount
                )
            } ?: defaultResult()
    }

    private fun getHeadOrgUnitId(paymentContext: PaymentEvalContext): String? {
        return paymentContext.buyerOrg?.orgAccount?.headOfficeOrgUnitRef
    }
}