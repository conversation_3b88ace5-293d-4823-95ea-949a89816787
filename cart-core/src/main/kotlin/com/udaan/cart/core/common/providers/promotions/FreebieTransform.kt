package com.udaan.cart.core.common.providers.promotions

import com.google.inject.Inject
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.context.CartContextKey
import com.udaan.cart.core.domain.CartItemBuilder
import com.udaan.cart.core.domain.models.CartItem
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.cart.models.common.FreebieInfo
import com.udaan.catalog.client.CategoryGroupV2
import com.udaan.common.utils.kotlin.logger
import com.udaan.orderform.common.providers.CatalogProvider
import com.udaan.promotions.dto.promotion.RewardClass
import com.udaan.promotions.dto.v2.promotion.PromotionDetails
import com.udaan.promotions.dto.v2.promotion.PromotionLevel
import com.udaan.promotions.dto.v2.promotion.PromotionStatus

class FreebieTransform @Inject constructor(
    private val catalogProvider: CatalogProvider,
    private val cartItemBuilder: CartItemBuilder
): Transform {

    companion object {
        private val logger by logger()
    }
    override suspend fun apply(
        promoContext: PromoContext,
        cartContext: CartContext
    ): PromoContext {
        val promosResponse = promoContext.promosResponse
        val itemLevelFreebiePromos = promosResponse.promotions.filter {
            it.status == PromotionStatus.APPLIED &&
            it.level == PromotionLevel.ITEM &&
            it.rewardDetails?.rewardClass == RewardClass.FREEBIE
        }
        val existingNonFreebieTransformedItems = promoContext.getTransformedItems()
            .filterNot { it.isFreebie() }
        val existingFreebieItems = cartContext.getCart()?.items?.filter { it.isFreebie() } ?: emptyList()
        val freebieCartItems = existingNonFreebieTransformedItems.mapNotNull { item ->
            val (cartItem, freebiePromotion) = getFreebieCartItemToPromoMap(
                item = item,
                promoDetails = itemLevelFreebiePromos,
                existingFreebieItems = existingFreebieItems,
                categoryGroupId = promoContext.getCategoryGroupId()
            )
            cartItem ?: return@mapNotNull null
            freebiePromotion ?: return@mapNotNull null
            promoContext.addFreebieCartLineToPromoMap(cartItem.id to freebiePromotion)
            cartItem
        }
        promoContext.updateTransformedItems(existingNonFreebieTransformedItems + freebieCartItems)
        return promoContext
    }

    @Suppress("ReturnCount", "ThrowsCount")
    private suspend fun getFreebieCartItemToPromoMap(
        item: CartItem,
        promoDetails: List<PromotionDetails>,
        existingFreebieItems: List<CartItem>,
        categoryGroupId: String?
    ): Pair<CartItem?, PromotionDetails?> {
        return try {
            val applicablePromoDetails = promoDetails.filter { it.applicableLineIds.contains(item.id) }
                .takeIf { it.isNotEmpty() } ?: return null to null
            // checking eligibility and selecting first only if 1 freebie promo is applied
            val freebiePromoDetail = applicablePromoDetails.singleOrNull()
                ?: throw IllegalArgumentException(
                    "FreebieTransform::Multiple freebie promos::${applicablePromoDetails.joinToString { it.id }}"
                )
            // validating freebie quantity in promo detail
            val freebieDetail = freebiePromoDetail.getFreebieDetails()?.takeIf { it.freebieQuantity != 0 }
                ?: throw IllegalArgumentException(
                    "FreebieTransform::0 freebie quantity::PromoId ${freebiePromoDetail.id}, CartId ${item.id}"
                )

            val freebieListingInfo = catalogProvider.getListing(freebieDetail.listingId)
            val matchedExistingFreebieItem = existingFreebieItems.singleOrNull {
                (it.product as ListingProductItem).listingId == freebieListingInfo.listingId
            }

            // validate salesUnitId from freebieDetails and listing's catalog
            val freebieSalesUnitId = freebieListingInfo.salesUnitList.firstOrNull {
                it.salesUnitId == freebieDetail.salesUnitId
            }?.salesUnitId
                ?: throw IllegalArgumentException(
                    "FreebieTransform::Mismatch in SalesUnitIds::Freebie SalesUnitId-${freebieDetail.salesUnitId} " +
                    "Freebie ListingId-${freebieDetail.listingId}"
                )

            if (matchedExistingFreebieItem == null) logger.info("FreebieTransform::new freebie::$freebieDetail")
            else logger.info(
                "FreebieTransform::updating freebie quantity to ${freebieDetail.freebieQuantity}::" +
                "$matchedExistingFreebieItem"
            )

            val freebieCartItem = matchedExistingFreebieItem?.copy(
                quantity = freebieDetail.freebieQuantity,
                product = (matchedExistingFreebieItem.product as ListingProductItem)
                    .copy(quantity = freebieDetail.freebieQuantity, freebieInfo = FreebieInfo(item.id)),
                perUnitAmountInPaise = freebieDetail.billAmountInPaise,
                priceDetails = matchedExistingFreebieItem.priceDetails.copy(unitPrice = freebieDetail.billAmountInPaise)
            ) ?: cartItemBuilder.build(
                item.cartId,
                freebieDetail.freebieQuantity,
                ListingProductItem(
                    sellerId = freebieListingInfo.orgId,
                    listingId = freebieListingInfo.listingId,
                    salesUnitId = freebieSalesUnitId,
                    categoryGroupId = categoryGroupId ?: CategoryGroupV2.FoodAndFMCG.id,
                    quantity = freebieDetail.freebieQuantity,
                    freebieInfo = FreebieInfo(item.id),
                    creditLineId = (item.product as ListingProductItem).creditLineId,
                    creditTenure = item.product.creditTenure
                ),
                freebieDetail.billAmountInPaise
            )
            freebieCartItem to freebiePromoDetail
        } catch (ile: IllegalArgumentException) {
            logger.warn(ile.message)
            null to null
        }
    }
}
