package com.udaan.cart.core.operations.v1.hooks.impl

import arrow.core.*
import com.google.inject.Singleton
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.cart.core.hooks.BaseOperationHook
import com.udaan.cart.core.operations.v1.contexts.EditCartContext
import com.udaan.cart.models.common.ListingProduct
import com.udaan.cart.models.default.EditCartReqDto
import com.udaan.catalog.models.ModelV2

@Singleton
class ListingStatusHook : BaseOperationHook<OpError, EditCartContext<EditCartReqDto>> {
    override suspend fun execute(
        context: EditCartContext<EditCartReqDto>
    ): Either<OpError, EditCartContext<EditCartReqDto>> {
        return context.getCartContext()?.getCart()?.let { cart ->
            val editableProducts = context.request.products.filter {
                (it as ListingProduct).quantity > 0
            }.mapNotNull { product ->
                product as ListingProduct
                cart.items.find { item ->
                    val productItem = item.product as ListingProductItem
                    product.listingId == productItem.listingId &&
                            product.salesUnitId == productItem.salesUnitId &&
                            product.quantity > item.quantity
                }?.let {
                    it.product as ListingProductItem
                }
            }
            val listingMap = context.getListingsMap()
            val inactiveListings = editableProducts.map { product ->
                listingMap[product.listingId]?.let { listing ->
                    val isActive =
                        (listing.status != ModelV2.TradeListing.Status.ACTIVE || listing.salesUnitList.find {
                            it.salesUnitId == product.salesUnitId
                        }?.status != ModelV2.EleStatus.ENABLED)
                    (listing.listingId to isActive)
                } ?: (product.listingId to false)
            }.filter { it.second }.mapNotNull { listingMap[it.first]?.let { it.generatedTitle.ifEmpty { it.title } } }
            if (inactiveListings.isNotEmpty()) {
                val listingNames = inactiveListings.joinToString(", ")
                OpError(message = "$listingNames not active any more").left()
            } else context.right()
        } ?: context.right()
    }
}
