package com.udaan.cart.core.payment

import com.udaan.cart.core.domain.models.PaymentMethod
import com.udaan.cart.models.common.CheckoutExperienceType
import com.udaan.cart.models.common.MetadataVariables

fun List<PaymentMethod>.addCheckoutExperienceTypeInfo(checkoutExpType: CheckoutExperienceType): List<PaymentMethod> {
    val checkoutExperienceMetadata = mapOf(MetadataVariables.CHECKOUT_EXPERIENCE_TYPE.name to checkoutExpType.name)
    return this.map { it.copy(metaData = it.metaData + checkoutExperienceMetadata) }
}

