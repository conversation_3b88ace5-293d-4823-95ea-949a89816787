package com.udaan.cart.core.operations.v1.lifecycles.impl

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.google.inject.Inject
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.CartContextFactory
import com.udaan.cart.core.db.repo.CartWriteRepo
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.cart.core.operations.v1.contexts.UpdateCreditLineContext
import com.udaan.cart.core.operations.v1.hooks.UpdateCreditLineHookCollection
import com.udaan.cart.core.operations.v1.lifecycles.AbstractUpdateCreditLineLifecycle
import com.udaan.cart.models.default.UpdateCreditLineReqDto
import com.udaan.common.utils.kotlin.logger

class DefaultUpdateCreditLineLifecycle @Inject constructor(
    private val cartContextFactory: CartContextFactory,
    private val cartWriteRepo: CartWriteRepo,
) : AbstractUpdateCreditLineLifecycle<UpdateCreditLineReqDto>() {

  companion object {
    private val logger by logger()
  }


  override suspend fun setupHooks(
      hooks: UpdateCreditLineHookCollection<OpError, UpdateCreditLineContext<UpdateCreditLineReqDto>>,
      context: UpdateCreditLineContext<UpdateCreditLineReqDto>
  ): UpdateCreditLineHookCollection<OpError, UpdateCreditLineContext<UpdateCreditLineReqDto>> {
    return hooks
  }


  override suspend fun doFetch(
      context: UpdateCreditLineContext<UpdateCreditLineReqDto>
  ): Either<OpError, UpdateCreditLineContext<UpdateCreditLineReqDto>> {
    return kotlin.runCatching {
      logger.info("#### [doFetch] context.selectorData = ${context.selectorData}")
      val carts = context.cartSelector.find(context.selectorData) ?: emptyList()
      logger.info("#### [doFetch] carts = $carts")
      val cartsContext = cartContextFactory.createCartsContext(carts)
      context.setCartsContext(cartsContext)
      context.right()
    }.getOrElse { ex ->
      logger.error("Failed to fetch cart", ex)
      OpError(message = "Failed to fetch cart").left()
    }
  }

  override suspend fun doUpdateCreditLineAndStoreCartDetails(
      context: UpdateCreditLineContext<UpdateCreditLineReqDto>
  ): Either<OpError, UpdateCreditLineContext<UpdateCreditLineReqDto>> {
    val resp =  context.getMultiCartContext()?.let { multiCartContext ->
      multiCartContext.getCarts().let { carts ->
        carts.map { cart ->
          val cartItems = cart.items
          val updatedCartItems = cartItems.map { cartItem ->
            val data = cartItem.product as ListingProductItem
            val creditLineIdFromCart = data.creditLineId
            val creditTenure = if(creditLineIdFromCart.isNullOrEmpty().not()) {
              data.creditTenure
            } else {
              context.request.creditTenure
            }
            val updatedData = data.copy(
                    creditLineId = context.request.creditLineId,
                    creditTenure = creditTenure
            )
            cartItem.copy(product = updatedData)
          }
          logger.info("### cartItems before update =>")
          cartItems.map {
            it.product as ListingProductItem
            logger.info("cartId = ${cart.id} cartItemId = ${it.id} creditLineId = ${it.product.creditLineId}" +
                    " creditTenure = ${it.product.creditTenure}")
            // add credit tenure here
          }
          logger.info("### cartItems after update =>")
          updatedCartItems.map {
            it.product as ListingProductItem
            logger.info("cartId = ${cart.id} cartItemId = ${it.id} creditLineId = ${it.product.creditLineId}")
          }
          kotlin.runCatching {
            cartWriteRepo.update(
                cart = cart,
                newItems = emptyList(),
                updatedItems = updatedCartItems,
                discardedItems = emptyList()
            )
          }.getOrElse { e ->
            logger.error("Failed to update credit line in cart ${cart.id} : ${e.message}", e)
            OpError(message = "Failed to update credit line").left()
          }
          context.right()
        }
      }
    } ?: OpError(message = "Requested cart not found").left()
    return context.right()
  }
}
