package com.udaan.cart.core.operations.v1.lifecycles.product.impl

import arrow.core.*
import com.google.inject.Inject
import com.google.inject.Injector
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.common.providers.PriceContextInterpreter
import com.udaan.cart.core.domain.CartItemBuilder
import com.udaan.cart.core.domain.models.ItemPUInfo
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.common.utils.kotlin.logger
import com.udaan.cart.core.operations.v1.contexts.EditCartContext
import com.udaan.cart.core.operations.v1.hooks.EditProductHookCollection
import com.udaan.cart.core.operations.v1.hooks.impl.*
import com.udaan.cart.core.operations.v1.lifecycles.impl.EditCartItemsHolder
import com.udaan.cart.core.operations.v1.lifecycles.product.AbstractEditProductLifecycle
import com.udaan.cart.models.default.EditCartReqDto
import com.udaan.listing.builder.enrichments.catalog.CategoryConfigHelper
import com.udaan.orderform.common.providers.*
import kotlinx.coroutines.future.await

class DefaultEditListingLifecycleImpl @Inject constructor(
    injector: Injector,
    private val catalogProvider: CatalogProvider,
    private val orgProvider: OrgProvider,
    private val pricingProvider: PriceProvider,
    private val priceContextInterpreter: PriceContextInterpreter,
    private val categoryConfigHelper: CategoryConfigHelper,
) : AbstractEditProductLifecycle<EditCartReqDto>() {
    companion object {
        private val logger by logger()
    }

    private val postProductsFetch = listOf(
        injector.getInstance(ListingStatusHook::class.java),
        injector.getInstance(IndividuallyBuyableCheckHook::class.java),
        injector.getInstance(CatalogAvailabilityCheckHook::class.java),
        injector.getInstance(EditInventoryCheckHook::class.java),
        injector.getInstance(MaxInventoryCheckHook::class.java),
    )

    private val postEditHooks = listOf(
        injector.getInstance(FreebieDeletionHook::class.java),
    )

    override suspend fun setupHooks(
        hooks: EditProductHookCollection<OpError, EditCartContext<EditCartReqDto>>,
        context: EditCartContext<EditCartReqDto>
    ): EditProductHookCollection<OpError, EditCartContext<EditCartReqDto>> {
        return hooks.copy(
            doPostProductFetch = hooks.doPostProductFetch.plus(postProductsFetch),
            doPostEditHooks = hooks.doPostEditHooks.plus(postEditHooks)
        )
    }

    override suspend fun doLoadProducts(
        context: EditCartContext<EditCartReqDto>
    ): Either<OpError, EditCartContext<EditCartReqDto>> {
        val listingProducts = context.request.getListingProducts()
        context.setListingProducts(listingProducts)
        val listings = listingProducts.map { listingProduct ->
            listingProduct.listingId to listingProduct.salesUnitId
        }.groupBy { it.first }.map { (listingId, salesUnitMap) ->
            listingId to salesUnitMap.map { it.second }
        }

        val listingsMap = catalogProvider.getMultipleListings(listings).associateBy {
            it.listingId
        }
        context.setListingsMap(listingsMap = listingsMap)
        return context.right()
    }

    override suspend fun doLoadPrices(
        context: EditCartContext<EditCartReqDto>
    ): Either<OpError, EditCartContext<EditCartReqDto>> {
        val listingProducts = context.getListingProducts() ?: context.request.getListingProducts()
        val buyerOrg = try {
            orgProvider.fetchOrgDetails(context.request.buyerId).await()
        } catch (e: Exception) {
            logger.error(
                "[doLoadPrices]Failed to fetch buyer org details: ${e.message}",
                e
            )
            return OpError(message = "Unable to find buyer org details").left()
        }
        val buyerOrgUnitId = context.request.buyerOrgUnitId ?: buyerOrg?.orgAccount?.headOfficeOrgUnitRef ?: ""
        val pincode = if (buyerOrgUnitId.isNotEmpty()) {
            buyerOrg?.orgAccount?.orgUnitsMap?.get(buyerOrgUnitId)?.unitAddress?.pincode
        } else ""
        if (pincode.isNullOrBlank()) {
            logger.error("[doLoadPrices] Pincode = $pincode buyerOrg = ${buyerOrg.orgAccount.orgId} " +
                "buyerOrgUnitId = $buyerOrgUnitId")
        }
        val listingSalesUnitRef = listingProducts.map { listingProduct ->
            ListingSalesUnitRef(
                listingId = listingProduct.listingId,
                salesUnitId = listingProduct.salesUnitId,
            )
        }
        val pricingContext = pricingProvider.fetchPriceAsync(
            PriceRequest(
                orgId = context.request.buyerId,
                orgUnitId = context.request.buyerOrgUnitId,
                listingsMap = context.getListingsMap(),
                listingSalesUnitRef = listingSalesUnitRef,
                pincode = pincode
            )
        )
        // TODO: Validate prices, if it's missing for any sales unit
        context.setPriceContext(pricingContext)

        return context.right()
    }

    override suspend fun doEditCart(
        context: EditCartContext<EditCartReqDto>
    ): Either<OpError, EditCartContext<EditCartReqDto>> {
        if (context.getCartContext() == null) {
            return OpError(message = "Unable to find any valid cart for the request").left()
        }
        logger.info("[doEditCart] Editing cart")
        context.getCartContext()?.getCart()?.let { cart ->
            val allCartItems = cart.items
            val listingRequests = context.getListingProducts() ?: context.request.getListingProducts()
            val initial = EditCartItemsHolder(
                newItems = emptyList(),
                editedItems = emptyList(),
                discardedItems = emptyList(),
            )
            val editedCartInfo = listingRequests.fold(initial) { currentState, listingRequest ->
                val existingItem = allCartItems.firstOrNull { item ->
                    (item.product as ListingProductItem).salesUnitId == listingRequest.salesUnitId
                }
                existingItem?.let { item ->
                    if (listingRequest.quantity == 0) {
                        // delete item
                        currentState.addDiscardedItem(item)
                    } else if (listingRequest.quantity != existingItem.quantity) {
                        val editedProductItem =
                            (item.product as ListingProductItem).copy(quantity = listingRequest.quantity)
                        val editedItem =
                            item.copy(quantity = listingRequest.quantity, product = editedProductItem).run {
                                context.getPriceContext()?.let { priceContext ->
                                    priceContextInterpreter.applyPrice(priceContext, this).item
                            } ?: this
                        }
                        currentState.addEditedItem(editedItem)
                    } else {
                        currentState
                    }
                } ?: kotlin.run {
                    // If quantity is 0 and identified as a new listing request, do not add item
                    if (listingRequest.quantity > 0) {
                        val listing = context.getListingsMap()[listingRequest.listingId]
                        listing?.let {
                            val categoryGroupId =
                                categoryConfigHelper.getCategoryGroup(listing = it, vertical = null, sellerOrg = null)
                            val itemPuInfo = listingRequest.puInfo?.puType?.let { puType ->
                                ItemPUInfo(puType)
                            }
                            val newItem = CartItemBuilder().build(
                                cartId = cart.id,
                                quantity = listingRequest.quantity,
                                product = ListingProductItem(
                                    sellerId = listing.orgId,
                                    listingId = listingRequest.listingId,
                                    salesUnitId = listingRequest.salesUnitId,
                                    categoryGroupId = categoryGroupId ?: "",
                                    quantity = listingRequest.quantity,
                                    creditTenure = listingRequest.creditTenure,
                                    creditLineId = listingRequest.creditLineId
                                ),
                                perUnitAmountInPaise = 0,
                                puInfo = itemPuInfo
                            ).run {
                                context.getPriceContext()?.let { priceContext ->
                                    priceContextInterpreter.applyPrice(priceContext, this).item
                                } ?: this
                            }
                            currentState.addNewItem(newItem)
                        } ?: currentState
                    } else currentState
                }
            }
            context.setEditItemHolder(
                EditCartItemsHolder(
                    newItems = editedCartInfo.newItems,
                    editedItems = editedCartInfo.editedItems,
                    discardedItems = editedCartInfo.discardedItems
                )
            )
            context
        }
        return context.right()
    }
}
