package com.udaan.cart.core.operations.v1.contexts

import com.udaan.cart.core.context.BaseOperationContext
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.domain.models.Cart
import com.udaan.cart.core.selectors.CartSelector
import com.udaan.cart.core.selectors.data.SelectorData

data class FetchCartContext<R>(
    val request: R,
    val selectorData: SelectorData,
    val cartSelector: CartSelector<Cart>,
) : BaseOperationContext() {
    private var cartContext: CartContext? = null

    suspend fun setCartContext(cartContext: CartContext) {
        this.cartContext = cartContext
    }

    override suspend fun getCartContext(): CartContext? {
        return cartContext
    }
}
