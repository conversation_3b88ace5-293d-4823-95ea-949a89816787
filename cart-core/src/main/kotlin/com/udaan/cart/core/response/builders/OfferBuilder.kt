package com.udaan.cart.core.response.builders

import com.udaan.cart.core.common.providers.promotions.DiscountDetails
import com.udaan.cart.core.common.providers.promotions.PromoContext
import com.udaan.cart.core.domain.models.*
import com.udaan.cart.models.common.*
import com.udaan.common.utils.kotlin.logger
import com.udaan.promotions.dto.promotion.RewardClass
import com.udaan.promotions.dto.v2.promotion.*

class OfferBuilder(
    private val detailedCart: DetailedCart,
    private val promoContext: PromoContext?,
) {
    companion object {
        private val logger by logger()
    }

    suspend fun build(): List<Offer> {
        return promoContext?.let { context ->
            context.promosResponse.promotions.map { promotionDetails ->
                createOfferItem(promotionDetails, detailedCart, promoContext.getItemToDiscountMap())
            }
        } ?: emptyList()
    }

    private fun createOfferItem(
        promotionDetails: PromotionDetails,
        detailedCart: DetailedCart,
        itemToDiscountMap: Map<String, Map<String, DiscountDetails>>
    ): Offer {
        val offerStatus = when (promotionDetails.status) {
            PromotionStatus.APPLIED -> OfferStatus.APPLIED
            PromotionStatus.APPLICABLE -> OfferStatus.APPLICABLE
            PromotionStatus.COLLECTIBLE -> OfferStatus.AVAILABLE
        }
        val offerType = when (promotionDetails.rewardDetails?.rewardClass) {
            RewardClass.DISCOUNT -> OfferType.DISCOUNT
            else -> OfferType.UNKNOWN
        }
        val promoType = when {
            promotionDetails.type == PromotionType.PROMOTION &&
                    promotionDetails.getAssociatedCoupon() != null -> PromoType.COUPON

            promotionDetails.type == PromotionType.PROMOTION -> PromoType.PROMOTION
            promotionDetails.type == PromotionType.COUPON -> PromoType.COUPON
            else -> PromoType.UNKNOWN
        }
        val applicabilityLevel = when (promotionDetails.level) {
            PromotionLevel.ITEM -> OfferApplicabilityLevel.LINE_ITEM
            PromotionLevel.CART -> OfferApplicabilityLevel.CART
        }
        val offerProducts = detailedCart.cart.items.filter { item ->
            promotionDetails.applicableLineIds.contains(item.id)
        }.map { item ->
            val discountDetails = itemToDiscountMap[item.id]?.get(promotionDetails.id)
            item.product as ListingProductItem
            OfferProduct(
                listingId = item.product.listingId,
                salesUnitId = item.product.salesUnitId,
                unit = item.quantity,
                discountInPaise = discountDetails?.discountInPaise,
                discountBps = discountDetails?.discountBps,
            )
        }
        val nudgeDetails = promotionDetails.getAggregateAmountNudgeDetails()
        val distanceToRedemption = nudgeDetails?.let {
            DistanceToRedemption(
                message = it.nudgeMessage,
                unit = DistanceToRedemptionUnit.PAISE,
                currentValue = it.currentValueInPaise,
                targetValue = it.targetValueInPaise,
            )
        }
        val coupon = promotionDetails.getAssociatedCoupon()
        return Offer(
            promotionId = promotionDetails.id,
            offerType = offerType,
            promoType = promoType,
            status = offerStatus,
            applicabilityLevel = applicabilityLevel,
            title = promotionDetails.attributes?.title ?: "",
            description = promotionDetails.attributes?.description ?: "",
            summary = promotionDetails.attributes?.summary ?: "",
            termsAndConditions = promotionDetails.attributes?.tnc ?: "",
            metaData = OfferMetaData(
                products = offerProducts,
                couponId = coupon?.id,
                couponCode = coupon?.code,
                offerExpiry = coupon?.endAt,
                distanceToRedemption = distanceToRedemption,
                isDeal = promotionDetails.isFlatDiscountCouponPromo(),
                tierIndex = promotionDetails.tierIndex
            ),
        )
    }
}
