package com.udaan.cart.core.payment.policies

import com.udaan.cart.core.payment.PaymentEvalContext

class PrepaidOnlyListingPolicy : AbstractPrePaymentPolicy() {
    companion object {
        private const val POLICY_NAME = "PrepaidOnlyListingPolicy"
        private const val BPS = HUNDRED_PERCENT_BPS
        private const val INSTRUCTIONS = "Some products you're purchasing require full online payment"
    }

    override fun getName(): String = POLICY_NAME
    override suspend fun evaluate(paymentContext: PaymentEvalContext): PrePaymentResult {
        val hasPrepaidListings = paymentContext.listingsMap.values.any { it.config.prepaidOnly }
        return if (hasPrepaidListings) {
            prepareResult(
                bps = BPS,
                totalAmount = paymentContext.detailedCart.totalAmount(),
                description = "",
                instruction = INSTRUCTIONS,
            )
        } else {
            defaultResult()
        }
    }
}
