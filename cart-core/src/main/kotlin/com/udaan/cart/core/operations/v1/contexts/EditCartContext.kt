package com.udaan.cart.core.operations.v1.contexts

import com.udaan.cart.core.context.BaseOperationContext
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.domain.models.Cart
import com.udaan.cart.core.operations.v1.lifecycles.impl.EditCartItemsHolder
import com.udaan.cart.core.selectors.CartSelector
import com.udaan.cart.core.selectors.data.SelectorData
import com.udaan.cart.models.common.ListingProduct
import com.udaan.cart.models.default.EditCartReqDto
import com.udaan.catalog.models.ModelV2
import com.udaan.orderform.common.providers.contexts.PriceContext

class EditCartContext<R>(
    var request: R,
    val selectorData: SelectorData,
    val cartSelector: CartSelector<Cart>,
) : BaseOperationContext() {
    private var cartContext: CartContext? = null
    private var listingsMap: Map<String, ModelV2.TradeListing> = emptyMap()
    private var priceContext: PriceContext? = null
    private var editedCart: Cart? = null
    private var editItemsHolder: EditCartItemsHolder? = null
    private var listingProductRequests: List<ListingProduct>? = null

    override suspend fun getCartContext(): CartContext? {
        return cartContext
    }

    fun setCartContext(cartContext: CartContext) {
        this.cartContext = cartContext
    }

    internal fun setListingsMap(listingsMap: Map<String, ModelV2.TradeListing>) {
        this.listingsMap = listingsMap
    }

    internal fun getListingsMap() = listingsMap

    internal fun setPriceContext(priceContext: PriceContext) {
        this.priceContext = priceContext
    }

    internal fun getPriceContext() = priceContext

    internal fun setEditedCart(editedCart: Cart) {
        this.editedCart = editedCart
    }

    internal fun getEditedCart(): Cart? {
        return this.editedCart
    }

    internal fun setEditItemHolder(editItemHolder: EditCartItemsHolder) {
        this.editItemsHolder = editItemHolder
    }

    internal fun getEditedItems(): EditCartItemsHolder? {
        return editItemsHolder
    }

    internal fun setListingProducts(listingProducts: List<ListingProduct>) {
        this.listingProductRequests = listingProducts
    }

    internal fun getListingProducts(): List<ListingProduct>? {
        return listingProductRequests
    }
}
