package com.udaan.cart.core.operations.v1.lifecycles

import arrow.core.Either
import arrow.core.continuations.either
import com.udaan.cart.core.common.helpers.Lifecycle
import com.udaan.cart.core.common.helpers.LifecycleStage
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.BaseContext
import com.udaan.cart.core.operations.v1.contexts.FetchCartContext
import com.udaan.cart.core.operations.v1.hooks.FetchCartHookCollection

abstract class AbstractFetchCartLifecycle<R> {
    protected abstract suspend fun setupHooks(
        hooks: FetchCartHookCollection<OpError, FetchCartContext<R>>,
        context: FetchCartContext<R>
    ): FetchCartHookCollection<OpError, FetchCartContext<R>>

    protected abstract suspend fun doFetch(
        context: FetchCartContext<R>
    ): Either<OpError, FetchCartContext<R>>

    @Suppress("UNCHECKED_CAST")
    suspend fun <T : BaseContext> execute(
        context: FetchCartContext<R>,
        hooks: FetchCartHookCollection<OpError, T>
    ): Either<OpError, FetchCartContext<R>> {
        val hook = setupHooks(
            hooks as FetchCartHookCollection<OpError, FetchCartContext<R>>,
            context
        )
        val stages = listOf(
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doFetch(currentContext)
                },
                post = hook.postCartFetchHooks,
            )
        )
        return either {
            Lifecycle(stages = stages)
                .execute(context)
                .bind()
        }
    }
}
