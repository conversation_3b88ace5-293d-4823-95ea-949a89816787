package com.udaan.cart.core.operations.v1.lifecycles.impl

import arrow.core.*
import com.google.inject.Inject
import com.google.inject.Injector
import com.google.inject.name.Named
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.CartContextFactory
import com.udaan.cart.core.domain.models.CartState
import com.udaan.cart.core.domain.models.PaymentMethod
import com.udaan.cart.core.exceptions.ExceptionHandler
import com.udaan.cart.core.exceptions.NoCartFoundException
import com.udaan.cart.core.operations.v1.contexts.PaymentMethodsContext
import com.udaan.cart.core.operations.v1.hooks.FetchPaymentMethodsHookCollection
import com.udaan.cart.core.operations.v1.hooks.impl.CPODPaymentMethodsBuilderHook
import com.udaan.cart.core.operations.v1.hooks.impl.OnlyTCPaymentMethodFilterHook
import com.udaan.cart.core.operations.v1.lifecycles.AbstractPaymentMethodsLifecycle
import com.udaan.cart.core.payment.PaymentEvalContext
import com.udaan.cart.core.payment.PaymentMethodResolver
import com.udaan.cart.models.default.PaymentMethodsReqDto
import com.udaan.common.utils.kotlin.logger

class DefaultPaymentMethodsLifecycle @Inject constructor(
    injector: Injector,
    private val cartContextFactory: CartContextFactory,
    @Named("default_exception_handler") private val exceptionHandler: ExceptionHandler,
    private val paymentMethodResolver: PaymentMethodResolver,
) : AbstractPaymentMethodsLifecycle<PaymentMethodsReqDto>() {
    companion object {
        private val logger by logger()
    }

    private val postPaymentMethodsFetch = listOf(
        injector.getInstance(OnlyTCPaymentMethodFilterHook::class.java),
        injector.getInstance(CPODPaymentMethodsBuilderHook::class.java)
    )

    override suspend fun setupHooks(
        hooks: FetchPaymentMethodsHookCollection<OpError, PaymentMethodsContext<PaymentMethodsReqDto>>,
        context: PaymentMethodsContext<PaymentMethodsReqDto>
    ): FetchPaymentMethodsHookCollection<OpError, PaymentMethodsContext<PaymentMethodsReqDto>> {
        return hooks.copy(
            postPaymentMethods = hooks.postPaymentMethods.plus(postPaymentMethodsFetch),
        )
    }

    override suspend fun doFetch(
        context: PaymentMethodsContext<PaymentMethodsReqDto>
    ): Either<OpError, PaymentMethodsContext<PaymentMethodsReqDto>> {
        return kotlin.runCatching {
            val cart = context.cartSelector.find(context.selectorData)
            cart?.let {
                if (cart.state != CartState.ACTIVE) {
                    logger.error("Cart id: ${cart.id} state is ${cart.state}, not active")
                    OpError(message = "No active cart found, please restart app to see active cart").left()
                } else if(cart.items.isEmpty()) {
                    logger.error("Cart id: ${cart.id} has no items")
                    OpError(message = "Cart is empty, kindly add items to cart").left()
                } else {
                    val cartContext = cartContextFactory.createCartContext(cart.id, cart)
                    val detailedCart = cartContext.getCartWithPromos(
                        orgUnitId = context.request.buyerOrgUnitId,
                        skipOOSItems = true,
                    )
                    context.setDetailedCart(detailedCart)
                    context.setCartContext(cartContext)
                    context.right()
                }
            } ?: throw NoCartFoundException("No active cart found")
        }.getOrElse { ex ->
            logger.error("Failed to fetch cart", ex)
            val errMsg = exceptionHandler.handle(ex)
            OpError(message = errMsg, exception = ex).left()
        }
    }

    override suspend fun doFindPaymentMethods(
        context: PaymentMethodsContext<PaymentMethodsReqDto>
    ): Either<OpError, PaymentMethodsContext<PaymentMethodsReqDto>> {
        return context.getCartContext()?.let { cartContext ->
            context.getDetailedCart()?.let { detailedCart ->
                val paymentEvalContext = PaymentEvalContext(
                    detailedCart = detailedCart,
                    listingsMap = cartContext.getListingsMap(),
                    buyerOrg = cartContext.getBuyerOrg(),
                    buyerOrgUnit = cartContext.getBuyerOrgUnit(context.request.buyerOrgUnitId),
                    cartContext = cartContext,
                )
                val paymentEvalResult = paymentMethodResolver.resolve(paymentEvalContext)
                val paymentMethods = paymentEvalResult.filter { it.isValidMethod }.map { result ->
                    PaymentMethod(
                        collectionType = result.collectionType,
                        payableTotalInPaise = result.payableAmountInPaise,
                        remainingTotalInPaise = result.remainingAmountInPaise,
                        prepaymentBps = result.paymentBps,
                        description = result.userPaymentDetails?.description ?: "",
                        instruction = result.userPaymentDetails?.instruction ?: "",
                        metaData = result.metaData,
                        rewardBasedPayment = result.rewardBasedPayment,
                    )
                }
                context.setPaymentMethods(paymentMethods)
                context.right()
            }
        } ?: context.right()
    }
}
