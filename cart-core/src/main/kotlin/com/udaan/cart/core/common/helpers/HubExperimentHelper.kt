package com.udaan.cart.core.common.helpers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.common.providers.HubProvider
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.context.CartContextKey
import com.udaan.catalog.client.helpers.Category
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.instrumentation.TelemetryScope
import com.udaan.listing.builder.enrichments.catalog.CategoryConfigHelper
import com.udaan.model.OrgUnit
import com.udaan.proto.models.ModelV1

enum class HubExperiment {
    CPOD,
    CPOD_Overdue,
    GST_CALLOUT,
    FMCG_CART
}

@Singleton
class HubExperimentHelper @Inject constructor(
        private val configHelper: ConfigServiceHelper,
        private val categoryConfigHelper: CategoryConfigHelper,
        private val hubProvider: HubProvider
) {

    companion object {
        private val logger by logger()
    }

    /**
     * Checks whether a specific experiment is enabled for the given cart context and experiment settings.
     *
     * @param cartContext The context of the cart, including details about the buyer, cart items,
     *                    and organizational unit information.
     * @param experiment  The experiment to check for enablement, represented by an instance of [HubExperiment].
     * @return A boolean indicating whether the experiment is enabled for the given cart context.
     */
    suspend fun isExperimentEnabled(cartContext: CartContext, experiment: HubExperiment): Boolean {
        val cartCategory = getCartCategory(cartContext)
        val buyerOrgUnit = cartContext.getBuyerOrgUnit(cartContext.getValue(CartContextKey.BUYER_ORG_UNIT_ID))
        val platformId = cartContext.getCart()?.platformId
        try {
            val expEnabledHubsDef = TelemetryScope.async { configHelper.getHubsEnabledForExp(experiment.name) }
            val buyerHubId = if(buyerOrgUnit!=null && platformId!=null) {
                hubProvider.getHubOrgUnitIdForBuyer(
                        category = cartCategory,
                        platform = platformId,
                        pincode = buyerOrgUnit.unitAddress.pincode,
                        buyerOrgUnitId = buyerOrgUnit.orgUnitId)
            } else null
            val expEnabledHubs = expEnabledHubsDef.await()
            logger.info("HubExperimentHelper.isExperimentEnabled $experiment: buyer=${buyerOrgUnit?.orgId} " +
                    "category=$cartCategory hubId=$buyerHubId ExpHubs=${expEnabledHubs}")
            return if(buyerHubId == null) {
                logger.error("HubExperimentHelper.isExperimentEnabled : Hub not found | $cartCategory |" +
                        " ${buyerOrgUnit?.orgId} | $platformId | ${buyerOrgUnit?.unitAddress?.pincode}")
                false
            } else expEnabledHubs.contains(buyerHubId)
        } catch (ex: Exception) {
            logger.info("HubExperimentHelper.isExperimentEnabled $experiment: buyer=${buyerOrgUnit?.orgId} " +
                    "category=$cartCategory | Exception: $ex")
            return false
        }
    }

    /**
     * Get cart category
     * This method determines the category of items in the cart based on the listings provided in the cartContext.
     * If all items in the cart belong to a single category, it returns that category.
     * If multiple categories are present, it defaults to the FoodAndFMCG category.
     *
     * @param cartContext
     * @return The ID of the category for the cart items
     */
    private suspend fun getCartCategory(cartContext: CartContext): String {
        return cartContext.getListingsMap().values.parallelMap { listing ->
            categoryConfigHelper.getCategory(listing, null, null)
        }.mapNotNull { it }.toSet().let {
            if (it.size == 1) it.first()
            else Category.FoodAndFMCG.id
        }
    }

}
