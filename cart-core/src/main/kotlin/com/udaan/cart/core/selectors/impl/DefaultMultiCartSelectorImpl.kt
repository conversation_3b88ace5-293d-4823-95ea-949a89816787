package com.udaan.cart.core.selectors.impl

import com.udaan.cart.core.db.repo.CartReadRepo
import com.udaan.cart.core.selectors.data.DefaultSelectorData
import com.udaan.cart.core.domain.models.Cart
import com.udaan.cart.core.selectors.CartSelector
import com.google.inject.Inject
import com.udaan.cart.core.db.repo.models.DbSelectorData
import com.udaan.cart.core.selectors.data.SelectorData
import com.udaan.common.utils.kotlin.logger

class DefaultMultiCartSelectorImpl @Inject constructor(
    private val cartReadRepo: CartReadRepo,
) : CartSelector<List<Cart>> {
    companion object {
        private val logger by logger()
    }

    override suspend fun <T : SelectorData> find(data: T): List<Cart> {
        val selectorData = data as DefaultSelectorData
        return cartReadRepo.findCarts(
            DbSelectorData(
                buyerId = selectorData.getBuyerId(),
                platformId = selectorData.getPlatformId().name,
                cartSelection = selectorData.getCartSelection(),
                cartId = selectorData.cartId,
            )
        )
    }
}
