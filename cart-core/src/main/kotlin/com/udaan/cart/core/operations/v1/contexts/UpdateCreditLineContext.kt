package com.udaan.cart.core.operations.v1.contexts

import com.udaan.cart.core.context.BaseOperationContext
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.context.MultiCartContext
import com.udaan.cart.core.domain.models.Cart
import com.udaan.cart.core.selectors.CartSelector
import com.udaan.cart.core.selectors.data.SelectorData

data class UpdateCreditLineContext<R>(
    val request: R,
    val selectorData: SelectorData,
    val cartSelector: CartSelector<List<Cart>>,
) : BaseOperationContext() {
  private var cartsContext: MultiCartContext? = null

  suspend fun setCartsContext(cartsContext: MultiCartContext) {
    this.cartsContext = cartsContext
  }

  suspend fun getMultiCartContext(): MultiCartContext? {
    return cartsContext
  }

  override suspend fun getCartContext(): CartContext? {
    return cartsContext?.getCartContext()
  }
}
