package com.udaan.cart.core.common.providers

import com.udaan.cart.core.domain.models.CartItem
import com.udaan.orderform.common.providers.contexts.PriceContext
import com.udaan.pricing.MetaData
import com.udaan.pricing_options.model.PricingOption

internal class PriceBasedCartItemBuilder(
    private val perUnitPrice: Long,
    private val priceOptions: List<PricingOption>,
    private val context: PriceContext,
    private val metadata: MetaData? = null,
) {
    suspend fun build(item: CartItem): CartItem {
        return item.copy(perUnitAmountInPaise = perUnitPrice)
    }
}
