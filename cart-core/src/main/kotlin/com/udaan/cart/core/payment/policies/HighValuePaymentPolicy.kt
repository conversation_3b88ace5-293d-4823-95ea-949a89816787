package com.udaan.cart.core.payment.policies

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.domain.models.CartItem
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.cart.core.payment.PaymentEvalContext
import com.udaan.catalog.client.CategoryGroupV2
import com.udaan.catalog.client.helpers.VerticalCache
import com.udaan.order_mgt.models.ModelV1

@Singleton
class HighValuePaymentPolicy @Inject constructor(
    private val policyExemptionValidator: PolicyExemptionValidator,
    private val verticalCache: VerticalCache,
) : AbstractPrePaymentPolicy() {

    companion object {
        const val POLICY_NAME = "HighValuePaymentPolicy"
        const val RISKY_COD_VALUE_IN_PAISE = 100000 * 100L
        const val PACMAN_RISKY_COD_VALUE_IN_PAISE = 199000 * 100L
        const val MARIO_RISKY_COD_VALUE_IN_PAISE = 0L
        const val PHONES_RISKY_COD_VALUE_IN_PAISE = 100000 * 100L
        const val BPS = HUNDRED_PERCENT_BPS
        const val mobileVertical = "Mobile"
        const val smartphoneVertical = "Smartphone"
        const val INSTRUCTIONS = "You are placing a high value order on cash payment. " +
                "Thank you for your trust. We would need to 100% pre-pay on these order to cover for logistics risk."
    }

    override fun getName(): String = POLICY_NAME

    override suspend fun evaluate(paymentContext: PaymentEvalContext): PrePaymentResult {
        val buyerId = paymentContext.detailedCart.cart.buyerId
        return if (policyExemptionValidator.isExempted(POLICY_NAME, buyerId)) {
            defaultResult()
        } else {
            val categoryGroupId = paymentContext.detailedCart.getCategoryGroupId()
            val cartAmount = paymentContext.detailedCart.totalAmount()
            val codLimit = when(categoryGroupId) {
                CategoryGroupV2.FoodAndFMCG.id -> PACMAN_RISKY_COD_VALUE_IN_PAISE
                CategoryGroupV2.Pharma.id -> RISKY_COD_VALUE_IN_PAISE
                else -> {
                    if (marioCartWithPhones(paymentContext.detailedCart.getCartItems())) {
                        PHONES_RISKY_COD_VALUE_IN_PAISE
                    } else {
                        MARIO_RISKY_COD_VALUE_IN_PAISE
                    }
                }
            }
            if (cartAmount >= codLimit) {
                prepareResult(
                    bps = BPS,
                    totalAmount = cartAmount,
                    description = null,
                    instruction = INSTRUCTIONS,
                )
            } else {
                defaultResult()
            }
        }
    }

    // Identify Mario order with only Mobile, Smartphone verticals - For other Mario orders we need to charge
    // 10% prepayment
    private suspend fun marioCartWithPhones(cartItems: List<CartItem>): Boolean {
        return cartItems.all { cartItem ->
            cartItem.product as ListingProductItem
            val vertical = verticalCache.getVerticalForListing2(cartItem.product.listingId)?.name
            vertical in listOf(mobileVertical, smartphoneVertical)
        }
    }
}
