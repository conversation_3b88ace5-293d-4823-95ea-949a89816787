package com.udaan.cart.core

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.google.inject.AbstractModule
import com.google.inject.Provides
import com.google.inject.Singleton
import com.google.inject.TypeLiteral
import com.google.inject.name.Named
import com.google.inject.name.Names
import com.google.protobuf.util.ProtoJacksonModule
import com.udaan.cart.core.common.providers.*
import com.udaan.cart.core.common.providers.DeliverySlotProvider
import com.udaan.cart.core.common.providers.DeliverySlotProviderImpl
import com.udaan.cart.core.common.providers.OrgIdentityProvider
import com.udaan.cart.core.common.providers.OrgIdentityProviderImpl
import com.udaan.cart.core.common.providers.promotions.PromotionsProvider
import com.udaan.cart.core.common.providers.promotions.PromotionsProviderImpl
import com.udaan.cart.core.common.providers.promotions.SchemesProvider
import com.udaan.cart.core.common.providers.promotions.SchemesProviderImpl
import com.udaan.cart.core.context.CartContextFactory
import com.udaan.cart.core.context.CartContextFactoryImpl
import com.udaan.cart.core.db.repo.CartReadRepo
import com.udaan.cart.core.db.repo.CartWriteRepo
import com.udaan.cart.core.db.repo.impl.CartReadRepoImpl
import com.udaan.cart.core.db.repo.impl.CartWriteRepoImpl
import com.udaan.cart.core.domain.models.Cart
import com.udaan.cart.core.exceptions.ExceptionHandler
import com.udaan.cart.core.exceptions.impl.DefaultExceptionHandler
import com.udaan.cart.core.metrics.CartEventTracker
import com.udaan.cart.core.metrics.CartEventTrackerImpl
import com.udaan.cart.core.metrics.EventTracker
import com.udaan.cart.core.metrics.EventTrackerImpl
import com.udaan.cart.core.operations.v1.lifecycles.*
import com.udaan.cart.core.operations.v1.lifecycles.impl.*
import com.udaan.cart.core.payment.PaymentMethodResolver
import com.udaan.cart.core.payment.PaymentMethodResolverImpl
import com.udaan.cart.core.operations.v1.lifecycles.product.ProductHandler
import com.udaan.cart.core.operations.v1.lifecycles.product.impl.ProductHandlerImpl
import com.udaan.cart.core.response.CheckoutResponseBuilder
import com.udaan.cart.core.response.DeliverySlotResponseBuilder
import com.udaan.cart.core.response.MultiCartResponseBuilder
import com.udaan.cart.core.response.ResponseBuilder
import com.udaan.cart.core.response.builders.*
import com.udaan.cart.core.selectors.CartSelector
import com.udaan.cart.core.selectors.impl.DefaultCartSelectorImpl
import com.udaan.cart.core.selectors.impl.DefaultMultiCartSelectorImpl
import com.udaan.cart.core.service.v1.CartService
import com.udaan.cart.core.service.v1.DefaultCartServiceImpl
import com.udaan.cart.core.violations.*
import com.udaan.cart.core.violations.mov.MOVViolationValidator
import com.udaan.cart.models.BaseRequestDto
import com.udaan.cart.models.BaseResponseDto
import com.udaan.cart.models.default.*
import com.udaan.catalog.client.CatalogServiceClient
import com.udaan.catalog.client.CategoryTreeV2
import com.udaan.catalog.client.RedisListingRepository
import com.udaan.catalog.client.combos.CatalogCombosClientV2
import com.udaan.catalog.client.helpers.VerticalCache
import com.udaan.chat.client.ChatServiceClient
import com.udaan.common.client.UdaanClientConfig
import com.udaan.common.client.UdaanServiceClient
import com.udaan.common.server.PrometheusClient
import com.udaan.common.server.UdaanServerConfig
import com.udaan.compliance.client.ComplianceServiceClient
import com.udaan.config.client.BusinessConfigClient
import com.udaan.config.client.ConfigClientFactory
import com.udaan.constraint.client.ConstraintClient
import com.udaan.credit.client.CreditRepository
import com.udaan.credit.client.CreditServiceClient
import com.udaan.dropslot.client.DropslotServiceClient
import com.udaan.fulfilment.client.ManagedFulfillmentServiceClient
import com.udaan.listing.builder.enrichments.catalog.CategoryConfigHelper
import com.udaan.orchestrator.client.AvailabilityServiceClient
import com.udaan.orchestrator.client.DeliverySlotsServiceClient
import com.udaan.orchestrator.client.MaxQuantityClient
import com.udaan.orchestrator.client.PromiseServiceClient
import com.udaan.orderform.client.*
import com.udaan.orderform.common.providers.*
import com.udaan.orderservice.client.OrderGraphQLClient
import com.udaan.orderservice.client.OrderReadServiceClient
import com.udaan.pricing.LotPricingClient
import com.udaan.pricing.PricingClient
import com.udaan.pricing_options.core.PharmaPricingOption
import com.udaan.pricing_options.core.PricingOptionsClientV2
import com.udaan.promotions.client.PromotionsServiceClientV2
import com.udaan.resources.ResourceBuilder
import com.udaan.rewards.client.RewardsCartClient
import com.udaan.rewards.client.RewardsClient
import com.udaan.scnetwork.client.userFacilityEdge.BuyerHubMappingCacheClient
import com.udaan.tradecredit.client.TradeCreditClient
import com.udaan.tradequality.client.CartProfilingClient
import com.udaan.tradequality.client.OrderProfilingClient
import com.udaan.tradequality.client.TradeQualityBuyerRiskClient
import com.udaan.user.client.OrgInternalIdentityCacheClient
import com.udaan.user.client.OrgServiceClient
import com.udaan.user.client.RedisOrgRepository
import org.jdbi.v3.core.Jdbi
import java.util.concurrent.TimeUnit

class CartCoreModule : AbstractModule() {

    private val cacheMetrics = PrometheusClient.cacheMetricsCollector
    override fun configure() {
        super.configure()

        val dbiInstance = buildDBClient()
        bind(Jdbi::class.java).toInstance(dbiInstance)
        getReadRepo()

        bind(OrgProvider::class.java).to(OrgProviderImpl::class.java)
        bind(CatalogProvider::class.java).to(CatalogProviderImpl::class.java)
        bind(PromotionsProvider::class.java).to(PromotionsProviderImpl::class.java)
        bind(CatalogProvider::class.java)
            .annotatedWith(Names.named("cached_listing_provider"))
            .to(CachedCatalogProviderImpl::class.java)
        bind(PriceProvider::class.java).to(PriceProviderImpl::class.java)
        bind(PriceContextInterpreter::class.java).to(PriceContextInterpreterImpl::class.java)
        bind(LevyProvider::class.java).to(LevyProviderImpl::class.java)
        bind(InventoryProvider::class.java).to(InventoryProviderImpl::class.java)
        bind(DeliverySlotProvider::class.java)
                .annotatedWith(Names.named("delivery_slot_provider"))
                .to(DeliverySlotProviderImpl::class.java)
        bind(DeliverySlotProvider::class.java)
                .annotatedWith(Names.named("item_level_delivery_slot_provider"))
                .to(ItemLevelDeliverySlotProviderImpl::class.java)
        bind(MOQProvider::class.java).to(MOQProviderImpl::class.java)
        bind(MOVProvider::class.java).to(MOVProviderImpl::class.java)
        bind(HubProvider::class.java).to(HubProviderImpl::class.java)
        bind(OrderCheckoutProvider::class.java).to(OrderCheckoutProviderImpl::class.java)
        bind(OrgIdentityProvider::class.java).to(OrgIdentityProviderImpl::class.java)
        bind(OrderReadProvider::class.java).to(OrderReadProviderImpl::class.java)
        bind(ViolationValidator::class.java)
            .annotatedWith(Names.named("moq_violation_validator"))
            .to(MOQViolationValidator::class.java)
        bind(ViolationValidator::class.java)
            .annotatedWith(Names.named("mov_violation_validator"))
            .to(MOVViolationValidator::class.java)
        bind(ViolationValidator::class.java)
            .annotatedWith(Names.named("max_inventory_validator"))
            .to(MaxInventoryValidator::class.java)

        bind(com.udaan.orderform.common.metrics.EventTracker::class.java)
            .to(com.udaan.orderform.common.metrics.EventTrackerImpl::class.java)
        bind(EventTracker::class.java).to(EventTrackerImpl::class.java)
        bind(CartEventTracker::class.java).to(CartEventTrackerImpl::class.java)

        bind(CartWriteRepo::class.java).to(CartWriteRepoImpl::class.java)

        bind(CartContextFactory::class.java).to(CartContextFactoryImpl::class.java)
        bind(ProductHandler::class.java).to(ProductHandlerImpl::class.java)
        bind(DeliverySlotResponseBuilder::class.java).to(DefaultDeliverySlotResBuilder::class.java)
        bind(PaymentMethodResolver::class.java).to(PaymentMethodResolverImpl::class.java)
        bind(SchemesProvider::class.java).to(SchemesProviderImpl::class.java)

        bind(ExceptionHandler::class.java)
            .annotatedWith(Names.named("default_exception_handler"))
            .to(DefaultExceptionHandler::class.java)

        bind(object : TypeLiteral<AbstractCreateCartLifecycle<CreateCartReqDto>>() {})
            .annotatedWith(Names.named("default_create_cart"))
            .to(DefaultCreateCartLifecycle::class.java)

        bind(object : TypeLiteral<AbstractEditCartLifecycle<EditCartReqDto>>() {})
            .annotatedWith(Names.named("default_edit_cart"))
            .to(DefaultEditCartLifecycle::class.java)

        bind(object : TypeLiteral<AbstractDeleteCartLifecycle<DeleteCartReqDto>>() {})
            .annotatedWith(Names.named("default_delete_cart"))
            .to(DefaultDeleteCartLifecycle::class.java)

        bind(object : TypeLiteral<AbstractFetchCartLifecycle<FetchCartReqDto>>() {})
            .annotatedWith(Names.named("default_fetch_cart"))
            .to(DefaultFetchCartLifecycle::class.java)

        bind(object : TypeLiteral<AbstractFetchCartsLifecycle<FetchCartReqDto>>() {})
            .annotatedWith(Names.named("default_fetch_carts"))
            .to(DefaultFetchCartsLifecycle::class.java)

        bind(object : TypeLiteral<AbstractPaymentMethodsLifecycle<PaymentMethodsReqDto>>() {})
            .annotatedWith(Names.named("default_fetch_payment_methods"))
            .to(DefaultPaymentMethodsLifecycle::class.java)

        bind(object : TypeLiteral<AbstractCheckoutCartLifecycle<CheckoutRequestDto>>() {})
            .annotatedWith(Names.named("default_checkout_lifecycle"))
            .to(DefaultCheckoutCartLifecycle::class.java)

        bind(object : TypeLiteral<AbstractUpdateCreditLineLifecycle<UpdateCreditLineReqDto>>() {})
            .annotatedWith(Names.named("default_update_creditline_lifecycle"))
            .to(DefaultUpdateCreditLineLifecycle::class.java)

        bind(object : TypeLiteral<CartSelector<Cart>>() {})
            .annotatedWith(Names.named("default_cart_selector"))
            .to(DefaultCartSelectorImpl::class.java)

//        bind(object : TypeLiteral<CartSelector<List<Cart>>>() {})
//            .annotatedWith(Names.named("default_multi_cart_selector"))
//            .to(DefaultMultiCartSelectorImpl::class.java)

        bind(ResponseBuilder::class.java)
            .annotatedWith(Names.named("default_response_builder"))
            .to(DefaultResponseBuilder::class.java)

        bind(ResponseBuilder::class.java)
            .annotatedWith(Names.named("default_payment_methods_response_builder"))
            .to(DefaultPaymentMethodsResponseBuilder::class.java)

        bind(CheckoutResponseBuilder::class.java)
            .annotatedWith(Names.named("default_checkout_response_builder"))
            .to(DefaultCheckoutResponseBuilder::class.java)

        bind(MultiCartResponseBuilder::class.java)
            .annotatedWith(Names.named("default_multi_cart_response_builder"))
            .to(DefaultMultiCartResponseBuilder::class.java)

        bind(object : TypeLiteral<CartService<BaseRequestDto, BaseResponseDto>>() {})
            .annotatedWith(Names.named("default_cart_service"))
            .to(DefaultCartServiceImpl::class.java)
        bind(ConstraintProvider::class.java).to(ConstraintProviderImpl::class.java)
    }

    private fun buildDBClient(): Jdbi {
//        return Jdbi.create("****************************************************************************")
//            .installPlugin(SqlObjectPlugin())
//            .installPlugin(KotlinPlugin())
//            .installPlugin(KotlinSqlObjectPlugin())
//            .installPlugin(Jackson2Plugin())
//            .installPlugin(PostgresPlugin())
        return ResourceBuilder.psqlClient("cart-service-psql").build()
    }

    @Named("default_multi_cart_selector")
    @Singleton
    @Provides
    private fun getMultiCartSelector(): CartSelector<List<Cart>> =
        DefaultMultiCartSelectorImpl(cartReadRepo = getReadRepo())

    @Singleton
    @Provides
    private fun getReadRepo(): CartReadRepo = CartReadRepoImpl(buildDBClient())

    @Singleton
    @Provides
    fun getCatalogComboClientV2(): CatalogCombosClientV2 = makeClient("resilient-catalog-read-path")


    @Singleton
    @Provides
    internal fun getPricingOptionsClientV2(
        chatServiceClient: ChatServiceClient,
        creditRepository: CreditRepository,
        pharmaPricingOption: PharmaPricingOption
    ): PricingOptionsClientV2 {
        return PricingOptionsClientV2(
            chatServiceClient,
            creditRepository,
            getClientConfig("credit"),
            pharmaPricingOption
        )
    }

    @Singleton
    @Provides
    fun getCreditRepository(): CreditRepository {
        return CreditRepository(
            creditServiceConfig = getClientConfig("credit"),
            creditFixedInfoCacheSize = 5000, creditFixedInfoCacheExpiryMinutes = 30,
            orgFilterTagsSize = 5000, orgFilterTagsCacheExpiryMinutes = 30,
            creditPolicyCacheSize = 20, creditPolicyCacheExpiryMinutes = TimeUnit.DAYS.toMinutes(10)
        )
    }

    @Singleton
    @Provides
    private fun getCategoryGroupHelper(
        verticalCache: VerticalCache,
        orgRepository: RedisOrgRepository,
        catalogServiceClient: RedisListingRepository,
        categoryTreeV2: CategoryTreeV2
    ) = CategoryConfigHelper(
        verticalCache,
        orgRepository,
        catalogServiceClient,
        categoryTreeV2
    )

    @Singleton
    @Provides
    private fun makeCreditServiceClient(): CreditServiceClient = makeClient("credit")

    @Singleton
    @Provides
    private fun makeComplianceServiceClient(): ComplianceServiceClient = makeClient("compliance-service")

    @Singleton
    @Provides
    fun makeAvailabilityServiceClient(): AvailabilityServiceClient = makeClient("orchestrator-service")

    @Singleton
    @Provides
    private fun provideMaxQuantityClient(): MaxQuantityClient = makeClient("orchestrator-service")

    @Singleton
    @Provides
    fun makeDeliverySlotClient(): DeliverySlotsServiceClient = makeClient("orchestrator-service")

    @Singleton
    @Provides
    fun makeConstraintServiceClient(): ConstraintClient = makeClient("constraint-service")

    @Singleton
    @Provides
    fun makeOrderFormServiceClient(): OrderFormClientV2 = makeClient("orderform-service")

    @Singleton
    @Provides
    fun makeOrderCheckoutClient(): OrderCheckoutClient = makeClient("orderform-service")

    @Singleton
    @Provides
    fun makeHorecaOrderFormClient(): HorecaOrderFormClientV1 = makeClient("pacman-orderform-service")

    @Singleton
    @Provides
    fun makePacmanOrderFormClient(): MultiSellerCartClient = makeClient("pacman-orderform-service")

    @Singleton
    @Provides
    fun makeOrderFormCartClient(): CartClient = makeClient("orderform-service")

    @Provides
    @Singleton
    fun makeOrderReadServiceClient() = OrderReadServiceClient(getClientConfig("order-read-service"))
    @Provides
    @Singleton
    fun makeOrderGraphQLClient() = OrderGraphQLClient(getClientConfig("order-read-service"))

    @Singleton
    @Provides
    fun makePromotionsClient(): PromotionsServiceClientV2 = makeClient("promotions-service")

    @Singleton
    @Provides
    fun makeRewardsClient(): RewardsClient = makeClient("rewards-service")

    @Singleton
    @Provides
    fun makeRewardsCartClient(): RewardsCartClient = makeClient("rewards-service")

    @Singleton
    @Provides
    fun makeCartProfilingClient(): CartProfilingClient = makeClient("trade-quality-service")

    @Singleton
    @Provides
    fun makeOrgServiceClient(): OrgServiceClient = makeClient("user-service")

    @Singleton
    @Provides
    fun getOrgInternalIdentityCacheClient(orgServiceClient: OrgServiceClient) =
        OrgInternalIdentityCacheClient(orgServiceClient, cacheMetrics)

    @Singleton
    @Provides
    private fun providedPricingServiceClient(): PricingClient = makeClient("resiliency-price-read-path")

    @Singleton
    @Provides
    private fun getLotPricingServiceClient(): LotPricingClient = makeClient("resiliency-price-read-path")

    @Singleton
    @Provides
    fun getMangedFFServiceClient(): ManagedFulfillmentServiceClient = makeClient("fulfillment-service")

    @Singleton
    @Provides
    private fun makeDropSlotServiceClient(): DropslotServiceClient = makeClient("dropslot-service")

    @Singleton
    @Provides
    fun getOrchestratorPromiseServiceClient(): PromiseServiceClient = makeClient("orchestrator-service")

    @Singleton
    @Provides
    fun makeBuyerHubMappingClient(): BuyerHubMappingCacheClient {
        val config: UdaanClientConfig = getClientConfig("orchestrator-service")
        return BuyerHubMappingCacheClient(config)
    }

    @Provides
    @Singleton
    fun getObjectMapper(): ObjectMapper = jacksonObjectMapper()
        .registerKotlinModule()
        .registerModule(ProtoJacksonModule())
        .registerModule(JavaTimeModule())
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)

    @Singleton
    @Provides
    private fun orgRepository() = RedisOrgRepository(
        getClientConfig("user-service"),
        RedisOrgRepository.defaultLocalCacheConfig.copy(size = 2000),
        RedisOrgRepository.defaultLocalCacheConfig.copy(size = 2000)
    )

    @Singleton
    @Provides
    private fun makeChatClient(): ChatServiceClient = makeClient("chat")

    @Singleton
    @Provides
    private fun makeCatalogServiceClient(): CatalogServiceClient = makeClient("resilient-catalog-read-path")

    @Singleton
    @Provides
    private fun makeCatalogRedisClient(): RedisListingRepository =
        RedisListingRepository(getClientConfig("resilient-catalog-read-path"))

    @Singleton
    @Provides
    private fun makeTradeQualityClient(): TradeQualityBuyerRiskClient = makeClient("trade-quality-service")

    @Singleton
    @Provides
    private fun makeOrderProfilingClient(): OrderProfilingClient = makeClient("trade-quality-service")

    @Singleton
    @Provides
    private fun makeBusinessConfigClient(): BusinessConfigClient {
        return ConfigClientFactory.getBusinessConfigClientV2(
            config = getClientConfig("config-service"),
            moduleName = "cart-service"
        )
    }

    @Singleton
    @Provides
    private fun tradeCreditClient(): TradeCreditClient = makeClient("trade-credit-service")


    @Singleton
    @Provides
    private fun getVerticalCache(catalogServiceClient: CatalogServiceClient): VerticalCache {
        return VerticalCache(catalogServiceClient)
    }

    @Singleton
    @Provides
    private fun getCategoryTreeV2(catalogServiceClient: CatalogServiceClient) = CategoryTreeV2(catalogServiceClient)

    private fun getClientConfig(configKey: String): UdaanClientConfig {
        return UdaanServerConfig[configKey]!!
    }

    private inline fun <reified T : UdaanServiceClient> makeClient(configKey: String): T {
        val config: UdaanClientConfig = getClientConfig(configKey)
        return T::class.java.getDeclaredConstructor(UdaanClientConfig::class.java).newInstance(config)
    }
}
