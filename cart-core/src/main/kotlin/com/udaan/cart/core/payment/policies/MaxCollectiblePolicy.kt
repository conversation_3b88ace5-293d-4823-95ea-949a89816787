package com.udaan.cart.core.payment.policies

import com.google.inject.Singleton
import com.udaan.cart.core.payment.PaymentEvalContext

@Singleton
class MaxCollectiblePolicy : AbstractPrePaymentPolicy() {
    override fun getName(): String {
        TODO("Not yet implemented")
    }

    override suspend fun evaluate(paymentContext: PaymentEvalContext): PrePaymentResult {
        TODO("Not yet implemented")
    }
}
