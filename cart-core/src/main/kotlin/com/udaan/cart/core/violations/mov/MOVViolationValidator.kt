package com.udaan.cart.core.violations.mov

import arrow.core.*
import com.google.inject.Inject
import com.udaan.cart.core.common.helpers.BaseVerticals
import com.udaan.cart.core.common.helpers.HubExperiment
import com.udaan.cart.core.common.helpers.HubExperimentHelper
import com.udaan.cart.core.common.providers.HubProvider
import com.udaan.cart.core.common.providers.MOVProvider
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.context.CartContextKey
import com.udaan.cart.core.domain.models.DetailedCart
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.cart.core.metrics.CartEventTracker
import com.udaan.cart.core.utils.DropslotOrderFetcher
import com.udaan.cart.core.violations.MovViolationData
import com.udaan.cart.core.violations.ViolationData
import com.udaan.cart.core.violations.ViolationValidator
import com.udaan.cart.models.common.CategoryGroupSellerSelection
import com.udaan.catalog.client.CategoryGroupV2
import com.udaan.catalog.client.helpers.Category
import com.udaan.catalog.client.helpers.VerticalCache
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.config.client.BusinessConfigClient
import com.udaan.listing.builder.enrichments.catalog.CategoryConfigHelper
import com.udaan.model.orgs.OrgBusinessType
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withTimeout

class MOVViolationValidator @Inject constructor(
    private val movProvider: MOVProvider,
    private val categoryConfigHelper: CategoryConfigHelper,
    private val businessConfigClient: BusinessConfigClient,
    private val dropslotOrderFetcher: DropslotOrderFetcher,
    private val hubProvider: HubProvider,
    private val eventTracker: CartEventTracker,
    private val hubExperimentHelper: HubExperimentHelper,
    private val verticalCache: VerticalCache
) : ViolationValidator {
    companion object {
        private val logger by logger()
        private const val DROP_SLOT_CONFIG_KEY = "pacman-mov-drop-slot-feature"
        private const val FRESH_SLOT_CONFIG_KEY = "fresh-mov-drop-slot-feature"
        private const val HORECA_SLOT_CONFG_KEY = "horeca-mov-drop-slot-feature"
        private const val PHARMA_SLOT_CONFIG_KEY = "pharma-mov-drop-slot-feature"
        private const val HUB_BASED_SLOT_CONFIG_KEY = "enabled-hubs-mov-drop-slot-feature"
    }

    override suspend fun validate(
        cartContext: CartContext,
        detailedCart: DetailedCart?
    ): Either<NonEmptyList<String>, List<ViolationData>> {
        return kotlin.runCatching {
            val buyerOrgUnitId = cartContext.getValue<String>(CartContextKey.BUYER_ORG_UNIT_ID)
            val finalCart = detailedCart ?: cartContext.getDetailedCart(buyerOrgUnitId)
            // Check if cart contains only FOOD / FMCG / FOOD+FMCG
            val baseVertical = getBaseVerticalForCart(cartContext)
            val isFoodFMCGSeparateCart = hubExperimentHelper.isExperimentEnabled(cartContext = cartContext, experiment = HubExperiment.FMCG_CART )
            logger.info("isFoodFMCGSeparateCart: $isFoodFMCGSeparateCart")
            (finalCart?.let { cart ->
                val availableItems = cartContext.getAvailableItems(
                    existingCart = cart.cart,
                    buyerOrgUnitId = buyerOrgUnitId,
                    forceFetch = false
                )
                val listingIds = availableItems.map { item ->
                    (item.product as ListingProductItem).listingId
                }
                val categoryGroupId = cart.getCategoryGroupId()
                val sellerId = if (cart.cart.selection is CategoryGroupSellerSelection) {
                    cart.cart.selection.sellerId
                } else ""
                val movAmount = movProvider.find(
                    platformId = cart.cart.platformId,
                    categoryGroupId = categoryGroupId,
                    buyerId = cart.cart.buyerId,
                    sellerId = sellerId,
                    buyerOrgUnitId = cartContext.getValue(CartContextKey.BUYER_ORG_UNIT_ID),
                    listingIds = listingIds
                )
                // Note: This is hack for food_and_fmcg to support drop slot based MOV
                if (categoryGroupId == CategoryGroupV2.FoodAndFMCG.id || categoryGroupId == CategoryGroupV2.Pharma.id) {
                    val categories = cartContext.getListingsMap().values.parallelMap { listing ->
                        categoryConfigHelper.getCategory(listing, null, null)
                    }.mapNotNull { it }.toSet()
                    val dropSlotCategory = if (categories.size == 1) {
                        categories.firstOrNull() ?: ""
                    } else Category.FoodAndFMCG.id
                    val hasActiveOrders = if (isDropSlotBasedMOVAllowed(cartContext, dropSlotCategory)) {
                        logger.info("baseVertical for cart: $baseVertical")
                        val ordersInDropSlot = dropslotOrderFetcher.fetchActiveOrdersInDropSlot(
                            buyerId = cart.cart.buyerId,
                            buyerOrgUnitId = cartContext.getValue(CartContextKey.BUYER_ORG_UNIT_ID),
                            listingsMap = cartContext.getListingsMap(),
                            isFoodFMCGSeparateCart = isFoodFMCGSeparateCart,
                            baseVertical = baseVertical
                        )
                        logger.info("ordersInDropSlot: $ordersInDropSlot")
                        ordersInDropSlot.isNotEmpty()
                    } else false
                    val cartValue = cart.totalAmountForItems(availableItems)
                    if (cartContext.getValue<Boolean>(CartContextKey.CHECKOUT_CALL) == true) {
                        trackMovInCheckout(
                            buyerId = cart.cart.buyerId,
                            cartId = cart.cart.id,
                            cartValue = cartValue,
                            mov = movAmount,
                            dropslotOrderExists = hasActiveOrders
                        )
                    }
                    // If FOOD & FMCG cart is separate, we need to check the MOV for FOOD and FMCG separately
                    // In those cases hasActiveOrders will have orders either for FOOD or FMCG
                    if (hasActiveOrders) {
                        emptyList()
                    } else checkMOVAmount(cartValue, movAmount)
                } else {
                    checkMOVAmount(cart.totalAmountForItems(availableItems), movAmount)
                }
            } ?: emptyList()).right()
        }.getOrElse { e ->
            logger.error("Failed to check minimum order total required: ", e)
            nonEmptyListOf("Unable to validate minimum order total required").left()
        }
    }

    /**
     * Determines the base vertical of the cart based on the listings in the cart context.
     *
     * The method fetches all distinct verticals from the cart's listings and then evaluates
     * each vertical to identify its associated base vertical. Priority is given to "FMCG"
     * and "Food" base verticals, in that order. If no matching base vertical is found, null
     * is returned.
     *
     * @param cartContext the cart context containing details about the cart, listings, and related metadata
     * @return the base vertical of the cart as a string, or null if no base vertical could be determined
     */
    private suspend fun getBaseVerticalForCart(cartContext: CartContext): String? {
        val listingMap = cartContext.getListingsMap()
        val verticals = listingMap.values.map { it.vertical }.distinct()
        val baseVertical = verticals.map {
            val baseVerticalList = verticalCache.getVerticalAsync(it).await().baseVerticalList
            when {
                baseVerticalList.any { vertical -> vertical.baseVerticalName.lowercase() == BaseVerticals.FMCG.lowercase() } -> BaseVerticals.FMCG
                baseVerticalList.any { vertical -> vertical.baseVerticalName.lowercase() == BaseVerticals.Food.lowercase() } -> BaseVerticals.Food
                else -> {""}
            }
        }.firstOrNull()

        return baseVertical
    }

    private fun checkMOVAmount(
        cartTotal: Long,
        movAmount: Long,
    ): List<ViolationData> {
        return if (cartTotal < movAmount) {
            logger.info("[checkMOVAmount] MOV violation occurred. cartTotal = $cartTotal movAmount = $movAmount")
            listOf(
                MovViolationData(
                    amountInPaise = movAmount
                )
            )
        } else emptyList()
    }

    private suspend fun isDropSlotBasedMOVAllowed(cartContext: CartContext, category: String): Boolean {
        return kotlin.runCatching {
            val orgId = cartContext.getBuyerOrg()?.orgAccount?.orgId ?: ""
            val isHorecaBuyer = cartContext.getBuyerOrgIdentityModel()?.let {
                it.businessType == OrgBusinessType.HORECA
            } ?: false
            val buyerOrgUnit = cartContext.getBuyerOrgUnit(cartContext.getValue(CartContextKey.BUYER_ORG_UNIT_ID))
            val platformId = cartContext.getCart()?.platformId
            val isDropSlotMovEnabledForHub = if (null != buyerOrgUnit && null != platformId) {
                val hubOrgUnitId = hubProvider.getHubOrgUnitIdForBuyer(
                    category = category,
                    platform = platformId,
                    pincode = buyerOrgUnit.unitAddress.pincode,
                    buyerOrgUnitId = buyerOrgUnit.orgUnitId)
                hubOrgUnitId?.let {
                    val isDropSlotMovEnabledForHub =
                        businessConfigClient.sIsMemberAsync(HUB_BASED_SLOT_CONFIG_KEY, hubOrgUnitId).await()
                    logger.info("MOV Violations check for org: $orgId hubOrgUnitId $hubOrgUnitId" +
                        " isDropSlotMovEnabledForHub: $isDropSlotMovEnabledForHub")
                    isDropSlotMovEnabledForHub
                } ?: false
            } else {
                false
            }

            val isDropSlotBasedMOVAllowed = withTimeout(1000) {
                when {
                    isHorecaBuyer -> {
                        val isHorecaDropSlotMovAllowed = businessConfigClient.getBooleanAsync(
                            HORECA_SLOT_CONFG_KEY).await()
                        logger.info("MOV Violations check for org: $orgId" +
                            " isHorecaDropSlotMovAllowed: $isHorecaDropSlotMovAllowed")
                        isHorecaDropSlotMovAllowed
                    }
                    isDropSlotMovEnabledForHub -> isDropSlotMovEnabledForHub
                    category == Category.Fresh.id -> businessConfigClient.getBooleanAsync(FRESH_SLOT_CONFIG_KEY).await()
                    category == Category.Pharma.id -> {
                        val isPharmaDropSlotMovAllowed = businessConfigClient.getBooleanAsync(
                            PHARMA_SLOT_CONFIG_KEY).await() ?: false

                        logger.info("MOV Violations check for org: $orgId" +
                            " isPharmaDropSlotMovAllowed: $isPharmaDropSlotMovAllowed")

                        isPharmaDropSlotMovAllowed
                    }
                    else -> businessConfigClient.getBooleanAsync(DROP_SLOT_CONFIG_KEY).await()
                } ?: false
            }
            logger.info("[isDropSlotBasedMOVAllowed] isDropSlotBasedMOVAllowed = $isDropSlotBasedMOVAllowed")
            return isDropSlotBasedMOVAllowed
        }.getOrElse { e ->
            logger.error("Failed to check drop slot based MOV flag for category${category} ${e.message}: ", e)
            false
        }
    }

    private suspend fun trackMovInCheckout(
        buyerId: String,
        cartId: String,
        cartValue: Long,
        mov: Long,
        dropslotOrderExists: Boolean
    ) {
        eventTracker.trackMov(buyerId, cartId, cartValue, mov, dropslotOrderExists)
    }
}

