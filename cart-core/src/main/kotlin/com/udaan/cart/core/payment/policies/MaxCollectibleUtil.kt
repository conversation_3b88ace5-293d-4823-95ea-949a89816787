package com.udaan.cart.core.payment.policies

import com.udaan.common.utils.kotlin.logger

private data class CODRange(
    val minAmount: Long,
    val maxAmount: Long,
    val percentage: Long,
    val message: String,
)

data class CollectibleInfo(
    val bps: Int,
    val message: String,
)

object MaxCollectibleUtil {
    private val logger by logger()
    private val ranges = listOf(
        CODRange(
            minAmount = 199000 * 100L,
            maxAmount = Long.MAX_VALUE,
            percentage = 0L, // apply difference
            message = "You are placing a high value order, maximum cash on delivery allowed is 199000 only. " +
                    "Kindly pre-pay order value",
        )
    )

    fun evaluateCollectibleInfo(
        orderValueInPaise: Long,
        additionalCollectibleAmountInPaise: Long = 0L,
    ): CollectibleInfo? {
        val totalCollectibleAmount = orderValueInPaise + additionalCollectibleAmountInPaise
        return ranges.find { range ->
            totalCollectibleAmount >= range.minAmount && totalCollectibleAmount < range.maxAmount
        }?.let { range ->
            val bps = if (range.maxAmount == Long.MAX_VALUE) {
                HUNDRED_PERCENT_BPS
            } else {
                range.percentage.toInt()
            }
            CollectibleInfo(
                bps = bps,
                message = range.message,
            )
        }
    }
}
