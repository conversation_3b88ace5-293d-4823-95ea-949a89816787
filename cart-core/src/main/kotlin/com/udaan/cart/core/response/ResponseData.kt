package com.udaan.cart.core.response

import com.udaan.cart.core.operations.v1.contexts.PaymentMethodsContext
import com.udaan.cart.models.default.PaymentMethodsReqDto

enum class ResponseBuilderDataKey {
    COUPON_IDS,
    PAYMENT_METHODS_CONTEXT,
}

typealias ResponseBuilderData = MutableMap<ResponseBuilderDataKey, Any>

object ResponseDataBuilder {
    fun create(): ResponseBuilderData {
        return mutableMapOf()
    }
}

fun ResponseBuilderData.setCouponIds(couponIds: List<String>): ResponseBuilderData {
    this[ResponseBuilderDataKey.COUPON_IDS] = couponIds
    return this
}

fun ResponseBuilderData.getCouponIds(): List<String> {
    return this[ResponseBuilderDataKey.COUPON_IDS]?.let {
        it as List<String>
    } ?: emptyList()
}

fun ResponseBuilderData.setPaymentMethodsContext(
    context: PaymentMethodsContext<PaymentMethodsReqDto>
): ResponseBuilderData {
    this[ResponseBuilderDataKey.PAYMENT_METHODS_CONTEXT] = context
    return this
}

fun ResponseBuilderData.getPaymentMethodsContext(): PaymentMethodsContext<PaymentMethodsReqDto>? {
    return this[ResponseBuilderDataKey.PAYMENT_METHODS_CONTEXT]?.let {
        it as PaymentMethodsContext<PaymentMethodsReqDto>
    }
}
