package com.udaan.cart.core.violations

import arrow.core.*
import com.google.inject.Inject
import com.udaan.cart.core.common.providers.MOQProvider
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.context.CartContextKey
import com.udaan.cart.core.domain.models.CartItem
import com.udaan.cart.core.domain.models.DetailedCart
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.common.utils.kotlin.logger
import com.udaan.orderform.common.models.ListingData
import com.udaan.orderform.common.providers.InventoryProvider
import com.udaan.proto.models.ModelV1

class MOQViolationValidator @Inject constructor(
    private val moqProvider: MOQProvider,
    private val inventoryProvider: InventoryProvider
) : ViolationValidator {
    companion object {
        private val logger by logger()
    }

    override suspend fun validate(
        cartContext: CartContext,
        detailedCart: DetailedCart?
    ): Either<NonEmptyList<String>, List<MoqViolationData>> {
        return kotlin.runCatching {
            val finalCart = detailedCart?.cart ?: cartContext.getCart()
            (finalCart?.let { cart ->
                val listingsMap = cartContext.getListingsMap()
                val buyerOrgUnitId = cartContext.getValue<String>(CartContextKey.BUYER_ORG_UNIT_ID)
                val availableItems = cartContext.getAvailableItems(
                    existingCart = cart,
                    buyerOrgUnitId = buyerOrgUnitId,
                    forceFetch = false
                )
                availableItems.mapNotNull { item ->
                    item.product as ListingProductItem
                    (listingsMap[item.product.listingId]?.let { listing ->
                        moqProvider.find(listing, item.product.salesUnitId)
                    } ?: moqProvider.find(item.product.listingId, item.product.salesUnitId)).run {
                        if (item.quantity < this.listingMoq) {
                            item.getAvailableQty(cartContext)?.let {
                                if (it < this.listingMoq) {
                                    logger.info(
                                        "MOQ violation skipped due to low inventory $it for " +
                                        "listingId: ${item.product.listingId}, quantity: ${item.quantity}, " +
                                        "listingMoq: ${this.listingMoq}"
                                    )
                                    return@run null
                                }
                            }
                            MoqViolationData(
                                type = MoqViolationType.MOQ_LISTING,
                                listingId = item.product.listingId,
                                salesUnitId = item.product.salesUnitId,
                                listingMoq = this.listingMoq,
                                salesUnitMoq = this.salesUnitMoq
                            )
                        } else if (item.quantity < this.salesUnitMoq) {
                            MoqViolationData(
                                type = MoqViolationType.MOQ_SALES_UNIT,
                                listingId = item.product.listingId,
                                salesUnitId = item.product.salesUnitId,
                                listingMoq = this.listingMoq,
                                salesUnitMoq = this.salesUnitMoq
                            )
                        } else null
                    }
                }
            } ?: emptyList()).right()
        }.getOrElse { e ->
            logger.error("Failed to check MOQ: ", e)
            nonEmptyListOf("Unable to validate max order quantity").left()
        }
    }

    /**
     * Computes the maximum available units for a given cart item based on inventory availability.
     *
     * @receiver The cart item for which available units need to be computed.
     * @param cartContext The context containing additional cart details.
     * @return The maximum units available for order or null if unavailable.
     */
    @Suppress("ReturnCount")
    private suspend fun CartItem.getAvailableQty(cartContext: CartContext): Long? {
        val listingId = (this.product as ListingProductItem).listingId
        val salesUnitId = this.product.salesUnitId
        val quantity = this.quantity
        val sellerOrgId = this.product.sellerId

        // Get buyer context information with null checks
        val buyerOrgUnitId = cartContext.getValue<String>(CartContextKey.BUYER_ORG_UNIT_ID)
            ?: run {
                logger.warn("Buyer Org Unit ID is missing from CartContext")
                return null
            }
        val buyerPincode = cartContext.getBuyerOrgUnit(buyerOrgUnitId)?.unitAddress?.pincode
            ?: run {
                logger.warn("Buyer Pincode is missing for Buyer Org Unit ID: $buyerOrgUnitId")
                return null
            }

        logger.info(
            "Fetching inventory for ListingID: $listingId, SellerOrgID: $sellerOrgId, " +
            "BuyerOrgUnitID: $buyerOrgUnitId, BuyerPincode: $buyerPincode"
        )

       return kotlin.runCatching {
            inventoryProvider.checkAvailabilityAsync(
                sellerOrgId = sellerOrgId,
                buyerOrgUnitId = buyerOrgUnitId,
                deliveryPincode = buyerPincode,
                platform = ModelV1.SellingPlatform.UDAAN_MARKETPLACE,
                listingsData = listOf(
                    ListingData(
                        listingId = listingId,
                        salesUnitId = salesUnitId,
                        quantity = quantity
                    )
                )
            ).await()[salesUnitId]?.availableQuantity.also {
                logger.info("Received inventory response for ListingID: $listingId Qty:$it")
            }
        }.getOrElse {
            logger.error("Unexpected error while fetching available qty for listing $listingId")
            null
        }
    }
}
