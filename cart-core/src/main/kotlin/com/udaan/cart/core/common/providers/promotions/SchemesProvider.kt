package com.udaan.cart.core.common.providers.promotions

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.common.helpers.ListingDetailsHelper
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.context.CartContextKey
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.common.utils.kotlin.logger
import com.udaan.rewards.client.RewardsCartClient
import com.udaan.rewards.models.*

/**
 * Interface for computing schemes applicable to a cart.
 */
interface SchemesProvider {
    /**
     * Computes applicable schemes for the given cart context.
     *
     * @param cartContext the context of the cart
     * @return a set of cart scheme details
     */
    suspend fun compute(
        cartContext: CartContext,
    ): Set<SchemeDetailsV2>
}

/**
 * Implementation of [SchemesProvider] that fetches applicable schemes using RewardsCartClient.
 */
@Singleton
class SchemesProviderImpl @Inject constructor(
    private val rewardsCartClient: RewardsCartClient,
    private val listingDetailsHelper: ListingDetailsHelper
) : SchemesProvider {

    companion object {
        private val logger by logger()
        private val VALID_SCHEME_REWARD_TYPES = setOf(RewardType.CASHBACK, RewardType.COUPON)
    }

    /**
     * Computes applicable schemes for the given cart context.
     *
     * @param cartContext the context of the cart
     * @return a set of cart scheme details
     */
    @Suppress("ReturnCount")
    override suspend fun compute(cartContext: CartContext): Set<SchemeDetailsV2> {
        logger.info("Starting computation of applicable schemes for cart.")

        val categoryGroupId = cartContext.getCart()?.getCategoryGroupId() ?: return emptySet<SchemeDetailsV2>().also {
            logger.warn("Cart does not have a category group ID.")
        }
        val cartId = cartContext.getCartId()
        val buyerOrgAccount = cartContext.getBuyerOrg() ?: return emptySet<SchemeDetailsV2>().also {
            logger.warn("Buyer organization account is not available.")
        }
        val buyerOrgUnit = cartContext.getBuyerOrgUnit(
            cartContext.getValue(CartContextKey.BUYER_ORG_UNIT_ID)
        ) ?: return emptySet<SchemeDetailsV2>().also {
            logger.warn("Buyer organization unit is not available.")
        }

        val buyerContext = BuyerContext(
            buyerOrgId = buyerOrgAccount.orgAccount.orgId,
            city = buyerOrgUnit.unitAddress.city,
            buyerOrgUnitId = buyerOrgUnit.orgUnitId
        )

        val cartRequest = CartRequest(
            cartId = cartId,
            buyerContext = buyerContext,
            categoryGroupId = categoryGroupId,
            lineItems = getLineItemsForRequest(cartContext)
        )

        return kotlin.runCatching {
            logger.info("Fetching applicable rewards for cart: $cartRequest")
            rewardsCartClient.getApplicableRewardsForCartV2(cartRequest).executeAwait().schemes
                .filter { VALID_SCHEME_REWARD_TYPES.contains(it.rewardTargetScheme.type)}.toSet()
                .also { schemes ->
                    logger.info("Filtered schemes: $schemes")
                }
        }.getOrElse { e ->
            logger.error("Failed to fetch schemes: ${e.message}", e)
            emptySet()
        }
    }

    /**
     * Prepares the line items for the cart request.
     *
     * @param cartContext the context of the cart
     * @return a list of line items
     */
    private suspend fun getLineItemsForRequest(cartContext: CartContext): List<LineItem> {
        logger.info("Preparing line items for cart request.")

        val detailedCart = cartContext.getDetailedCart(
            orgUnitId = cartContext.getValue(CartContextKey.BUYER_ORG_UNIT_ID),
            skipOOSItems = true,
        )
        val availableCartItems = cartContext.getAvailableItems(
            existingCart = cartContext.getCart(),
            buyerOrgUnitId = cartContext.getValue(CartContextKey.BUYER_ORG_UNIT_ID)
        )
        val listingDetails = listingDetailsHelper.prepareListingDetails(cartContext.getListingsMap())

        return availableCartItems.mapNotNull { cartItem ->
            if (cartItem.product !is ListingProductItem) {
                logger.info("Skipping non-listing product item: $cartItem")
                return@mapNotNull null
            }
            // skip freebie items
            if (cartItem.isFreebie()) return@mapNotNull null

            val listingItem = ListingItem(
                sellerOrgId = cartItem.product.sellerId,
                listingId = cartItem.product.listingId,
                salesUnitId = cartItem.product.salesUnitId,
            )

            LineItem(
                lineItemId = cartItem.id,
                itemDetail = listingItem,
                vertical = listingDetails.listingVerticalMap[cartItem.product.listingId] ?: "DUMMY_VERTICAL",
                brand = listingDetails.listingBrandMap[cartItem.product.listingId] ?: "DUMMY_BRAND",
                category = cartItem.product.categoryGroupId,
                units = cartItem.quantity,
                totalPreTaxAmountInPaise = cartItem.perUnitAmountInPaise * cartItem.quantity,
                totalTaxInPaise = detailedCart?.getItemTax(cartItem) ?: 0,
            ).also {
                logger.info("Prepared line item: $it")
            }
        }
    }
}
