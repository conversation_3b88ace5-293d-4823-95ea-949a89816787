package com.udaan.cart.core.operations.v1.validators

import arrow.core.*
import com.google.inject.Singleton
import com.udaan.cart.core.common.helpers.Validator
import com.udaan.cart.core.common.models.OpError
import com.udaan.proto.models.ModelV1

@Singleton
class OrgValidator : Validator<ModelV1.OrgAccount, Boolean> {
    companion object {
        private const val ORG_ENABLED = "ENABLED"
    }

    override suspend fun validate(
        data: ModelV1.OrgAccount
    ): Either<OpError, Boolean> {

        if (data.status != ORG_ENABLED) {
            return OpError(message = "Org is not enabled").left()
        }
        return true.right()
    }
}
