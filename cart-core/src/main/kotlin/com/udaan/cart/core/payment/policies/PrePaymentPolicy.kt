package com.udaan.cart.core.payment.policies

import com.udaan.cart.core.payment.PaymentEvalContext
import kotlin.math.roundToLong

const val BPS_CONVERTER = 100
const val TEN_PERCENT_BPS = 10 * BPS_CONVERTER
const val FIFTY_PERCENT_BPS = 50 * BPS_CONVERTER
const val HUNDRED_PERCENT_BPS = 100 * BPS_CONVERTER

interface PrePaymentPolicy {

    fun getName(): String
    suspend fun evaluate(paymentContext: PaymentEvalContext): PrePaymentResult
}

abstract class AbstractPrePaymentPolicy : PrePaymentPolicy {
    protected fun defaultResult(): PrePaymentResult {
        return PrePaymentResult(
            name = getName(),
            isApplicable = false,
            description = "",
            instruction = "",
            bps = 0L,
            payableAmountInPaise = 0L,
            remainingAmountInPaise = 0L,
        )
    }

    private fun calculateTotals(bps: Int, totalAmount: Long): CalculatedTotals {
        val payableAmount = roundOff(totalAmount, bps.toDouble() / 100)
        val remainingAmount = totalAmount - payableAmount
        return CalculatedTotals(
            payableAmountInPaise = payableAmount,
            remainingAmountInPaise = remainingAmount,
        )
    }

    private fun roundOff(amountInPaisa: Long, percentage: Double): Long {
        val prepayment: Double = (amountInPaisa * percentage) / 100
        val nearest100 = prepayment.div(100 * 100).roundToLong() * 100 * 100
        return if (nearest100 >= amountInPaisa || nearest100 <= 0) {
            prepayment.roundToLong()
        } else {
            nearest100
        }
    }

    protected fun prepareResult(
        bps: Int,
        totalAmount: Long,
        description: String?,
        instruction: String
    ): PrePaymentResult {
        val calculatedTotals = calculateTotals(bps, totalAmount)
        return PrePaymentResult(
            name = getName(),
            isApplicable = true,
            description = description,
            instruction = instruction,
            bps = bps.toLong(),
            payableAmountInPaise = calculatedTotals.payableAmountInPaise,
            remainingAmountInPaise = calculatedTotals.remainingAmountInPaise,
        )
    }
}

data class CalculatedTotals(
    val payableAmountInPaise: Long,
    val remainingAmountInPaise: Long,
)
