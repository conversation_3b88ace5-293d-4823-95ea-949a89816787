package com.udaan.cart.core.common.providers

import com.google.inject.Inject
import com.udaan.cart.core.common.providers.promotions.PromoContext
import com.udaan.cart.core.domain.models.CartItem
import com.udaan.cart.core.domain.models.DetailedCart
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.cart.models.common.PaymentCollectionType
import com.udaan.cart.models.default.CheckoutRequestDto
import com.udaan.cart.models.default.RequestExtraDataKeys
import com.udaan.common.utils.kotlin.logger
import com.udaan.order_mgt.models.PromoSource
import com.udaan.order_mgt.models.SellerOrderInternals.ExtraData.SelectedOnlinePaymentType
import com.udaan.order_mgt.models.pg.PaymentGatewayV1
import com.udaan.orderform.cart.models.dto.*
import com.udaan.orderform.client.OrderCheckoutClient
import com.udaan.promotions.dto.promotion.RewardClass
import com.udaan.promotions.dto.v2.promotion.PromotionDetails
import com.udaan.promotions.dto.v2.promotion.PromotionLevel

interface OrderCheckoutProvider {
    suspend fun checkout(
        detailedCart: DetailedCart,
        promoContext: PromoContext?,
        riderPromos: Map<String, Map<String, Int>>,
        request: CheckoutRequestDto,
        deliveryChargeCartIdentifier: String
    ): CheckoutDetails
}

class OrderCheckoutProviderImpl @Inject constructor(
    private val orderCheckoutClient: OrderCheckoutClient,
) : OrderCheckoutProvider {

    companion object {
        private val logger by logger()
    }

    override suspend fun checkout(
        detailedCart: DetailedCart,
        promoContext: PromoContext?,
        riderPromos: Map<String, Map<String, Int>>,
        request: CheckoutRequestDto,
        deliveryChargeCartIdentifier: String
    ): CheckoutDetails {
        val paymentModes = when (request.collectionType) {
            PaymentCollectionType.COD -> listOf(PaymentGatewayV1.PaymentInstrument.COD)
            PaymentCollectionType.ADVANCE -> listOf(PaymentGatewayV1.PaymentInstrument.ONLINE)
            PaymentCollectionType.TOKEN -> listOf(PaymentGatewayV1.PaymentInstrument.ONLINE)
            PaymentCollectionType.CREDIT -> listOf(PaymentGatewayV1.PaymentInstrument.CREDIT)
            PaymentCollectionType.TRADE_CREDIT -> listOf(PaymentGatewayV1.PaymentInstrument.CREDIT)
        }
        val selectedOnlinePaymentType = when (request.collectionType) {
            PaymentCollectionType.COD, PaymentCollectionType.CREDIT, PaymentCollectionType.TRADE_CREDIT ->
                SelectedOnlinePaymentType.None
            PaymentCollectionType.ADVANCE -> SelectedOnlinePaymentType.Advance
            PaymentCollectionType.TOKEN -> SelectedOnlinePaymentType.Token
        }
        val itemProducts = detailedCart.getCartItems(skipItemsWithPriceIssues = true).map { cartItem ->
            cartItem.product as ListingProductItem
            val prePromoCartItem = detailedCart.prePromoCart?.let { cart -> cart.items.find { it.id == cartItem.id } }
            ItemProduct(
                orgId = cartItem.product.sellerId,
                listingId = cartItem.product.listingId,
                salesUnitId = cartItem.product.salesUnitId,
                quantity = cartItem.quantity,
                unitPriceInPaise = cartItem.perUnitAmountInPaise,
                prePromoUnitPriceInPaise = prePromoCartItem?.perUnitAmountInPaise ?: cartItem.perUnitAmountInPaise,
                puInfo = cartItem.priceDetails.puInfo?.let { PUReqDto(cartItem.priceDetails.puInfo.userPU) },
                promotions = promoContext?.let {
                    preparePromoItems(cartItem, promoContext, riderPromos)
                } ?: emptyList(),
                creditTenure = cartItem.product.creditTenure,
                creditLineId = cartItem.product.creditLineId,
                auditPricingId = detailedCart.listingIdToPricingAuditIds["${cartItem.product.listingId}:${cartItem.product.salesUnitId}"]
            )
        }
        val isCPODPaymentSelected = (request.extraData[RequestExtraDataKeys.CPOD_EXP_ENABLED] as? Boolean) ?: false
        val checkoutReq = CheckoutReqDto(
            platformId = request.platformId,
            buyerOrgId = request.buyerId,
            buyerOrgUnitId = request.buyerOrgUnitId,
            products = itemProducts,
            requester = Requester(userId = request.requester.userId, orgId = request.requester.orgId),
            paymentModes = paymentModes,
            selectedOnlinePaymentType = selectedOnlinePaymentType,
            deliverySlotId = request.deliverySlotId,
            couponIds = promoContext?.getAppliedCouponIds() ?: emptyList(),
            useRewardsWorth = request.useRewardsWorth?.toLong(),
            instruction = request.instruction,
            billToOrgUnitId = request.billToOrgUnitId,
            itemLevelDeliverySlot = request.itemLevelDeliverySlot,
            additionalData = mapOf(
                AdditionalDataKey.CPOD_PAYMENT_SELECTED to isCPODPaymentSelected
            ),
            deliveryChargeCartIdentifier = deliveryChargeCartIdentifier
        )
        logger.info("Order checkout request: $checkoutReq")
        val checkoutResponse = orderCheckoutClient.checkout(checkoutReq).executeAwait()
        return CheckoutDetails(
            placedOrderIds = checkoutResponse.placedOrderIds,
            prePaymentId = checkoutResponse.prePaymentId,
            mainOrderId = checkoutResponse.mainOrderId,
            orderSlaMap = checkoutResponse.orderSlasMap,
        )
    }

    private fun preparePromoItems(
        cartItem: CartItem,
        promoContext: PromoContext,
        riderPromos: Map<String, Map<String, Int>>
    ): List<ItemPromotion> {
        val itemPromos = promoContext.getItemToDiscountMap()[cartItem.id]
        cartItem.product as ListingProductItem
        val pricingPromos = riderPromos[cartItem.id]?.let { pricingPromosMap ->
            pricingPromosMap.map { (promotionId, bps) ->
                ItemPromotion(
                    promotionId = promotionId,
                    discountBps = bps.toLong(),
                    rewardClass = RewardClass.DISCOUNT.name,
                    promoSource = PromoSource.PRICING,
                    rewardedItems = listOf(
                        RewardItem(
                            listingId = cartItem.product.listingId,
                            salesUnitId = cartItem.product.salesUnitId,
                        )
                    ),
                    isItemLevel = true,
                    parentRewardItem = null,
                )
            }
        } ?: emptyList()
        val allPricingPromos = (itemPromos?.map { (promotionId, discountDetails) ->
            val isItemLevel =
                promoContext.promosResponse.promotions.find { it.id == promotionId }?.level == PromotionLevel.ITEM
            ItemPromotion(
                promotionId = promotionId,
                discountBps = discountDetails.discountBps.toLong(),
                rewardClass = RewardClass.DISCOUNT.name,
                promoSource = PromoSource.PROMOTIONS,
                rewardedItems = listOf(
                    RewardItem(
                        listingId = cartItem.product.listingId,
                        salesUnitId = cartItem.product.salesUnitId,
                    )
                ),
                isItemLevel = isItemLevel,
                parentRewardItem = null,
            )
        } ?: emptyList()).plus(pricingPromos)

        val freebieCartLineToPromoMap = promoContext.getFreebieCartLineToPromoMap()
        val freebiePromos = freebieCartLineToPromoMap[cartItem.id]?.let {
            getFreebieItemPromotion(it, cartItem, promoContext.getTransformedItems())
        }.let { listOfNotNull(it) }

        return allPricingPromos + freebiePromos
    }

    private fun getFreebieItemPromotion(
        freebiePromotion: PromotionDetails,
        cartItem: CartItem,
        allCartItems: List<CartItem>
    ): ItemPromotion? {
        val isItemLevel = freebiePromotion.level == PromotionLevel.ITEM
        val parentCartItem = allCartItems.singleOrNull {
            it.id == (cartItem.product as ListingProductItem).freebieInfo?.parentCartLineId
        } ?: return null
        return ItemPromotion(
            promotionId = freebiePromotion.id,
            discountBps = 0,
            rewardClass = RewardClass.FREEBIE.name,
            promoSource = PromoSource.PROMOTIONS,
            rewardedItems = listOf(
                RewardItem(
                    listingId = (cartItem.product as ListingProductItem).listingId,
                    salesUnitId = cartItem.product.salesUnitId,
                )
            ),
            isItemLevel = isItemLevel,
            parentRewardItem = RewardItem(
                (parentCartItem.product as ListingProductItem).listingId,
                parentCartItem.product.salesUnitId
            ),
        )
    }
}

data class CheckoutDetails(
    val placedOrderIds: List<String>,
    val prePaymentId: String? = null,
    val mainOrderId: String? = null,
    val orderSlaMap: Map<String, OrderSLA>? = null
)
