package com.udaan.cart.core.utils

import com.udaan.model.orgs.BusinessClass
import com.udaan.model.orgs.OrgBusinessType
import com.udaan.model.orgs.OrgInternalIdentityModel
import com.udaan.model.orgs.toHorecaBusinessClass

enum class BuyerProfile {
    BAKERY,
    SMALL_KIRANA,
    REGULAR,
    FnV_STORE,
    HORECA_AB,
    HORECA_CD,
}

fun OrgInternalIdentityModel?.getBuyerProfile(): BuyerProfile {
    val isBakery = this?.businessSubType == "Bakery"
    val isSmallKirana = this?.businessSubType == "Condiments"
    val isFreshBuyer = this?.primaryBusinessCategory?.let {
        it.size == 1 && it.first() == "fresh"
    } ?: false
    val isHoreca = this?.businessType == OrgBusinessType.HORECA
    val isBusinessClassAB = this?.businessClass.toHorecaBusinessClass() in
            listOf(BusinessClass.ENTERPRISE, BusinessClass.LARGE)
    val isHorecaAB = isHoreca && isBusinessClassAB
    val isHorecaCD = isHoreca && isBusinessClassAB.not()

    return when {
        isHorecaAB -> BuyerProfile.HORECA_AB
        isHorecaCD -> BuyerProfile.HORECA_CD
        isBakery -> BuyerProfile.BAKERY
        isSmallKirana -> BuyerProfile.SMALL_KIRANA
        isFreshBuyer -> BuyerProfile.FnV_STORE
        else -> BuyerProfile.REGULAR
    }
}
