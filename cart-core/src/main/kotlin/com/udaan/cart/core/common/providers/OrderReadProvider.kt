package com.udaan.cart.core.common.providers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.orderservice.client.OrderGraphQLClient
import com.udaan.orderservice.client.fragments.ORDER_AMOUNT_SUMMARY_FRAGMENT
import com.udaan.orderservice.client.fragments.SELLER_ORDER_FRAGMENT
import com.udaan.orderservice.client.requests.GraphQLQueries
import com.udaan.orderservice.models.client.SellerOrderExtendedResponseDTO

interface OrderReadProvider {
    suspend fun getCodOrdersWithOrderValue(orderIds: List<String>): Map<String, Long>
}

@Singleton
class OrderReadProviderImpl @Inject constructor(
    private val orderGraphQLClient: OrderGraphQLClient
): OrderReadProvider {
    companion object {
        private val logger by logger()
        private const val COD_PAYMENT_METHOD = "COD"
        val ORDER_FRAGMENT_ORDER_AMOUNT = """
        {
            $SELLER_ORDER_FRAGMENT
            orderAmountSummary {
                $ORDER_AMOUNT_SUMMARY_FRAGMENT
            }
	    }
        """.trimIndent()
    }
    override suspend fun getCodOrdersWithOrderValue(orderIds: List<String>): Map<String, Long> {
        val orderDetails = getOrderDetailsByOrderIds(orderIds = orderIds)
        val codOrders = orderDetails.filter { it.extraData.selected_payment_method == COD_PAYMENT_METHOD }
        return codOrders.map { order ->
            order.orderId to (order.orderAmountSummary?.totalOrderAmountPaisa ?: 0L)
        }.toMap()
    }


    private suspend fun getOrderDetailsByOrderIds(orderIds: List<String>): List<SellerOrderExtendedResponseDTO> {
        try {
            val orderIdsList = orderIds.joinToString(separator = "\", \"", prefix = "[\"", postfix = "\"]")
            val graphqlQuery = """
            {
                ${GraphQLQueries.SELLER_ORDER_BY_IDS}(orderIds: $orderIdsList, isReadReplica: false) 
                $ORDER_FRAGMENT_ORDER_AMOUNT
            }
            """.trimIndent()
            logger.info("[OrderReadProvider][getOrderDetailsByOrderIds] graphqlQuery = $graphqlQuery")
            return orderGraphQLClient.executeBulkOrderGraphQL(graphqlQuery, GraphQLQueries.SELLER_ORDER_BY_IDS)
                .executeAwait(3)
        } catch (ex: Exception) {
            logger.error("[OrderReadProvider][getOrderDetailsByOrderIds] Error : $ex")
            throw ex
        }
    }
}