package com.udaan.cart.core.payment.policies

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.config.client.BusinessConfigClient
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withTimeout

@Singleton
class PolicyExemptionValidator @Inject constructor(
    private val objectMapper: ObjectMapper,
    private val businessConfigClient: BusinessConfigClient,
) {
    companion object {
        private val logger by logger()
        private const val PREPAYMENT_POLICY_EXEMPTIONS_KEY = "prepayment-policy-exemptions"
        private const val TIMEOUT_MILLIS_DEFAULT = 10L
    }

    suspend fun isExempted(
        policyName: String,
        buyerId: String,
    ): Boolean {
        return getExemptionConfig()?.let { config ->
            val exemptionStatus = config.exemptions.any {
                it.policyName == policyName && it.buyers.contains(buyerId)
            }
            logger.info("exemptionStatus for $policyName, $buyerId is $exemptionStatus")
            exemptionStatus
        } ?: false
    }

    private suspend fun getExemptionConfig(): ExemptionConfig? {
        return kotlin.runCatching {
            withTimeout(TIMEOUT_MILLIS_DEFAULT) {
                businessConfigClient.getStringAsync(PREPAYMENT_POLICY_EXEMPTIONS_KEY).await()?.let { config ->
                    objectMapper.readValue(config, ExemptionConfig::class.java)
                }
            }
        }.getOrElse { e ->
            logger.error("Failed to load exemption config: ${e.message}", e)
            ExemptionConfig(exemptions = emptyList())
        }
    }
}

private data class Exemption(
    val policyName: String,
    val buyers: List<String>,
)

private data class ExemptionConfig(
    val exemptions: List<Exemption>,
)