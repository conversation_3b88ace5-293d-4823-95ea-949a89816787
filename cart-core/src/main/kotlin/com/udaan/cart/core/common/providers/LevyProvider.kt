package com.udaan.cart.core.common.providers

import arrow.core.*
import arrow.core.right
import com.google.inject.Inject
import com.udaan.cart.core.domain.models.ItemLevy
import com.udaan.catalog.client.CategoryGroupV2
import com.udaan.catalog.models.ModelV2.TradeListing
import com.udaan.common.utils.getCurrentMillis
import com.udaan.compliance.api.requests.TcsItTaxRateRequestV2
import com.udaan.compliance.client.ComplianceServiceClient
import com.udaan.invoicing.math.LineItemPriceDetails
import com.udaan.invoicing.models.AdditionalCess
import com.udaan.invoicing.models.AdditionalCessType
import com.udaan.invoicing.models.SupplyType
import com.udaan.proto.models.ModelV1.SellingPlatform
import java.math.BigDecimal
import kotlin.collections.flatMap

data class LevyItemReq(
    val id: String,
    val listingId: String,
    val salesUnitId: String,
    val quantity: Int,
    val amountInPaise: Long,
)

interface LevyProvider {
    suspend fun getLevies(
        buyerId: String,
        levyItemReqs: List<LevyItemReq>,
        listingsMap: Map<String, TradeListing>,
        categoryGroupId: String,
        platformId: SellingPlatform,
    ): Either<NonEmptyList<String>, Map<String, List<ItemLevy>>>
}

class LevyProviderImpl @Inject constructor(
    private val complianceServiceClient: ComplianceServiceClient,
) : LevyProvider {
    override suspend fun getLevies(
        buyerId: String,
        levyItemReqs: List<LevyItemReq>,
        listingsMap: Map<String, TradeListing>,
        categoryGroupId: String,
        platformId: SellingPlatform,
    ): Either<NonEmptyList<String>, Map<String, List<ItemLevy>>> {
        // Ensure all the listings are present
        if (levyItemReqs.groupBy { it.listingId }.count() != listingsMap.size) {
            nonEmptyListOf("Listings are not matching items").left()
        }

        val listingsByOrg = listingsMap.values.groupBy { it.orgId }
        return listingsByOrg.flatMap { (orgId, listings) ->
            val tcsItRate = getTcsItTaxRateFor(orgId, buyerId, platformId.name)
            listings.flatMap { listing ->
                levyItemReqs.filter { it.listingId == listing.listingId }.flatMap { levyItemReq ->
                    prepareLevies(
                        supplyType = findSupplyType(categoryGroupId),
                        tcsItRate = tcsItRate,
                        levyItemReq = levyItemReq,
                        listing = listing,
                    )
                }
            }
        }.groupBy { it.refId }.right()
    }

    /**
     * Supply has to be determined based on seller org unit(WH) and buyer delivery org unit.
     * Since seller org unit can't be determined before placing order, falling back to category group based solution
     */
    private fun findSupplyType(categoryGroupId: String): SupplyType {
        return when (categoryGroupId) {
            CategoryGroupV2.FoodAndFMCG.id, CategoryGroupV2.Pharma.id -> SupplyType.INTRA_STATE
            else -> SupplyType.INTER_STATE
        }
    }

    private fun prepareLevies(
        supplyType: SupplyType,
        tcsItRate: BigDecimal,
        levyItemReq: LevyItemReq,
        listing: TradeListing
    ): List<ItemLevy> {
        val taxBps = listing.taxDetails.gstBps
        val cessBps = listing.taxDetails.cessBps

        val salesUnit = listing.salesUnitList.firstOrNull { it.salesUnitId == levyItemReq.salesUnitId }
        val additionalCess = salesUnit?.taxDetails?.additionalCess

        val lineItem = LineItemPriceDetails.fromTaxableAmount(
            priceExclusiveOfAllLevies = levyItemReq.amountInPaise,
            gstBps = taxBps,
            supplyType = supplyType,
            cessBps = cessBps,
            marketFeesBps = 0,
            tcsItRate = tcsItRate,
            qty = levyItemReq.quantity.toLong(),
            acess = if (additionalCess != null) {
                AdditionalCess(
                    type = AdditionalCessType.valueOf(additionalCess.type.name),
                    rate = additionalCess.value.toLong()
                )
            } else null
        )
        return lineItem.nonZeroLevies.map { (levyType, levy) ->
            ItemLevy(
                refId = levyItemReq.id,
                levyAmountPaise = levy.levyInPaise,
                levyBps = levy.levyBps,
                levyType = levyType,
                listingId = levyItemReq.listingId,
                salesUnitId = levyItemReq.salesUnitId,
            )
        }
    }

    private suspend fun getTcsItTaxRateFor(
        sellerId: String,
        buyerId: String,
        platformId: String?
    ): BigDecimal {
        return if (platformId == SellingPlatform.UDAAN_MANDI.name) {
            complianceServiceClient.getTcsItTaxRateForV2(
                TcsItTaxRateRequestV2(
                    sellerOrgId = sellerId,
                    buyerOrgId = buyerId,
                    eventTimestamp = getCurrentMillis()
                )
            ).executeAwait().tcsItTaxRate
        } else {
            BigDecimal.ZERO
        }
    }
}
