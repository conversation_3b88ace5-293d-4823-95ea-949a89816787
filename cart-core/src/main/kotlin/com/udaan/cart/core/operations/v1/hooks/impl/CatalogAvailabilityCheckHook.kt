package com.udaan.cart.core.operations.v1.hooks.impl

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.common.helpers.ConfigServiceHelper
import com.udaan.cart.core.common.helpers.HubExperimentHelper
import com.udaan.cart.core.common.helpers.isMeat
import com.udaan.cart.core.common.models.InventoryListingDetails
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.common.providers.HubProvider
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.cart.core.hooks.BaseOperationHook
import com.udaan.cart.core.operations.v1.contexts.EditCartContext
import com.udaan.cart.models.common.ListingProduct
import com.udaan.cart.models.default.EditCartReqDto
import com.udaan.catalog.client.CatalogServiceClient
import com.udaan.catalog.client.helpers.VerticalCache
import com.udaan.catalog.models.ModelV2.TradeListing
import com.udaan.catalog.representations.ApiV2
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.config.client.BusinessConfigClient
import com.udaan.proto.models.ModelV1
import kotlinx.coroutines.future.await

@Singleton
class CatalogAvailabilityCheckHook @Inject constructor(
    private val catalogServiceClient: CatalogServiceClient,
    private val verticalCache: VerticalCache,
    private val hubProvider: HubProvider,
    private val configServiceHelper: ConfigServiceHelper,
    private val objectMapper: ObjectMapper,
) : BaseOperationHook<OpError, EditCartContext<EditCartReqDto>> {

    companion object {
        private val logger by logger()
        private const val catalogBatch = 5
        private const val PARALLELISM_FACTOR = 10
        private const val MEAT_TO_FIRST_PARTY_EXP = "meat-to-first-party-experiment"
    }

    @Suppress("LongMethod")
    override suspend fun execute(
        context: EditCartContext<EditCartReqDto>
    ): Either<OpError, EditCartContext<EditCartReqDto>> {
        val listingsMap = context.getListingsMap()
        return kotlin.runCatching {
            val firstPartyMigration = meatMigrationToFirstPartyExpEnabled(context)
            if (firstPartyMigration) {
                logger.info("[CatalogAvailabilityCheckHook] Meat availability check is moved to first party " +
                    "for this hub")
                return context.right()
            }
            context.getCartContext()?.getCart()?.let { cart ->
                val inventoryDetails = context.request.products
                    .filterIsInstance<ListingProduct>()
                    .filter { it.quantity > 0 }
                    .filter { isMeatVertical(it, listingsMap) }
                    .mapNotNull { product ->
                        // Find an existing cart item matching the current product's listingId and salesUnitId
                        val existingCartItem = cart.items.find { cartItem ->
                            cartItem.product as ListingProductItem
                            cartItem.product.listingId == product.listingId &&
                                cartItem.product.salesUnitId == product.salesUnitId
                        }
                        when {
                            existingCartItem != null -> {
                                val cartItemProduct = existingCartItem.product as ListingProductItem
                                // Only do the inventory check if the request quantity is greater than the cart quantity
                                if (product.quantity > cartItemProduct.quantity) {
                                    InventoryListingDetails(
                                        orgId = "",
                                        listingId = cartItemProduct.listingId,
                                        salesUnitId = cartItemProduct.salesUnitId,
                                        quantity = product.quantity
                                    )
                                } else {
                                    null
                                }
                            }
                            else -> {
                                // Item doesn't exist in cart, check for inventory availability for this new item
                                InventoryListingDetails(
                                    orgId = "",
                                    listingId = product.listingId,
                                    salesUnitId = product.salesUnitId,
                                    quantity = product.quantity
                                )
                            }
                        }
                    }
                val unavailabilityMessage = inventoryDetails.chunked(catalogBatch).map { inventoryDetailsChuck ->
                    val availabilityRequest = inventoryDetailsChuck.map { inventoryDetail ->
                        ApiV2.AvailabilityRequest.newBuilder().let {
                            it.setListingId(inventoryDetail.listingId).setSalesUnitId(inventoryDetail.salesUnitId)
                                .setNumUnits(inventoryDetail.quantity)
                            it.build()
                        }
                    }
                    catalogServiceClient.checkAvailability(
                        ApiV2.AvailabilityBatchRequest.newBuilder().addAllRequest(availabilityRequest).build()
                    ).execute()
                }.parallelMap(PARALLELISM_FACTOR) { batchResponseFuture ->
                    val notFoundList = batchResponseFuture.await().notFoundIdList
                    require(notFoundList.size == 0) { "Found invalid sales unit ids: $notFoundList" }
                    batchResponseFuture.await().responseMap.entries.filterNot { it.value.isAvailable }.map { entry ->
                        listingsMap[entry.value.listingId]?.let {
                            it.generatedTitle.ifEmpty { it.title }
                        } ?: ""
                    }
                }.flatten().filter { it.isNotEmpty() }.joinToString().let { listingTitles ->
                    if (listingTitles.isNotEmpty()) {
                        "Requested inventory is not available for $listingTitles"
                    } else ""
                }
                if (unavailabilityMessage.isNotEmpty()) {
                    logger.warn(
                        "CatalogAvailabilityCheckHook::Listings are unavailable, " +
                        "unavailabilityMessage-> $unavailabilityMessage"
                    )
                    OpError(message = unavailabilityMessage).left()
                } else context.right()
            } ?: context.right()
        }.getOrElse { e ->
            logger.error("Failed to check inventory while editing cart: ${e.message}", e)
            context.right()
        }
    }

    // Call catalog API for inventory availability check only for meat category
    private suspend fun isMeatVertical(
        listingProduct: ListingProduct,
        listingMap: Map<String, TradeListing>
    ): Boolean {
        val listing = listingMap.filter { it.key == listingProduct.listingId }.values.first()
        val verticalName = listing.vertical
        val vertical = verticalCache.getVerticalAsync(verticalName).await()
        return vertical.isMeat()
    }

    private suspend fun meatMigrationToFirstPartyExpEnabled(context: EditCartContext<EditCartReqDto>): Boolean {
        return try {
            context.getCartContext()?.let { cartContext ->
                val buyerOrgUnit = cartContext.getBuyerOrgUnit(context.request.buyerOrgUnitId)
                val platformId = cartContext.getCart()?.platformId
                if (null != buyerOrgUnit && null != platformId) {
                    val buyerHub = hubProvider.getHubOrgUnitIdForBuyer(
                        category = "meat",
                        platform = platformId,
                        pincode = buyerOrgUnit.unitAddress.pincode,
                        buyerOrgUnitId = buyerOrgUnit.orgUnitId
                    )
                    buyerHub?.let {
                        logger.info("[CatalogAvailabilityCheckHook] BuyerHub = $it")
                        isHubPartOfExperiment(it)
                    } ?: run {
                        logger.info("[CatalogAvailabilityCheckHook] Unable to find hub info for pincode " +
                            "${buyerOrgUnit.unitAddress.pincode} and orgUnitId ${buyerOrgUnit.orgUnitId}")
                        false
                    }
                } else {
                    logger.info("[CatalogAvailabilityCheckHook] Unable to find buyerOrgUnit or platform details for " +
                        "the buyer ")
                    false
                }
            } ?: false
        } catch (ex: Exception) {
            logger.info("[CatalogAvailabilityCheckHook] Exception while verifying if buyerHub is part " +
                "of experiment - ${ex.message} - $ex")
            false
        }
    }

    private suspend fun isHubPartOfExperiment(hubId: String): Boolean {
        return kotlin.runCatching {
            val config = configServiceHelper.getValueByConfigName(MEAT_TO_FIRST_PARTY_EXP)?.let { config ->
                objectMapper.readValue(config, ConfigManager::class.java)
            }
            val configsMatchingHubs =
                config?.configList?.filter { it.hubIds.contains(hubId) || it.hubIds.contains("ALL") } ?: emptyList()
            return if (configsMatchingHubs.isNotEmpty()) {
                val currentTime = System.currentTimeMillis()
                configsMatchingHubs.firstOrNull { hubConfig ->
                    val endCondition = hubConfig.endTime?.let { it > currentTime } ?: true
                    hubConfig.startTime < currentTime && endCondition
                } != null
            } else {
                false
            }
        }.getOrElse { exception ->
            logger.error("Failed to read meat migration to first party config", exception)
            false
        }
    }

    private data class ConfigManager(
        val configList: List<Config>
    )
    private data class Config(
        val hubIds: List<String>,
        val startTime: Long,
        val endTime: Long?
    )
}
