package com.udaan.cart.core.operations.v1.validators

import arrow.core.Either
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.common.helpers.Validator
import com.udaan.cart.core.common.models.OpError
import com.udaan.proto.representations.OrgV1

@Singleton
class BuyerOrgValidator @Inject constructor(
    private val orgValidator: OrgValidator
) : Validator<OrgV1.OrgAccountExtendedResponse, Boolean> {
    override suspend fun validate(
        data: OrgV1.OrgAccountExtendedResponse
    ): Either<OpError, Boolean> = orgValidator.validate(data.orgAccount).mapLeft { error ->
        OpError(message = "Buyer $error")
    }
}
