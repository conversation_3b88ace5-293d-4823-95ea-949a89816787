package com.udaan.cart.core.domain.models

import com.udaan.cart.core.payment.RewardBasedPayment

enum class PaymentCollectionType {
    ADVANCE,
    TOKEN,
    COD,
    CREDIT,
    TRADE_CREDIT
}

enum class CreditPayType {
    CREDIT,
    CREDIT_BUY,
    CREDIT_PAY_LATER,
}


data class PaymentMethod(
    val collectionType: PaymentCollectionType,
    val payableTotalInPaise: Long,
    val remainingTotalInPaise: Long,
    val prepaymentBps: Long = 0,
    val description: String? = null,
    val instruction: String = "",
    val metaData: Map<String, Any> = emptyMap(),
    val rewardBasedPayment: RewardBasedPayment? = null,
) {
    companion object {
        private const val CREDIT = "Credit"
        private const val CREDIT_BUY = "CreditBuy"
        private const val CREDIT_PAY_LATER = "PayLater"
        private const val CREDIT_INSTRUMENT = "CREDIT"
        private const val CREDIT_INSTRUMENT_PAY_LATER = "CREDIT_PAY_LATER"
        private val creditTypeMap = mapOf(
            CreditPayType.CREDIT_BUY to CREDIT_BUY,
            CreditPayType.CREDIT_PAY_LATER to CREDIT_PAY_LATER,
            CreditPayType.CREDIT to CREDIT,
        )
        private const val PAY_LATER_KEY = "pay_later"
    }

    fun getCreditType(): String? {
        return if (collectionType == PaymentCollectionType.CREDIT) {
            val payLaterStatus = kotlin.runCatching {
                metaData.getOrDefault(PAY_LATER_KEY, "false").toString().toBoolean()
            }.getOrElse {
                false
            }
            if (payLaterStatus) {
                creditTypeMap[CreditPayType.CREDIT]
            } else creditTypeMap[CreditPayType.CREDIT_BUY]
        } else null
    }

    fun getCreditInstrumentId(): String? {
        return if (collectionType == PaymentCollectionType.CREDIT) {
            val payLaterStatus = kotlin.runCatching {
                metaData.getOrDefault(PAY_LATER_KEY, "false").toString().toBoolean()
            }.getOrElse {
                false
            }
            if (payLaterStatus) {
                CREDIT_INSTRUMENT_PAY_LATER
            } else CREDIT_INSTRUMENT
        } else null
    }

    fun getDefaultCreditType(): String = CREDIT_BUY
}
