package com.udaan.cart.core.operations.v1.hooks

import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.BaseContext
import com.udaan.cart.core.hooks.Hook

data class EditProductHookCollection<E: OpError, T : BaseContext>(
    val doValidationHooks: List<Hook<E, T>>,
    val doPostProductFetch: List<Hook<E, T>>,
    val doPostPriceFetch: List<Hook<E, T>>,
    val doPostCartFetch: List<Hook<E, T>>,
    val doPostEditHooks: List<Hook<E, T>>,
)
