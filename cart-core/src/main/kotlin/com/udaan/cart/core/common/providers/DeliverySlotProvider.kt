package com.udaan.cart.core.common.providers

import com.google.inject.Inject
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.cart.core.metrics.CartEventTracker
import com.udaan.common.utils.kotlin.logger
import com.udaan.orchestrator.client.DeliverySlotsServiceClient
import com.udaan.orchestrator.models.deliverySlots.GetDeliverySlotsRequest
import com.udaan.orchestrator.models.deliverySlots.GetDeliverySlotsRequestItem
import com.udaan.orchestrator.models.deliverySlots.GetDeliverySlotsResponse
import com.udaan.proto.models.ModelV1.SellingPlatform
import java.time.Instant

data class DeliverySlotInfo(
    val slotId: String,
    val startMinuteOfDay: Int,
    val endMinuteOfDay: Int,
    val deliveryDay: Int,
    val slotAvailable: Boolean,
    val metadata: Map<String, String>,
    val excludedPincodes: List<String>,
    val pincodePrefix: Int,
    val currentActive: Boolean,
    val delayedSla: Boolean?
) {
    fun getDeliveryDayInstant(): Instant {
        return Instant.now().plusSeconds(deliveryDay * 24 * 60 * 60L)
    }
}

private fun  com.udaan.orchestrator.models.v2.DeliverySlotInfo.toDeliverySlotInfo() = DeliverySlotInfo(
        slotId = slotId,
        startMinuteOfDay = startMinuteOfDay,
        endMinuteOfDay = endMinuteOfDay,
        deliveryDay = deliveryDay,
        slotAvailable = slotAvailable,
        metadata = metadata,
        excludedPincodes = excludedPincodes,
        pincodePrefix = pincodePrefix,
        currentActive = currentActive,
        delayedSla = delayedSla
)

interface DeliverySlotProvider {
    suspend fun getDeliverySlots(
            sellerId: String,
            buyerId: String,
            buyerOrgUnitId: String,
            platform: SellingPlatform
    ): List<DeliverySlotInfo> {
        return emptyList()
    }
    suspend fun getItemLevelDeliverySlots(
            sellerId: String,
            buyerId: String,
            buyerOrgUnitId: String,
            platform: SellingPlatform,
            allProductItems: List<ListingProductItem>? = null
    ): Map<String, List<DeliverySlotInfo>> {
        return emptyMap()
    }
}

class ItemLevelDeliverySlotProviderImpl @Inject constructor(
    private val deliverySlotsServiceClient: DeliverySlotsServiceClient,
    private val eventTracker: CartEventTracker
) : DeliverySlotProvider {

    companion object {
        private val logger by logger()
        const val SOURCE = "ORCHESTRATOR"
    }
    override suspend fun getItemLevelDeliverySlots(
            sellerId: String,
            buyerId: String,
            buyerOrgUnitId: String,
            platform: SellingPlatform,
            allProductItems: List<ListingProductItem>?
    ): Map<String, List<DeliverySlotInfo>> {
        val slots = deliverySlotsServiceClient.getDeliverySlotsByListings(
                request = GetDeliverySlotsRequest(
                        buyerOrgUnitId = buyerOrgUnitId,
                        sellerOrgId = sellerId,
                        sellingPlatform = platform,
                        items = allProductItems.orEmpty().map {
                            GetDeliverySlotsRequestItem(
                                    itemId = it.salesUnitId,
                                    listingId = it.listingId,
                                    requestedQuantity = it.quantity,
                                    salesUnitId = it.salesUnitId
                            )
                        }
                )
        ).executeAwait()
        trackEmptyItemDeliverySlot(buyerId, slots, allProductItems)
        return slots.itemSlots.mapNotNull { (itemKey, itemSlotValue) ->
            val itemSlotsInfo = itemSlotValue.slots.map { slot ->
                slot.toDeliverySlotInfo()
            }
            itemKey to itemSlotsInfo
        }.toMap()
    }

    suspend fun trackEmptyItemDeliverySlot(
        buyerId: String,
        slots: GetDeliverySlotsResponse,
        allProductItems: List<ListingProductItem>?,
    ) {
        val suListingMap = allProductItems?.associate { it.salesUnitId to it.listingId }
        slots.itemSlots.map { (itemKey, itemSlotValue) ->
            if (itemSlotValue.slots.isEmpty()) {
                eventTracker.trackEmptyItemDeliverySlot(
                    buyerId = buyerId,
                    listingId = suListingMap?.get(itemKey) ?: "",
                    salesUnitId = itemKey,
                    source = SOURCE
                )
            }
        }
    }
}

class DeliverySlotProviderImpl @Inject constructor(
    private val deliverySlotsServiceClient: DeliverySlotsServiceClient,
) : DeliverySlotProvider {

    companion object {
        private val logger by logger()
    }
    
    override suspend fun getDeliverySlots(
        sellerId: String,
        buyerId: String,
        buyerOrgUnitId: String,
        platform: SellingPlatform,
    ): List<DeliverySlotInfo> {
        logger.info("Deprecated Client. Please call item delivery slot to get the delivery slot details")
        val slots = deliverySlotsServiceClient.getDeliverySlotsByOrgUnitIdV2(
            sellerOrgId = sellerId,
            buyerOrgId = buyerId,
            shipToOrgUnitId = buyerOrgUnitId,
            platformId = platform,
        ).executeAwait()
        return slots.map { slot ->
            slot.toDeliverySlotInfo()
        }
    }
}
