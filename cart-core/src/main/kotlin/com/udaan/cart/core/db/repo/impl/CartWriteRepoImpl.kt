package com.udaan.cart.core.db.repo.impl

import com.google.inject.Inject
import com.udaan.cart.core.db.dao.CartDao
import com.udaan.cart.core.db.repo.CartWriteRepo
import com.udaan.cart.core.domain.models.Cart
import com.udaan.cart.core.domain.models.CartItem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.jdbi.v3.core.Jdbi
import org.joda.time.DateTime


class CartWriteRepoImpl @Inject constructor(
    private val dbi: Jdbi,
) : CartWriteRepo {
    override suspend fun create(cart: Cart) {
        withContext(Dispatchers.IO) {
            dbi.inTransaction<Int, Exception> { conn ->
                val updateDao = conn.attach(CartDao.Update::class.java)
                updateDao.createCart(
                    id = cart.id,
                    type = cart.type.name,
                    platformId = cart.platformId,
                    buyerId = cart.buyerId,
                    selection = cart.selection,
                    state = cart.state.name,
                    currentActive = cart.currentActive,
                    orderInfo = cart.orderInfo,
                    createdAt = cart.createdAt,
                    updatedAt = cart.updatedAt,
                )
                cart.items.map { item ->
                    updateDao.createCartItem(
                        id = item.id,
                        cartId = item.cartId,
                        quantity = item.quantity,
                        product = item.product,
                        perUnitAmountInPaisa = item.perUnitAmountInPaise,
                        priceDetails = item.priceDetails,
                        properties = item.properties,
                        currentActive = item.currentActive,
                        orderInfo = item.orderInfo,
                        createdAt = item.createdAt,
                        updatedAt = item.updatedAt,
                    )
                }.firstOrNull()
            }
        }
    }

    override suspend fun update(
        cart: Cart,
        newItems: List<CartItem>,
        updatedItems: List<CartItem>,
        discardedItems: List<CartItem>
    ) {
        withContext(Dispatchers.IO) {
            dbi.inTransaction<Unit, Exception> { conn ->
                val updateDao = conn.attach(CartDao.Update::class.java)
                newItems.map { item ->
                    createItem(updateDao, item)
                }
                updatedItems.map { item ->
                    updateItem(updateDao, item)
                }
                discardedItems.map { item ->
                    deleteItem(updateDao, item)
                }
                updateDao.updateCart(
                    id = cart.id,
                    type = cart.type,
                    state = cart.state,
                    currentActive = cart.currentActive,
                    orderInfo = cart.orderInfo,
                    updatedAt = cart.updatedAt,
                )
            }
        }
    }

    override suspend fun delete(cartId: String) {
        withContext(Dispatchers.IO) {
            dbi.inTransaction<Unit, Exception> { conn ->
                val updateDao = conn.attach(CartDao.Update::class.java)
                val now = DateTime.now().toDate()
                updateDao.deleteCartItemByCartId(cartId, now)
                updateDao.deleteCart(cartId, now)
            }
        }
    }

    private fun createItem(
        updateDao: CartDao.Update,
        item: CartItem
    ) {
        println("Creating item: $item")
        updateDao.createCartItem(
            id = item.id,
            cartId = item.cartId,
            quantity = item.quantity,
            product = item.product,
            perUnitAmountInPaisa = item.perUnitAmountInPaise,
            priceDetails = item.priceDetails,
            properties = item.properties,
            currentActive = item.currentActive,
            orderInfo = item.orderInfo,
            createdAt = item.createdAt,
            updatedAt = item.updatedAt,
        )
    }

    private fun updateItem(
        updateDao: CartDao.Update,
        item: CartItem,
    ) {
        updateDao.updateCartItem(
            itemId = item.id,
            quantity = item.quantity,
            product = item.product,
            perUnitAmountInPaisa = item.perUnitAmountInPaise,
            priceDetails = item.priceDetails,
            properties = item.properties,
            currentActive = item.currentActive,
            orderInfo = item.orderInfo,
            updatedAt = item.updatedAt,
        )
    }

    private fun deleteItem(
        updateDao: CartDao.Update,
        item: CartItem,
    ) {
        updateDao.deleteCartItem(itemId = item.id, updatedAt = item.updatedAt)
    }
}
