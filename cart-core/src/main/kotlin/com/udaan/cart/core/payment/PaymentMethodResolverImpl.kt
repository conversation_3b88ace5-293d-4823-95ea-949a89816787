package com.udaan.cart.core.payment

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.udaan.cart.core.domain.models.PaymentCollectionType
import com.udaan.catalog.client.CategoryGroupV2
import com.udaan.common.utils.kotlin.logger
import com.udaan.config.client.BusinessConfigClient
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withTimeout

class PaymentMethodResolverImpl @Inject constructor(
    private val codMethodEvaluator: CODMethodEvaluator,
    private val creditMethodEvaluator: CreditMethodEvaluator,
    private val objectMapper: ObjectMapper,
    private val businessConfigClient: BusinessConfigClient,
) : PaymentMethodResolver {

    companion object {
        private val logger by logger()
        private const val HUNDRED_PERCENT = 10000L
        private const val TIMEOUT_MILLIS_DEFAULT = 100L
        private const val ONLINE_PAYMENT_PILOT_BUYERS = "online-payment-pilot-buyers"
        private const val ALL_BUYERS = "ALL"
        private const val DISABLE_ONLINE_PAYMENT = "disable-online-payment"
    }

    override suspend fun resolve(context: PaymentEvalContext): List<PaymentEvalResult> {
        val creditResult = creditMethodEvaluator.evaluate(context)
        return if (creditResult.allowOtherMethods) {
            val codResult = codMethodEvaluator.evaluate(context)
            val isOnlinePaymentEnable = isOnlinePaymentEnable()
            val otherMethods = if ( isOnlinePaymentEnable && (codResult.collectionType == PaymentCollectionType.TOKEN ||
                pilotBuyersWithOnlinePayment(context, listOf(creditResult, codResult)))
            ) {
                listOf(
                    PaymentEvalResult(
                        collectionType = PaymentCollectionType.ADVANCE,
                        allowOtherMethods = true,
                        isValidMethod = true,
                        metaData = emptyMap(),
                        paymentBps = HUNDRED_PERCENT,
                        payableAmountInPaise = context.detailedCart.totalAmount(),
                        remainingAmountInPaise = 0L,
                    )
                )
            } else emptyList()
            if (creditResult.isValidMethod) {
                listOf(creditResult, codResult).plus(otherMethods)
            } else listOf(codResult).plus(otherMethods)
        } else listOf(creditResult)
    }

    private suspend fun pilotBuyersWithOnlinePayment(context: PaymentEvalContext, existingPaymentMethods: List<PaymentEvalResult>): Boolean {
        if (existingPaymentMethods.any {
                it.collectionType in listOf(PaymentCollectionType.TRADE_CREDIT, PaymentCollectionType.ADVANCE)
            }) {
            return false
        }
        val categoryGroupId = context.detailedCart.getCategoryGroupId()
        val buyerId = context.detailedCart.cart.buyerId
        logger.info("[pilotBuyersWithOnlinePayment] categoryGroupId = $categoryGroupId buyerId = $buyerId")
        if (categoryGroupId != CategoryGroupV2.FoodAndFMCG.id){
            return false
        }
        return kotlin.runCatching {
            val config = withTimeout(TIMEOUT_MILLIS_DEFAULT) {
                businessConfigClient.getStringAsync(ONLINE_PAYMENT_PILOT_BUYERS).await()?.let { config ->
                    objectMapper.readValue(config, OnlinePaymentConfigManager::class.java)
                }
            }
            val response = config?.let {
                pilotPharmaBuyersBuyingFmcg(it, buyerId) || pilotPacmanBuyersWithNoCredit(it, buyerId)
            } ?: false
            logger.info("[pilotBuyersWithOnlinePayment] online payment enabled = $response")
            response
        }.getOrElse { e ->
            logger.error("[pilotBuyersWithOnlinePayment] Failed to load online-payment-pilot-buyers " +
                "config: ${e.message}", e)
            false
        }
    }

    private suspend fun isOnlinePaymentEnable(): Boolean {
        return kotlin.runCatching {
            withTimeout(TIMEOUT_MILLIS_DEFAULT) {
                businessConfigClient.getBooleanAsync(DISABLE_ONLINE_PAYMENT).await()
            }?.not() ?: true
        }.getOrElse { e ->
            logger.error("[isOnlinePaymentEnable] Failed to load online-payment-pilot-buyers " +
                    "config: ${e.message}", e)
            true
        }
    }

    // If a pilot pharma buyer (i.e. traditionally buying pharma products) is purchasing food_and_fmcg products, we
    // need to give him full prepayment option
    private fun pilotPharmaBuyersBuyingFmcg(config: OnlinePaymentConfigManager, buyerId: String): Boolean {
        val pharmaConfig = config.onlinePaymentConfigs.firstOrNull { it.categoryGroupId == CategoryGroupV2.Pharma.id }
        return pharmaConfig?.let{configEnabledForBuyer(it, buyerId)} ?: false
    }

    // If a pilot food_and_fmcg buyer is doesn't have credit option
    // we need to give him full prepayment option
    private fun pilotPacmanBuyersWithNoCredit(config: OnlinePaymentConfigManager, buyerId: String): Boolean {
        val pacmanConfig = config.onlinePaymentConfigs.firstOrNull {
            it.categoryGroupId == CategoryGroupV2.FoodAndFMCG.id
        }
        return pacmanConfig?.let{configEnabledForBuyer(it, buyerId)} ?: false
    }

    private fun configEnabledForBuyer(config: OnlinePaymentConfig, buyerId: String) =
        buyerId in config.buyers || ALL_BUYERS in config.buyers
}

private data class OnlinePaymentConfig(
    val categoryGroupId: String,
    val buyers: List<String> = emptyList(),
)

private data class OnlinePaymentConfigManager(
    val onlinePaymentConfigs: List<OnlinePaymentConfig>
)
