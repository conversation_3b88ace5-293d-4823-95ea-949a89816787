package com.udaan.cart.core.operations.v1.contexts

import com.udaan.cart.core.context.BaseOperationContext
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.domain.models.Cart
import com.udaan.cart.core.domain.models.DetailedCart
import com.udaan.cart.core.domain.models.PaymentMethod
import com.udaan.cart.core.selectors.CartSelector
import com.udaan.cart.core.selectors.data.SelectorData

data class PaymentMethodsContext<R>(
    val request: R,
    val selectorData: SelectorData,
    val cartSelector: CartSelector<Cart>,
) : BaseOperationContext() {
    private var cartContext: CartContext? = null
    private var detailedCart: DetailedCart? = null
    private var paymentMethods: List<PaymentMethod> = emptyList()

    override suspend fun getCartContext(): CartContext? {
        return cartContext
    }

    suspend fun setCartContext(cartContext: CartContext) {
        this.cartContext = cartContext
    }

    suspend fun getDetailedCart(): DetailedCart? = detailedCart

    suspend fun setDetailedCart(detailedCart: DetailedCart?) {
        this.detailedCart = detailedCart
    }

    suspend fun setPaymentMethods(paymentMethods: List<PaymentMethod>) {
        this.paymentMethods = paymentMethods
    }

    suspend fun getPaymentMethods(): List<PaymentMethod> {
        return paymentMethods
    }
}
