package com.udaan.cart.core.context

import com.udaan.cart.core.domain.models.Cart

 abstract class MultiCartContext : BaseOperationContext() {
    abstract suspend fun getCartContexts(): List<CartContext>

    abstract suspend fun getCarts(): List<Cart>
}

class MultiCartContextImpl(
    private val carts: List<Cart>,
    private val cartContextFactory: CartContextFactory,
) : MultiCartContext() {
    private var cartContexts = emptyList<CartContext>()
    override suspend fun getCartContexts(): List<CartContext> {
        if (carts.isNotEmpty() && cartContexts.isEmpty()) {
            cartContexts = carts.map { cart ->
                cartContextFactory.createCartContext(cart.id, cart)
            }
        }
        return cartContexts
    }

    override suspend fun getCarts(): List<Cart> {
        return carts
    }

    override suspend fun getCartContext(): CartContext? {
        return getCartContexts().firstOrNull()
    }
}
