package com.udaan.cart.core.common.helpers

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.udaan.cart.core.operations.v1.hooks.impl.CatalogAvailabilityCheckHook
import com.udaan.common.utils.kotlin.error
import com.udaan.common.utils.kotlin.logger
import com.udaan.config.client.BusinessConfigClient
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withTimeout
import javax.inject.Inject

class ConfigServiceHelper @Inject constructor(
        private val businessConfigClient: BusinessConfigClient,
) {

    @JsonIgnoreProperties(ignoreUnknown = true)
    private data class HubExperimentData(
            val hubIds: List<String>
    )

    companion object {
        const val DEFAULT_TIMEOUT_MS = 100L
        const val HUB_EXPERIMENT_CONFIG = "hub-level-experiments"
        const val ONLY_TC_PAYMENT_ENABLED_ORGS_CONFIG = "only-tc-payment-enabled-orgs"
        private val defaultObjectMapper: ObjectMapper = jacksonObjectMapper()
        private val logger by logger()
    }

    private suspend fun getSetOfStringByKey(key: String): Set<String> {
        return withTimeout(DEFAULT_TIMEOUT_MS) {
            businessConfigClient.sMembersAsync<String>(key).await() ?: run {
                logger.error("ConfigServiceHelper.getSetOfStringByKey : config not found with key = $key")
                emptySet()
            }
        }
    }

    private suspend fun getValueByHashMapNameAndKey(
            hashMapName: String,
            key: String
    ): String? {
        return withTimeout(DEFAULT_TIMEOUT_MS) {
            businessConfigClient.hGetValueAsync<String>(
                    hashMapName = hashMapName,
                    key = key
            ).await()
        }
    }

    suspend fun getHubsEnabledForExp(expName: String): List<String> {
        return try {
            val config = getValueByHashMapNameAndKey(HUB_EXPERIMENT_CONFIG, expName)
            val hubExpData = defaultObjectMapper.readValue(config, HubExperimentData::class.java)
            hubExpData.hubIds
        } catch (ex: Exception) {
            logger.error("ConfigServiceHelper.getHubsEnabledForExp Failed to get hubs for $expName experiment" +
                    "| Exception: $ex"
            )
            emptyList()
        }
    }

    suspend fun getOnlyTCPaymentEnabledOrgs(): List<String> {
        return try {
            getSetOfStringByKey(ONLY_TC_PAYMENT_ENABLED_ORGS_CONFIG).toList()
        } catch (ex: Exception) {
            logger.error("ConfigServiceHelper.getOnlyTCPaymentEnabledOrgs: Failed to fetch | Exception: $ex")
            emptyList()
        }
    }

    suspend fun getValueByConfigName(configName: String): String? {
        return withTimeout(DEFAULT_TIMEOUT_MS) {
            businessConfigClient.getStringAsync(configName).await()
        }
    }
}
