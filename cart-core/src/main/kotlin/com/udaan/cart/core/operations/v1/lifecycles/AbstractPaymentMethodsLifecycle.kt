package com.udaan.cart.core.operations.v1.lifecycles

import arrow.core.Either
import arrow.core.continuations.either
import com.udaan.cart.core.common.helpers.Lifecycle
import com.udaan.cart.core.common.helpers.LifecycleStage
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.BaseContext
import com.udaan.cart.core.operations.v1.contexts.PaymentMethodsContext
import com.udaan.cart.core.operations.v1.hooks.FetchPaymentMethodsHookCollection

abstract class AbstractPaymentMethodsLifecycle<R> {
    protected abstract suspend fun setupHooks(
        hooks: FetchPaymentMethodsHookCollection<OpError, PaymentMethodsContext<R>>,
        context: PaymentMethodsContext<R>
    ): FetchPaymentMethodsHookCollection<OpError, PaymentMethodsContext<R>>

    protected abstract suspend fun doFetch(
        context: PaymentMethodsContext<R>
    ): Either<OpError, PaymentMethodsContext<R>>

    protected abstract suspend fun doFindPaymentMethods(
        context: PaymentMethodsContext<R>
    ): Either<OpError, PaymentMethodsContext<R>>

    @Suppress("UNCHECKED_CAST")
    suspend fun <T : BaseContext> execute(
        context: PaymentMethodsContext<R>,
        hooks: FetchPaymentMethodsHookCollection<OpError, T>
    ): Either<OpError, PaymentMethodsContext<R>> {
        val hook = setupHooks(
            hooks as FetchPaymentMethodsHookCollection<OpError, PaymentMethodsContext<R>>,
            context
        )
        val stages = listOf(
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doFetch(currentContext)
                },
                post = hook.postCartFetchHooks,
            ),
            LifecycleStage(
                pre = hook.preFindPaymentMethodsHooks,
                execute = { currentContext ->
                    doFindPaymentMethods(currentContext)
                },
                post = hook.postPaymentMethods,
            )
        )
        return either {
            Lifecycle(stages = stages)
                .execute(context)
                .bind()
        }
    }
}
