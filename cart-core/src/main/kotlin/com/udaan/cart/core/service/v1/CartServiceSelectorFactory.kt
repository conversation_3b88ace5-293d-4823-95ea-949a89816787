package com.udaan.cart.core.service.v1

import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.cart.core.domain.models.Cart
import com.udaan.cart.core.operations.v1.lifecycles.*
import com.udaan.cart.core.response.*
import com.udaan.cart.core.response.builders.DefaultItemLevelDeliverySlotResBuilder
import com.udaan.cart.core.selectors.CartSelector
import com.udaan.cart.models.default.*
import com.udaan.proto.models.ModelV1.SellingPlatform

@Suppress("TooManyFunctions", "LongParameterList")
class CartServiceSelectorFactory @Inject constructor(
    @Named("default_cart_selector")
    private val cartSelector: CartSelector<Cart>,
    @Named("default_multi_cart_selector")
    private val multiCartsSelector: CartSelector<List<Cart>>,
    @Named("default_edit_cart")
    private val editCartLifecycle: AbstractEditCartLifecycle<EditCartReqDto>,
    @Named("default_delete_cart")
    private val deleteCartLifecycle: AbstractDeleteCartLifecycle<DeleteCartReqDto>,
    @Named("default_fetch_cart")
    private val fetchCartLifecycle: AbstractFetchCartLifecycle<FetchCartReqDto>,
    @Named("default_fetch_carts")
    private val fetchCartsLifecycle: AbstractFetchCartsLifecycle<FetchCartReqDto>,
    @Named("default_fetch_payment_methods")
    private val fetchPaymentMethodsLifecycle: AbstractPaymentMethodsLifecycle<PaymentMethodsReqDto>,
    @Named("default_checkout_lifecycle")
    private val cartCheckoutLifecycle: AbstractCheckoutCartLifecycle<CheckoutRequestDto>,
    @Named("default_update_creditline_lifecycle")
    private val updateCreditLineLifecycle: AbstractUpdateCreditLineLifecycle<UpdateCreditLineReqDto>,
    @Named("default_response_builder")
    private val responseBuilder: ResponseBuilder,
    @Named("default_multi_cart_response_builder")
    private val multiCartResponseBuilder: MultiCartResponseBuilder,
    private val deliverySlotResponseBuilder: DeliverySlotResponseBuilder,
    @Named("default_payment_methods_response_builder")
    private val defaultPaymentResponseBuilder: ResponseBuilder,
    @Named("default_checkout_response_builder")
    private val defaultCheckoutResponseBuilder: CheckoutResponseBuilder,
    private val defaultItemLevelDeliverySlotResBuilder: DefaultItemLevelDeliverySlotResBuilder
) {
    fun getCartSelector(platformId: SellingPlatform): CartSelector<Cart> {
        return when (platformId) {
            SellingPlatform.UDAAN_MARKETPLACE -> cartSelector
            else -> cartSelector
        }
    }

    fun getMultiCartSelector(platformId: SellingPlatform): CartSelector<List<Cart>> {
        return when (platformId) {
            SellingPlatform.UDAAN_MARKETPLACE -> multiCartsSelector
            else -> multiCartsSelector
        }
    }

    fun getEditLifecycle(request: EditCartReqDto): AbstractEditCartLifecycle<EditCartReqDto> {
        return when (request.platformId) {
            SellingPlatform.UDAAN_MARKETPLACE -> editCartLifecycle
            else -> editCartLifecycle
        }
    }

    fun getDeleteLifecycle(request: DeleteCartReqDto): AbstractDeleteCartLifecycle<DeleteCartReqDto> {
        return when (request.platformId) {
            SellingPlatform.UDAAN_MARKETPLACE -> deleteCartLifecycle
            else -> deleteCartLifecycle
        }
    }

    fun getFetchLifecycle(request: FetchCartReqDto): AbstractFetchCartLifecycle<FetchCartReqDto> {
        return when (request.platformId) {
            SellingPlatform.UDAAN_MARKETPLACE -> fetchCartLifecycle
            else -> fetchCartLifecycle
        }
    }

    fun getFetchMultipleLifecycle(request: FetchCartReqDto): AbstractFetchCartsLifecycle<FetchCartReqDto> {
        return when (request.platformId) {
            SellingPlatform.UDAAN_MARKETPLACE -> fetchCartsLifecycle
            else -> fetchCartsLifecycle
        }
    }

    fun getPaymentMethodsLifecycle(
        request: PaymentMethodsReqDto
    ): AbstractPaymentMethodsLifecycle<PaymentMethodsReqDto> {
        return when (request.platformId) {
            SellingPlatform.UDAAN_MARKETPLACE -> fetchPaymentMethodsLifecycle
            else -> fetchPaymentMethodsLifecycle
        }
    }

    fun getCheckoutLifecycle(
        request: CheckoutRequestDto,
    ): AbstractCheckoutCartLifecycle<CheckoutRequestDto> {
        return when (request.platformId) {
            SellingPlatform.UDAAN_MARKETPLACE -> cartCheckoutLifecycle
            else -> cartCheckoutLifecycle
        }

    }

    fun getUpdateCreditLineLifecycle(
        request: UpdateCreditLineReqDto
    ): AbstractUpdateCreditLineLifecycle<UpdateCreditLineReqDto> {
        return when (request.platformId) {
            SellingPlatform.UDAAN_MARKETPLACE -> updateCreditLineLifecycle
            else -> updateCreditLineLifecycle
        }
    }

    fun getResponseBuilder(platformId: SellingPlatform): ResponseBuilder {
        return when (platformId) {
            SellingPlatform.UDAAN_MARKETPLACE -> responseBuilder
            else -> responseBuilder
        }
    }

    fun getMultiCartResponseBuilder(platformId: SellingPlatform): MultiCartResponseBuilder {
        return when (platformId) {
            SellingPlatform.UDAAN_MARKETPLACE -> multiCartResponseBuilder
            else -> multiCartResponseBuilder
        }
    }

    fun getDeliverySlotBuilder(platformId: SellingPlatform): DeliverySlotResponseBuilder {
        return when (platformId) {
            SellingPlatform.UDAAN_MARKETPLACE -> defaultItemLevelDeliverySlotResBuilder
            else -> deliverySlotResponseBuilder
        }
    }

    fun getPaymentMethodsResponseBuilder(platformId: SellingPlatform): ResponseBuilder {
        return when (platformId) {
            SellingPlatform.UDAAN_MARKETPLACE -> defaultPaymentResponseBuilder
            else -> defaultPaymentResponseBuilder
        }
    }

    fun getCheckoutResponseBuilder(platformId: SellingPlatform): CheckoutResponseBuilder {
        return when (platformId) {
            SellingPlatform.UDAAN_MARKETPLACE -> defaultCheckoutResponseBuilder
            else -> defaultCheckoutResponseBuilder
        }
    }
}
