package com.udaan.cart.core.common.helpers

import com.udaan.catalog.models.ModelV2

/**
 * Helper class for preparing listing details, including mapping verticals and brands
 * for trade listings.
 */
class ListingDetailsHelper {

    /**
     * Prepares listing details from a map of listings.
     *
     * @param listingsMap a map where the key is the listing ID and the value is a [ModelV2.TradeListing].
     * @return a [ListingDetails] object containing vertical and brand mappings for the listings.
     */
    fun prepareListingDetails(
        listingsMap: Map<String, ModelV2.TradeListing>
    ): ListingDetails = ListingDetails(
        listingVerticalMap = listingsMap.map {
            it.key to it.value.vertical
        }.toMap(),
        listingBrandMap = listingsMap.map {
            it.key to it.value.brand
        }.toMap()
    )
}

/**
 * Data class representing listing details, including mappings of listing verticals and brands.
 *
 * @property listingVerticalMap a map where the key is the listing ID and the value is the vertical.
 * @property listingBrandMap a map where the key is the listing ID and the value is the brand.
 */
data class ListingDetails(
    val listingVerticalMap: Map<String, String>,
    val listingBrandMap: Map<String, String>
)
