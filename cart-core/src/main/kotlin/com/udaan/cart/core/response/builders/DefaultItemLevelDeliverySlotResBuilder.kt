package com.udaan.cart.core.response.builders

import arrow.core.Either
import arrow.core.right
import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.cart.core.common.providers.DeliverySlotInfo
import com.udaan.cart.core.common.providers.DeliverySlotProvider
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.domain.models.Cart
import com.udaan.cart.core.domain.models.CartItem
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.cart.core.response.DeliverySlotResponseBuilder
import com.udaan.cart.models.BaseResponseDto
import com.udaan.cart.models.common.DeliverySlotDetails
import com.udaan.cart.models.common.DeliverySlotDto
import com.udaan.cart.models.common.ItemLevelDeliverySlot
import com.udaan.common.utils.kotlin.logger
import com.udaan.listing.builder.enrichments.catalog.CategoryConfigHelper
import com.udaan.proto.models.ModelV1
import org.joda.time.DateTime

class DefaultItemLevelDeliverySlotResBuilder @Inject constructor(
        @Named("item_level_delivery_slot_provider")
        private val deliverySlotProvider: DeliverySlotProvider,
        private val categoryConfigHelper: CategoryConfigHelper,
) : DeliverySlotResponseBuilder {

    companion object {
        private val logger by logger()
    }

    override suspend fun build(
            cartContext: CartContext,
            buyerOrgUnitId: String?
    ): Either<String, BaseResponseDto> {

        val buyerOrgUnitObj = cartContext.getBuyerOrgUnit(buyerOrgUnitId)
        val cart = cartContext.getCart()

        if (buyerOrgUnitObj == null || cart == null) {
            logger.info("Empty item level delivery slots. Buyer org unit : $buyerOrgUnitId cart: $cart")
            return DeliverySlotDto(deliverySlots = emptyList(), itemDeliverySlots = emptyList()).right()
        }

        val itemsBySeller = groupItemsBySeller(cart.items)

        return buyerOrgUnitObj.let { buyerOrgUnit ->
            val slotsBySeller = getSlotsBySeller(cart, itemsBySeller, buyerOrgUnit)

            DeliverySlotDto(
                    deliverySlots = emptyList(),
                    itemDeliverySlots = slotsBySeller
            ).right()
        }
    }

    private fun groupItemsBySeller(items: List<CartItem>): Map<String, List<ListingProductItem>> {
        return items.groupBy { (it.product as ListingProductItem).sellerId }
                    .mapValues { entry ->
                        entry.value.map {
                            (it.product as ListingProductItem)
                                    .copy(quantity = it.quantity)
                        }
                    }
    }

    private suspend fun getSlotsBySeller(
            cart: Cart,
            itemsBySeller: Map<String, List<ListingProductItem>>,
            buyerOrgUnit: ModelV1.OrgUnit
    ): List<ItemLevelDeliverySlot> {
        return itemsBySeller.mapNotNull { (sellerId, items) ->
        try {
            val item = items.first()
            val allProductItems = items.map { it.copy(quantity = it.quantity) }
            val suListingMap = items.associate { it.salesUnitId to it.listingId }
            val category = categoryConfigHelper.getCategory(
                    listingId = item.listingId,
                    vertical = null,
                    sellerOrg = null,
            )

            val itemLevelDeliverySlots = deliverySlotProvider.getItemLevelDeliverySlots(
                    sellerId = sellerId,
                    buyerId = cart.buyerId,
                    buyerOrgUnitId = buyerOrgUnit.orgUnitId,
                    platform = cart.platformId,
                    allProductItems = allProductItems
            )

            val itemCategoryDeliverySlots = itemLevelDeliverySlots.map { (salesUnitId, slots) ->
                ItemLevelDeliverySlot(
                        category = category.orEmpty(),
                        listingId = suListingMap[salesUnitId].orEmpty(),
                        slots = slots.map { slot -> slot.toDeliverySlotDetails() },
                        salesUnitId = salesUnitId
                )
            }

            logger.info("Delivery slots for $sellerId, ${cart.buyerId}, ${buyerOrgUnit.orgUnitId} " +
                    "$itemCategoryDeliverySlots")

            itemCategoryDeliverySlots
        } catch (e: Exception) {
            logger.error("Error fetching delivery slots for seller: $sellerId, cartId: ${cart.id} buyerOrgUnit: $buyerOrgUnit", e)
            null
        }
    }.flatten()
    }

    private fun DeliverySlotInfo.toDeliverySlotDetails(): DeliverySlotDetails {
        val today = DateTime.now().withTimeAtStartOfDay()
        return DeliverySlotDetails(
                slotId = slotId,
                available = slotAvailable,
                currentActive = currentActive,
                delayedSla = delayedSla,
                startTimestamp = today.plusMinutes(deliveryDay * 24 * 60 + startMinuteOfDay).millis,
                endTimestamp = today.plusMinutes(deliveryDay * 24 * 60 + endMinuteOfDay).millis
        )
    }

}
