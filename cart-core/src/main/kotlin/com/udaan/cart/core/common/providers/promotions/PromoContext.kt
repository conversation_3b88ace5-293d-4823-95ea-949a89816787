package com.udaan.cart.core.common.providers.promotions

import com.udaan.cart.core.domain.models.CartItem
import com.udaan.promotions.dto.v2.cart.CartResponse
import com.udaan.promotions.dto.v2.promotion.PromotionDetails
import com.udaan.promotions.dto.v2.promotion.PromotionStatus
import com.udaan.promotions.dto.v2.promotion.PromotionType
import com.udaan.promotions.dto.v2.promotion.getAssociatedCoupon
import com.udaan.rewards.models.SchemeDetailsV2

@Suppress("TooManyFunctions")
data class PromoContext(
    val promosResponse: CartResponse,
) {
    private var transformedItems = listOf<CartItem>()
    private var isReady = false
    private var itemToDiscountMap = emptyMap<String, Map<String, DiscountDetails>>()
    private var categoryGroupId: String? = null
    private var freebieCartLineToPromoMap = emptyMap<String, PromotionDetails>()
    private var schemeDetails: Set<SchemeDetailsV2> = emptySet()

    fun setIsReady(isReady: Boolean) {
        this.isReady = isReady
    }
    fun setCategoryGroupId(categoryGroupId: String) {
        this.categoryGroupId = categoryGroupId
    }

    fun updateTransformedItems(items: List<CartItem>) {
        transformedItems = items
    }

    fun updateItemToDiscountMap(discountsMap: Map<String, Map<String, DiscountDetails>>) {
        itemToDiscountMap = discountsMap
    }

    fun updateFreebieCartLineToPromoMap(cartLineToPromoMap: Map<String, PromotionDetails>) {
        freebieCartLineToPromoMap = cartLineToPromoMap
    }

    fun updateSchemeDetails(updatedSchemeDetails: Set<SchemeDetailsV2>) {
        schemeDetails = updatedSchemeDetails
    }

    fun addFreebieCartLineToPromoMap(cartLineToPromo: Pair<String, PromotionDetails>) {
        freebieCartLineToPromoMap = freebieCartLineToPromoMap + mapOf(cartLineToPromo)
    }

    fun getTransformedItems() = transformedItems

    fun getItemToDiscountMap() = itemToDiscountMap

    fun getCategoryGroupId() = categoryGroupId

    fun getFreebieCartLineToPromoMap() = freebieCartLineToPromoMap

    fun getAppliedCouponIds(): List<String> {
        return promosResponse.promotions.filter { promotionDetails ->
            promotionDetails.type == PromotionType.PROMOTION &&
                    promotionDetails.getAssociatedCoupon() != null &&
                    promotionDetails.status == PromotionStatus.APPLIED
        }.mapNotNull { promotionDetails ->
            promotionDetails.getAssociatedCoupon()?.id
        }
    }

    fun getSchemeDetails(): Set<SchemeDetailsV2> = schemeDetails
}
