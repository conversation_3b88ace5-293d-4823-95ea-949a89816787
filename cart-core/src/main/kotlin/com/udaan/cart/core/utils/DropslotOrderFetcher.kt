package com.udaan.cart.core.utils

import awaitOrNull
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.common.helpers.toSupplyChainCategory
import com.udaan.cart.core.common.providers.OrgIdentityProvider
import com.udaan.catalog.client.helpers.VerticalCache
import com.udaan.catalog.models.ModelV2
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.dropslot.client.DropslotServiceClient
import com.udaan.listing.builder.enrichments.catalog.CategoryConfigHelper
import com.udaan.model.orgs.OrgBusinessType
import com.udaan.orchestrator.client.PromiseServiceClient
import com.udaan.orchestrator.models.promiseEngine.availability.PreOrderEtaResponse
import com.udaan.orchestrator.models.v2.SupplyChainCategory
import com.udaan.orderform.common.providers.OrgProvider
import com.udaan.proto.models.ModelV1
import com.udaan.user.client.headOfficeUnit
import kotlinx.coroutines.future.await
import java.time.temporal.ChronoUnit

@Singleton
class DropslotOrderFetcher @Inject constructor(
    private val orgProvider: OrgProvider,
    private val verticalCache: VerticalCache,
    private val promiseServiceClient: PromiseServiceClient,
    private val dropSlotServiceClient: DropslotServiceClient,
    private val categoryConfigHelper: CategoryConfigHelper,
    private val orgIdentityProvider: OrgIdentityProvider,
) {
    companion object {
        private val logger by logger()
        private const val FOOD_AND_FMCG_CATEGORY = "food_and_fmcg"
        private const val FMCG_CATEGORY = "fmcg"
        private const val FRESH_CATEGORY = "fresh"
        private const val MEAT_CATEGORY = "meat"
        val ONE_DAY_IN_MILLISECONDS = ChronoUnit.DAYS.duration.toMillis()
    }
    suspend fun fetchActiveOrdersInDropSlot(
        buyerId: String,
        buyerOrgUnitId: String? = null,
        listingsMap: Map<String, ModelV2.TradeListing>,
        isFoodFMCGSeparateCart: Boolean = false,
        baseVertical: String? = null,
    ): List<String> {
        return kotlin.runCatching {
            val buyerOrgUnit = getOrgUnit(buyerId, buyerOrgUnitId)
            val listingAndCategoryBySeller = listingsMap.values.map {
                it.orgId to Pair(
                    it.listingId,
                    verticalCache.getVerticalAsync(it.vertical).awaitOrNull()?.toSupplyChainCategory()
                )
            }
            val isHorecaBuyer = orgIdentityProvider.getOrgIdentityModel(buyerId)?.businessType == OrgBusinessType.HORECA
            val categories = if(isHorecaBuyer) {
                listOf(FOOD_AND_FMCG_CATEGORY, FRESH_CATEGORY, MEAT_CATEGORY)
            } else {
                listingsMap.values.parallelMap { listing ->
                    categoryConfigHelper.getCategory(listing, null, null)
                }.mapNotNull { category ->
                    if (category == FMCG_CATEGORY) FOOD_AND_FMCG_CATEGORY else category
                }.toSet()
            }
            val promiseCutOffList = getPromiseCutoffListForBuyerAndCategory(
                listingAndCategoryBySeller,
                buyerOrgUnit,
                isHorecaBuyer
            )
            logger.info("[ExistingOrdersInDropslot] promiseCutOffList: $promiseCutOffList")
            val activeOrdersInDropSlot = categories.map { categoryId ->
                val activeOrdersForCategory = activeOrdersInDropSlot(
                    buyerOrgUnit = buyerOrgUnit,
                    buyerId = buyerId,
                    categoryId = categoryId,
                    promiseCutOffList = promiseCutOffList,
                    isFoodFMCGSeparateCart = isFoodFMCGSeparateCart,
                    baseVertical = baseVertical
                )
                logger.info("[ExistingOrdersInDropslot] categoryId = $categoryId " +
                    "activeOrdersForCategory = $activeOrdersForCategory")
                activeOrdersForCategory
            }.flatten()
            logger.info("[ExistingOrdersInDropslot] hasActiveOrdersInDropSlot: $activeOrdersInDropSlot")
            activeOrdersInDropSlot
        }.getOrElse { e ->
            logger.error("Failed to determine active slots for $buyerId: ", e)
            emptyList()
        }
    }

    private suspend fun activeOrdersInDropSlot(
        buyerOrgUnit: ModelV1.OrgUnit?,
        buyerId: String,
        categoryId: String,
        promiseCutOffList: List<PreOrderEtaResponse>,
        isFoodFMCGSeparateCart: Boolean,
        baseVertical: String? = null
    ): List<String> {
        val activeOrdersInDropSlotMap = promiseCutOffList.map { promiseCutOff->
            val ordersExistInDropSlot = dropSlotServiceClient.getBuyerDropSlotsV2(
                buyerId = buyerId,
                orgUnitId = buyerOrgUnit?.orgUnitId ?: "",
                pointSla = promiseCutOff.deliveryEtaEpoch,
                categoryId = categoryId
            ).executeAwait()
            val ordersInExistingInDropSlotByBaseVertical = ordersExistInDropSlot.orderBaseVerticalInfo.flatMap { order ->
                order.listingVerticalInfo.map { verticalInfo ->
                    verticalInfo.baseVertical.name.lowercase() to order.orderId
                }
            }.groupBy({ it.first }, { it.second })
            logger.info("ordersInExistingInDropSlotByBaseVertical: $ordersInExistingInDropSlotByBaseVertical")
            logger.info("[activeOrdersInDropSlot] currentDeliveryEta = ${promiseCutOff.deliveryEtaEpoch} " +
                "ordersExistInDropSlot = ${ordersExistInDropSlot.orderIds}")
            // Hack -  Adding 1 day to the delivery eta to check if there are any orders in the dropslot
            // To handle case of buyerWeeklyOff until getPreOrderEtaForBuyer handles buyerWeeklyOff
            val nextDayCutOff = promiseCutOff.deliveryEtaEpoch + ONE_DAY_IN_MILLISECONDS
            val ordersExistInDropSlotNextDay = dropSlotServiceClient.getBuyerDropSlotsV2(
                buyerId = buyerId,
                orgUnitId = buyerOrgUnit?.orgUnitId ?: "",
                pointSla = nextDayCutOff,
                categoryId = categoryId
            ).executeAwait()
            val ordersExistInDropSlotNextDayByBaseVertical = ordersExistInDropSlotNextDay.orderBaseVerticalInfo.flatMap { order ->
                order.listingVerticalInfo.map { verticalInfo ->
                    verticalInfo.baseVertical.name.lowercase() to order.orderId
                }
            }.groupBy({ it.first }, { it.second })
            logger.info("ordersExistInDropSlotNextDayByBaseVertical: $ordersExistInDropSlotNextDayByBaseVertical")
            logger.info("[activeOrdersInDropSlot] nextDayDeliveryEta = $nextDayCutOff " +
                "ordersExistInDropSlotNextDay = ${ordersExistInDropSlotNextDay.orderIds}")
            val existingOrderIds = if(isFoodFMCGSeparateCart) {
                when {
                    ordersInExistingInDropSlotByBaseVertical[baseVertical?.lowercase()]
                        .isNullOrEmpty().not() -> ordersInExistingInDropSlotByBaseVertical[baseVertical?.lowercase()]
                    ordersExistInDropSlotNextDayByBaseVertical[baseVertical?.lowercase()]
                        .isNullOrEmpty().not() -> ordersExistInDropSlotNextDayByBaseVertical[baseVertical?.lowercase()]
                    else -> emptyList()
                }
            } else {
                when {
                    ordersExistInDropSlot.orderIds
                        .isNullOrEmpty().not() -> ordersExistInDropSlot.orderIds
                    ordersExistInDropSlotNextDay.orderIds
                        .isNullOrEmpty().not() -> ordersExistInDropSlotNextDay.orderIds
                    else -> emptyList()
                }
            }
            logger.info(
                "Drop slot details => buyerId = $buyerId orgUnitId = ${buyerOrgUnit?.orgUnitId} " +
                        "promiseCutOff = $promiseCutOff categoryId = $categoryId " +
                        "ordersExistInDropSlot = $existingOrderIds "
            )
            existingOrderIds
        }
        return activeOrdersInDropSlotMap.filterNotNull().flatten()
    }

    private suspend fun getPromiseCutoffListForBuyerAndCategory(
            listingAndCategoryBySeller: List<Pair<String, Pair<String, SupplyChainCategory?>>>,
            buyerOrgUnit: ModelV1.OrgUnit?,
            isHorecaBuyer: Boolean
    ): List<PreOrderEtaResponse> {
        val scCategories = if (isHorecaBuyer) {
            setOf(SupplyChainCategory.FOOD, SupplyChainCategory.FRESH, SupplyChainCategory.MEAT)
        } else {
            listingAndCategoryBySeller
                    .mapNotNull { item ->
                        val listingAndCategoryPair = item.second
                        val category = listingAndCategoryPair.second
                        category
                    }
                    .toSet()
        }

        logger.info("SC Categories of cart: $scCategories")

        return scCategories.parallelMap { scCategory ->
            try {
                promiseServiceClient.getPreOrderEtaForBuyer(
                        category = scCategory,
                        buyerPincode = buyerOrgUnit?.unitAddress?.pincode ?: "",
                        buyerOrgUnitId = buyerOrgUnit?.orgUnitId ?: "",
                        sellingPlatform = ModelV1.SellingPlatform.UDAAN_MARKETPLACE,
                ).executeAwait()
            } catch (ex: Exception) {
                logger.error("Failed to get PreOrder ETA for category: $scCategory," +
                        "buyerPincode:${buyerOrgUnit?.unitAddress?.pincode ?: ""}," +
                        "buyerOrgUnitId: ${buyerOrgUnit?.orgUnitId ?: ""}, error: ${ex.message}")
                null // Ignore the exception and continue
            }
        }.filterNotNull()
    }


    private suspend fun getOrgUnit(
        buyerId: String,
        buyerOrgUnitId: String? = null,
    ): ModelV1.OrgUnit? {
        return buyerOrgUnitId?.let {
            orgProvider.fetchOrgUnit(buyerOrgUnitId).await()
        } ?: kotlin.run {
            orgProvider.fetchOrgDetails(buyerId).await().orgAccount.headOfficeUnit
        }
    }
}