package com.udaan.cart.core.db.repo

import com.udaan.cart.core.db.repo.models.DbSelectorData
import com.udaan.cart.core.domain.models.Cart

interface CartReadRepo {
    suspend fun findCarts(dbSelectorData: DbSelectorData): List<Cart>

    suspend fun findCart(dbSelectorData: DbSelectorData): Cart?

    suspend fun findCartById(dbSelectorData: DbSelectorData): Cart?

    suspend fun findByOrderId(buyerId: String, orderId: String): Cart?
}
