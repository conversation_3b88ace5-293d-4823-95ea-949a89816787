package com.udaan.cart.core.db.repo.impl

import com.google.inject.Inject
import com.udaan.cart.core.db.dao.ReadQueryStore
import com.udaan.cart.core.db.repo.CartReadRepo
import com.udaan.cart.core.db.repo.models.DbSelectorData
import com.udaan.cart.core.domain.models.*
import com.udaan.cart.models.common.CategoryGroupSelection
import com.udaan.catalog.client.CategoryGroupV2
import com.udaan.common.utils.idgen.UUIDUtils
import com.udaan.proto.models.ModelV1
import com.udaan.proto.models.ModelV1.SellingPlatform
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.core.kotlin.KotlinPlugin
import org.jdbi.v3.jackson2.Jackson2Plugin
import org.jdbi.v3.sqlobject.kotlin.KotlinSqlObjectPlugin
import kotlin.system.exitProcess
import org.jdbi.v3.postgres.PostgresPlugin

class CartReadRepoImpl @Inject constructor(
    private val dbi: Jdbi,
) : CartReadRepo {
    override suspend fun findCarts(
        dbSelectorData: DbSelectorData
    ): List<Cart> {
        return withContext(Dispatchers.IO) {
            dbi.withHandle<List<Cart>, Exception> { handle ->
                ReadQueryStore.findCarts(dbSelectorData = dbSelectorData, handle)
            }
        }
    }

    override suspend fun findCart(
        dbSelectorData: DbSelectorData
    ): Cart? {
        return withContext(Dispatchers.IO) {
            dbi.withHandle<Cart, Exception> { handle ->
                ReadQueryStore.findCart(dbSelectorData = dbSelectorData, handle)
            }
        }
    }

    override suspend fun findCartById(
        dbSelectorData: DbSelectorData
    ): Cart? {
        return withContext(Dispatchers.IO) {
            dbi.withHandle<Cart, Exception> { handle ->
                ReadQueryStore.findCart(dbSelectorData = dbSelectorData, handle)
            }
        }
    }

    override suspend fun findByOrderId(buyerId: String, orderId: String): Cart? {
        return withContext(Dispatchers.IO) {
            dbi.withHandle<Cart, Exception> { handle ->
                ReadQueryStore.findByOrderId(buyerId, orderId, handle)
            }
        }
    }
}

fun main() {
    runBlocking {
//        System.setProperty("udaan.env", "test")
//        val dbi = DBI("********************************************", "postgres", "postgres")
        val dbi = Jdbi.create("****************************************************************************")
        dbi.installPlugin(KotlinPlugin())
            .installPlugin(KotlinSqlObjectPlugin())
            .installPlugin(Jackson2Plugin())
            .installPlugin(PostgresPlugin())
//        val  builder = ResourceBuilder.dbiClient("cart-service-psql")
//        val dbi = builder.buildForPSQL()
        val readRepo = CartReadRepoImpl(dbi)
        val writeRepo = CartWriteRepoImpl(dbi)
        val id = UUIDUtils.uuidBase32("CT")
        val writeRes = writeRepo.create(
            Cart(
                id = id,
                type = CartType.FORWARD,
                platformId = ModelV1.SellingPlatform.UDAAN_MARKETPLACE,
                buyerId = "BUYER1",
                selection = CategoryGroupSelection(
                    categoryGroupId = CategoryGroupV2.FoodAndFMCG.id,
                ),
                state = CartState.ACTIVE,
                orderInfo = CartOrderInfo(
                    orderIds = listOf("ODA123")
                ),
                items = listOf(
                    CartItem(
                        cartId = id,
                        quantity = 1,
                        product = ListingProductItem(
                            sellerId = "1",
                            listingId = "L1",
                            salesUnitId = "SU1",
                            categoryGroupId = CategoryGroupV2.FoodAndFMCG.id,
                            quantity = 1
                        ),
                        perUnitAmountInPaise = 10000,
                        priceDetails = ItemPriceDetails(10000),
                        properties = ItemProperties(inventoryAvailable = true)
                    )
                )
            )
        )
        println("writeRes: $writeRes")
        val carts = readRepo.findCarts(
            DbSelectorData(
                buyerId = "BUYER1",
                platformId = SellingPlatform.UDAAN_MARKETPLACE.name,
                cartSelection = CategoryGroupSelection(
                    categoryGroupId = CategoryGroupV2.FoodAndFMCG.id,
                ),
            )
        )
        println("carts: $carts")
        val cartByOrder = readRepo.findByOrderId("BUYER1", "ODA123")
        println("cartByOrder: $cartByOrder")
        exitProcess(0)
    }
}
