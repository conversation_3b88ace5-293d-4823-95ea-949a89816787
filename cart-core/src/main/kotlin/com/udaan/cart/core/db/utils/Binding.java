package com.udaan.cart.core.db.utils;

import org.jdbi.v3.core.argument.AbstractArgumentFactory;
import org.jdbi.v3.core.argument.Argument;
import org.jdbi.v3.core.config.ConfigRegistry;

import java.util.UUID;

class Binding extends AbstractArgumentFactory<UUID> {
    protected Binding(int sqlType) {
        super(sqlType);
    }

    @Override
    protected Argument build(UUID value, ConfigRegistry config) {
        return (position, statement, ctx) -> statement.setString(position, value.toString());
    }
}

