package com.udaan.cart.core.operations.v1.hooks.impl

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.hooks.BaseOperationHook
import com.udaan.cart.core.operations.v1.contexts.EditCartContext
import com.udaan.cart.models.default.EditCartReqDto
import com.udaan.common.utils.kotlin.logger
import com.udaan.config.client.BusinessConfigClient
import kotlinx.coroutines.future.await

@Singleton
class MaxCartLinesCheckHook @Inject constructor(
    private val businessConfigClient: BusinessConfigClient
) : BaseOperationHook<OpError, EditCartContext<EditCartReqDto>> {

    companion object {
        private val logger by logger()
        private const val DEFAULT_MAX_ALLOWED_CART_SIZE = 199
        private const val CART_MAX_ORDERLINES = "cart-max-orderlines"
    }

    override suspend fun execute(context: EditCartContext<EditCartReqDto>): Either<OpError, EditCartContext<EditCartReqDto>> {
        return kotlin.runCatching {
            val MAX_ALLOWED_CART_SIZE = kotlin.runCatching {
                businessConfigClient.getIntAsync(CART_MAX_ORDERLINES).await() ?: DEFAULT_MAX_ALLOWED_CART_SIZE
            }.getOrElse {
                DEFAULT_MAX_ALLOWED_CART_SIZE
            }
            logger.info("Checking MaxCartLinesCheckHook")
            context.getCartContext()?.getCart()?.let { cart ->
                val newItemsCount = context.getEditedItems()?.newItems?.count() ?: 0
                when {
                    newItemsCount == 0 -> context.right()
                    cart.items.size + newItemsCount > MAX_ALLOWED_CART_SIZE -> {
                        OpError(
                            message = "Cannot add more items to cart, max allowed cart size is $MAX_ALLOWED_CART_SIZE"
                        ).left()
                    }
                    else -> context.right()
                }
            } ?: context.right()
        }.getOrElse { e ->
            logger.error("Failed to check cart size while editing cart: ${e.message}", e)
            context.right()
        }
    }
}