package com.udaan.cart.core.operations.v1.hooks.impl

import arrow.core.Either
import arrow.core.right
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.common.helpers.HubExperimentHelper
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.domain.models.PaymentCollectionType
import com.udaan.cart.core.domain.models.PaymentMethod
import com.udaan.cart.core.hooks.BaseOperationHook
import com.udaan.cart.core.operations.v1.contexts.PaymentMethodsContext
import com.udaan.cart.core.payment.addCheckoutExperienceTypeInfo
import com.udaan.cart.core.payment.policies.HighValuePaymentPolicy.Companion.PACMAN_RISKY_COD_VALUE_IN_PAISE
import com.udaan.cart.core.response.builders.DefaultPaymentMethodsResponseBuilder
import com.udaan.cart.core.utils.CartContextUtils
import com.udaan.cart.models.common.CheckoutExperienceType
import com.udaan.cart.models.common.MetadataVariables
import com.udaan.cart.models.default.PaymentMethodsReqDto
import com.udaan.common.utils.kotlin.logger
import com.udaan.tradecredit.models.enum.TradeCreditLineType

@Singleton
class CPODPaymentMethodsBuilderHook : BaseOperationHook<OpError, PaymentMethodsContext<PaymentMethodsReqDto>> {

    @Inject
    private lateinit var hubExperimentHelper: HubExperimentHelper

    @Inject
    private lateinit var cartContextUtils: CartContextUtils

    companion object {
        private val logger by logger()
    }

    override suspend fun execute(
        context: PaymentMethodsContext<PaymentMethodsReqDto>
    ): Either<OpError, PaymentMethodsContext<PaymentMethodsReqDto>> {
        return runCatching {
            val cartContext = context.getCartContext() ?: return context.right()
            val availablePaymentMethods = context.getPaymentMethods()
            val tcPaymentMethod =
                availablePaymentMethods.firstOrNull { it.collectionType == PaymentCollectionType.TRADE_CREDIT }
            val creditLineType = tcPaymentMethod?.metaData?.get(MetadataVariables.LINE_TYPE.name)
                ?.toString()?.let { TradeCreditLineType.valueOf(it) }
            val isNonCPODExperience = tcPaymentMethod?.metaData?.get(MetadataVariables.CHECKOUT_EXPERIENCE_TYPE.name)
                ?.toString() == CheckoutExperienceType.NON_CPOD.name
            val isCPODBuyer = tcPaymentMethod?.metaData
                ?.get(MetadataVariables.CPOD_BUYER.name)?.toString()?.toBoolean() ?: false

            if (tcPaymentMethod == null || !isCPODBuyer || isNonCPODExperience) {
                context.setPaymentMethods(
                    availablePaymentMethods.addCheckoutExperienceTypeInfo(CheckoutExperienceType.NON_CPOD)
                )
                return context.right()
            }
            val tcAvailableCredit =
                tcPaymentMethod.metaData[MetadataVariables.AVAILABLE_PURCHASE_AMOUNT.name].toString().toLong()
            val tcLineId = tcPaymentMethod.metaData[DefaultPaymentMethodsResponseBuilder.Variables.ID.name] as String

            val cpodPaymentMethods = getSelectedPaymentMethodForCPODOverdueExperience(availablePaymentMethods, tcPaymentMethod, tcAvailableCredit, tcLineId)
            context.setPaymentMethods(cpodPaymentMethods)
            context.right()
        }.getOrElse { e ->
            logger.error("Failed to build payment methods for CPOD ${context.request.buyerId} ${e.message}", e)
            context.right()
        }
    }

    /**
     * Get selected payment method for CPOD_Overdue experience.
     * This returns only a single selected method that is shown by
     * default on app. We don't give option for payment method selection
     * to user and automatically select the suitable method to be used.
     *
     * @param availablePaymentMethods
     * @param tcPaymentMethod
     * @param tcAvailableCredit
     * @param tcLineId
     * @return
     */
    private fun getSelectedPaymentMethodForCPODOverdueExperience(
        availablePaymentMethods: List<PaymentMethod>,
        tcPaymentMethod: PaymentMethod,
        tcAvailableCredit: Long,
        tcLineId: String
    ): List<PaymentMethod> {
        val onlinePaymentMethod =
            availablePaymentMethods.firstOrNull { it.collectionType == PaymentCollectionType.ADVANCE }
        // Take advance payment for order value > 2L and remaining_payable > 2L even after deducting available credit
        val showOnlineForHighValueOrders = onlinePaymentMethod != null
                && (onlinePaymentMethod.payableTotalInPaise - tcAvailableCredit) >= PACMAN_RISKY_COD_VALUE_IN_PAISE
        // Show online for orders < 2L (risk flagged orders)
        val showOnlineForRiskyOrders = onlinePaymentMethod != null
                && onlinePaymentMethod.payableTotalInPaise < PACMAN_RISKY_COD_VALUE_IN_PAISE

        val cpodV2PaymentMethods = if (showOnlineForHighValueOrders || showOnlineForRiskyOrders) {
            val additionalMetaData = mapOf(
                MetadataVariables.CPOD_TC_AVAILABLE_PURCHASE_AMOUNT.name to tcAvailableCredit,
                MetadataVariables.ORDER_AMOUNT.name to onlinePaymentMethod!!.payableTotalInPaise,
                MetadataVariables.REMAINING_OVERDUE.name to tcPaymentMethod.metaData[MetadataVariables.REMAINING_OVERDUE.name] as String
            )
            listOf(
                onlinePaymentMethod.let {
                    it.copy(
                        metaData = it.metaData + additionalMetaData
                    )
                }
            )
        } else {
            val additionalMetaData = mapOf(
                MetadataVariables.CPOD_TC_AVAILABLE_PURCHASE_AMOUNT.name to tcAvailableCredit,
                MetadataVariables.TC_LINE_ID.name to tcLineId
            )
            listOf(
                tcPaymentMethod.let {
                    it.copy(
                        metaData = additionalMetaData + it.metaData
                    )
                }
            )
        }
        return cpodV2PaymentMethods.addCheckoutExperienceTypeInfo(CheckoutExperienceType.CPOD_OVERDUE)
    }

}
