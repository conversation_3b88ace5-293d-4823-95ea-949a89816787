package com.udaan.cart.core.operations.v1.lifecycles

import arrow.core.Either
import arrow.core.continuations.either
import com.udaan.cart.core.common.helpers.Lifecycle
import com.udaan.cart.core.common.helpers.LifecycleStage
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.operations.v1.contexts.UpdateCreditLineContext
import com.udaan.cart.core.operations.v1.hooks.UpdateCreditLineHookCollection

abstract class AbstractUpdateCreditLineLifecycle<R> {

  protected abstract suspend fun setupHooks(
      hooks: UpdateCreditLineHookCollection<OpError, UpdateCreditLineContext<R>>,
      context: UpdateCreditLineContext<R>
  ): UpdateCreditLineHookCollection<OpError, UpdateCreditLineContext<R>>

  protected abstract suspend fun doFetch(
      context: UpdateCreditLineContext<R>
  ): Either<OpError, UpdateCreditLineContext<R>>

  protected abstract suspend fun doUpdateCreditLineAndStoreCartDetails(
      context: UpdateCreditLineContext<R>
  ): Either<OpError, UpdateCreditLineContext<R>>

  suspend fun execute(
      context: UpdateCreditLineContext<R>,
  ): Either<OpError, UpdateCreditLineContext<R>> {
    val stages = listOf(
        LifecycleStage<OpError, UpdateCreditLineContext<R>>(
            pre = emptyList(),
            execute = { currentContext ->
              doFetch(currentContext)
            },
            post = emptyList(),
        ),
        LifecycleStage(
            pre = emptyList(),
            execute = { currentContext ->
              doUpdateCreditLineAndStoreCartDetails(currentContext)
            },
            post = emptyList(),
        ),
    )
    return either {
      Lifecycle(stages = stages)
          .execute(context)
          .bind()
    }
  }
}
