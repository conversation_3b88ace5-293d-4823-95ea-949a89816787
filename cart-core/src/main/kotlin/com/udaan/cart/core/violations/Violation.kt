package com.udaan.cart.core.violations

import arrow.core.Either
import arrow.core.NonEmptyList
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.domain.models.DetailedCart
import com.udaan.cart.core.domain.models.ItemSummary
import com.udaan.orchestrator.models.maxQuantity.GroupType
import com.udaan.orchestrator.models.maxQuantity.MeasureUnit

interface ViolationData

enum class MoqViolationType {
    MOQ_LISTING,
    MOQ_SALES_UNIT,
}

data class MoqViolationData(
    val type: MoqViolationType,
    val listingId: String,
    val salesUnitId: String,
    val listingMoq: Int,
    val salesUnitMoq: Int
) : ViolationData

data class MaxUnitViolationData(
    val listingId: String,
    val salesUnitId: String,
    val maxUnits: Int
) : ViolationData

data class MovViolationData(
    val amountInPaise: Long
) : ViolationData

data class MaxUnitInventoryData(
    val itemSummary: ItemSummary,
    val maxUnits: Int,
): ViolationData

data class MaxGroupInventoryData(
    val items: List<ItemSummary>,
    val maxUnits: Double,
    val measureUnit: MeasureUnit,
    val groupType: GroupType,
): ViolationData

data class BuyerOrgShopTimingsData (
    val openingHours: String,
    val closingHours: String,
    val preferredDeliverySlotHour: String
) : ViolationData

interface ViolationValidator {
    suspend fun validate(
        cartContext: CartContext,
        detailedCart: DetailedCart?
    ): Either<NonEmptyList<String>, List<ViolationData>>
}
