package com.udaan.cart.core.domain.models

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.udaan.cart.models.common.FreebieInfo


enum class ProductType {
    LISTING
}

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = ListingProductItem::class, name = "LISTING"),
)
sealed class ProductItem(val type: ProductType)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ListingProductItem(
    val sellerId: String,
    val listingId: String,
    val salesUnitId: String,
    val categoryGroupId: String,
    val quantity: Int,
    val freebieInfo: FreebieInfo? = null,
    val creditTenure: String? = null,
    val creditLineId: String? = null
) : ProductItem(type = ProductType.LISTING)
