package com.udaan.cart.core.db.models

import com.udaan.cart.core.domain.models.*
import com.udaan.cart.models.common.CartSelection
import com.udaan.proto.models.ModelV1
import org.jdbi.v3.json.Json
import java.util.*

data class ExtendedCart(
    val id: String,
    val type: CartType,
    val buyerId: String,
    val platformId: ModelV1.SellingPlatform,
    @Json
    val cartSelection: CartSelection,
    val state: CartState,
    val currentActive: Boolean,
    @Json
    val orderInfo: CartOrderInfo?,
    val createdAt: Date,
    val updatedAt: Date,

    // Item details
    val itemId: String?,
    val quantity: Int?,
    @Json
    val product: ProductItem?,
    val perUnitAmountInPaisa: Long?,
    @Json
    val priceDetails: ItemPriceDetails?,
    @Json
    val itemProperties: ItemProperties?,
    val itemCurrentActive: Boolean = false,
    @Json
    val itemOrderInfo: CartItemOrderInfo?,
    val itemCreatedAt: Date?,
    val itemUpdatedAt: Date?,
) {
    fun toCart(): Cart {
        return Cart(
            id = id,
            type = type,
            buyerId = buyerId,
            platformId = platformId,
            selection = cartSelection,
            items = emptyList(),
            state = state,
            currentActive = currentActive,
            orderInfo = orderInfo,
            createdAt = createdAt,
            updatedAt = updatedAt,
        )
    }

    fun toCartItem(): CartItem? {
        // Minimum fields required to construct item
        return if (itemId != null &&
            quantity != null &&
            product != null &&
            perUnitAmountInPaisa != null &&
            priceDetails != null &&
            itemProperties != null &&
            itemCreatedAt != null &&
            itemUpdatedAt != null
        ) {
            CartItem(
                id = itemId,
                cartId = id,
                quantity = quantity,
                product = product,
                perUnitAmountInPaise = perUnitAmountInPaisa,
                priceDetails = priceDetails,
                properties = itemProperties,
                currentActive = itemCurrentActive,
                orderInfo = itemOrderInfo,
                createdAt = itemCreatedAt,
                updatedAt = itemUpdatedAt,
            )
        } else null
    }
}

fun List<ExtendedCart>.toCarts(): List<Cart> {
    return this.groupBy { it.id }.map { (_, groupedItems) ->
        val items = groupedItems.filter { it.itemCurrentActive }.mapNotNull {
            it.toCartItem()
        }
        groupedItems.first().toCart().copy(items = items)
    }
}
