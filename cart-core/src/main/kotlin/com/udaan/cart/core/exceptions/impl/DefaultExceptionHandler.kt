package com.udaan.cart.core.exceptions.impl

import com.udaan.cart.core.exceptions.BaseCartException
import com.udaan.cart.core.exceptions.ExceptionHandler
import com.udaan.common.client.UdaanServiceException
import com.udaan.common.utils.kotlin.logger
import java.util.concurrent.TimeoutException

class DefaultExceptionHandler : ExceptionHandler {
    companion object {
        private val logger by logger()
        private const val DEFAULT_OF_ERROR = "Failed to process the request"
        private const val UNKNOWN_ERROR = "Failed with unknown error"
        private const val TIMEOUT_ERROR = "Unable to complete request in time"
    }

    override fun handle(exception: Throwable): String {
        logger.error("Handling exception for ${exception.message}", exception)
        return when (exception) {
            is BaseCartException -> exception.message ?: DEFAULT_OF_ERROR
            is UdaanServiceException -> exception.message ?: DEFAULT_OF_ERROR // Check if it contains time out error
            is TimeoutException -> TIMEOUT_ERROR
            else -> UNKNOWN_ERROR
        }
    }
}
