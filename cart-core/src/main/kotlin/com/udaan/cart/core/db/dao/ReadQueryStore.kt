package com.udaan.cart.core.db.dao

import com.udaan.cart.core.db.dao.mappers.ExtendedCartMapper
import com.udaan.cart.core.db.models.toCarts
import com.udaan.cart.core.db.repo.models.DbSelectorData
import com.udaan.cart.core.domain.models.Cart
import com.udaan.cart.core.exceptions.InvalidCartSelectionException
import com.udaan.cart.models.common.*
import org.jdbi.v3.core.Handle

object ReadQueryStore {
    private val baseQuery = """
        select 
            c.id,
            c.type,
            c.buyer_id,
            c.platform_id,
            c.selection as cart_selection,
            c.state,
            c.current_active,
            c.order_info,
            c.created_at,
            c.updated_at,
            ci.id as "item_id",
            ci.quantity,
            ci.product,
            ci.per_unit_amount_in_paisa,
            ci.price_details,
            ci.properties as "item_properties",
            ci.current_active as "item_current_active",
            ci.order_info as "item_order_info",
            ci.created_at as "item_created_at",
            ci.updated_at as "item_updated_at"
            from carts c left join cart_items ci on c.id = ci.cart_id
    """.trimIndent()

    private val defaultQuery = "$baseQuery where c.buyer_id = ? and c.platform_id = ? " +
            "and c.state = 'ACTIVE' and c.current_active = 'true' "

    private val queryByOrder = "$baseQuery where c.buyer_id = ? and c.state = 'ACTIVE' and c.current_active = 'true' and " +
            "(c.order_info ->>'orderIds')::jsonb ??| array[?] and ci.current_active = 'true'"

    fun findCart(dbSelectorData: DbSelectorData, handle: Handle): Cart? {
        val (query, arguments) = prepareQueryForCart(dbSelectorData)
        return handle
            .select(query, *arguments.toTypedArray())
            .map(ExtendedCartMapper())
            .list()
            .toCarts()
            .firstOrNull()
    }

    fun findCarts(dbSelectorData: DbSelectorData, handle: Handle): List<Cart> {
        val (query, arguments) = prepareQueryForCarts(dbSelectorData)
        return handle
            .select(query, *arguments.toTypedArray())
            .map(ExtendedCartMapper())
            .list().toCarts()
    }

    fun findByOrderId(buyerId: String, orderId: String, handle: Handle): Cart? {
        return handle
            .select(queryByOrder, buyerId, orderId)
            .map(ExtendedCartMapper())
            .list()
            .toCarts()
            .firstOrNull()
    }

    private fun prepareQueryForCart(dbSelectorData: DbSelectorData): Pair<String, List<Any>> {
        if (!dbSelectorData.cartId.isNullOrBlank()) {
            val extendedQuery = "$baseQuery where c.id = ? and c.state = 'ACTIVE' and c.current_active = 'true'"
            return extendedQuery to listOf(dbSelectorData.cartId)
        }
        val cartSelection = dbSelectorData.cartSelection
            ?: throw InvalidCartSelectionException("Invalid cart selection parameters received")
        val arguments = mutableListOf<Any>(dbSelectorData.buyerId, dbSelectorData.platformId)
        return when (cartSelection) {
            is BuyerBasedSelection -> {
                val extendedQuery = defaultQuery + """
                     and c.selection->>'type' = ? 
                """.trimIndent()
                val selectionStrategy = dbSelectorData.cartSelection
                arguments.addAll(listOf(selectionStrategy.type))
                extendedQuery to arguments
            }

            is CategoryGroupSelection -> {
                val extendedQuery = defaultQuery + """
                    and c.selection->>'type' = ? 
                     and c.selection->>'categoryGroupId' = ? 
                """.trimIndent()
                arguments.addAll(listOf(cartSelection.type, cartSelection.categoryGroupId))
                extendedQuery to arguments
            }

            is CategoryGroupSellerSelection -> {
                val extendedQuery = defaultQuery + """
                    and c.selection->>'type' = ? 
                     and c.selection->>'categoryGroupId' = ? 
                      and c.selection->>'sellerId' = ?
                """.trimIndent()
                arguments.addAll(
                    listOf(
                        cartSelection.type,
                        cartSelection.categoryGroupId,
                        cartSelection.sellerId
                    )
                )
                extendedQuery to arguments
            }

            is CategoryGroupCategorySelection -> {
                val extendedQuery = defaultQuery + """
                    and c.selection->>'type' = ? 
                     and c.selection->>'categoryGroupId' = ? 
                      and c.selection->>'category' = ?
                """.trimIndent()
                arguments.addAll(
                    listOf(
                        cartSelection.type,
                        cartSelection.categoryGroupId,
                        cartSelection.category,
                    )
                )
                extendedQuery to arguments
            }

            is CategoryGroupBrandSelection -> {
                val extendedQuery = defaultQuery + """
                    and c.selection->>'type' = ? 
                     and c.selection->>'categoryGroupId' = ? 
                      and c.selection->>'brandId' = ?
                """.trimIndent()
                arguments.addAll(
                    listOf(
                        cartSelection.type,
                        cartSelection.categoryGroupId,
                        cartSelection.brandId,
                    )
                )
                extendedQuery to arguments
            }

            is CategoryGroupKeyBasedSelection -> {
                val extendedQuery = defaultQuery + """
                    and c.selection->>'type' = ? 
                     and c.selection->>'categoryGroupId' = ? 
                      and c.selection->>'key' = ?
                """.trimIndent()
                arguments.addAll(
                    listOf(
                        cartSelection.type,
                        cartSelection.categoryGroupId,
                        cartSelection.key,
                    )
                )
                extendedQuery to arguments
            }

            is KeyBasedSelection -> {
                val extendedQuery = defaultQuery + """
                    and c.selection->>'type' = ? 
                     and c.selection->>'key' = ? 
                      order by ci.created_at desc
                """.trimIndent()
                arguments.addAll(listOf(cartSelection.type, cartSelection.key))
                extendedQuery to arguments
            }

            is CategoryGroupOrgUnitSelection -> {
                val extendedQuery = defaultQuery + """
                    and c.selection->>'type' = ? 
                     and c.selection->>'categoryGroupId' = ? 
                      and c.selection->>'orgUnitId' = ?
                       order by ci.created_at desc
                """.trimIndent()
                arguments.addAll(
                    listOf(
                        cartSelection.type,
                        cartSelection.categoryGroupId,
                        cartSelection.orgUnitId
                    )
                )
                extendedQuery to arguments
            }

            is CategoryGroupBuyerSelection -> {
              val extendedQuery = defaultQuery + """
                      and c.selection->>'categoryGroupId' = ?
                  """.trimIndent()
              arguments.addAll(
                  listOf(
                      cartSelection.categoryGroupId,
                  )
              )
              extendedQuery to arguments
            }
        }
    }

    private fun prepareQueryForCarts(dbSelectorData: DbSelectorData): Pair<String, List<Any>> {
        val arguments = mutableListOf<Any>(dbSelectorData.buyerId, dbSelectorData.platformId)
        val cartSelection = dbSelectorData.cartSelection
            ?: throw InvalidCartSelectionException("Invalid cart selection parameters received")
        return when (cartSelection) {
            is CategoryGroupSellerSelection -> {
                val extendedQuery = defaultQuery + """
                    and c.selection->>'type' = ? 
                     and c.selection->>'categoryGroupId' = ? 
                """.trimIndent()
                val selectionStrategy = dbSelectorData.cartSelection
                arguments.addAll(
                    listOf(
                        cartSelection.type,
                        cartSelection.categoryGroupId,
                    )
                )
                extendedQuery to arguments
            }

            is CategoryGroupBrandSelection -> {
                val extendedQuery = defaultQuery + """
                    and c.selection->>'type' = ? 
                     and c.selection->>'categoryGroupId' = ? 
                """.trimIndent()
                arguments.addAll(
                    listOf(
                        cartSelection.type,
                        cartSelection.categoryGroupId,
                    )
                )
                extendedQuery to arguments
            }

            is CategoryGroupKeyBasedSelection -> {
                val extendedQuery = defaultQuery + """
                    and c.selection->>'type' = ? 
                     and c.selection->>'categoryGroupId' = ? 
                """.trimIndent()
                arguments.addAll(
                    listOf(
                        cartSelection.type,
                        cartSelection.categoryGroupId,
                    )
                )
                extendedQuery to arguments
            }

            is CategoryGroupBuyerSelection -> {
              val extendedQuery = defaultQuery + """
                    and c.selection->>'categoryGroupId' = ?
                """.trimIndent()
              arguments.addAll(
                  listOf(
                      cartSelection.categoryGroupId,
                  )
              )
              extendedQuery to arguments
            }

            else -> prepareQueryForCart(dbSelectorData)
        }
    }
}
