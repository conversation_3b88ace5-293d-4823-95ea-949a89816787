package com.udaan.cart.core.response.builders

import arrow.core.*
import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.cart.core.context.MultiCartContext
import com.udaan.cart.core.exceptions.ExceptionHandler
import com.udaan.cart.core.response.MultiCartResponseBuilder
import com.udaan.cart.core.response.ResponseBuilder
import com.udaan.cart.core.response.ResponseBuilderData
import com.udaan.cart.models.BaseResponseDto
import com.udaan.cart.models.common.ResponseFlags
import com.udaan.cart.models.default.CartResDto
import com.udaan.cart.models.default.CartsResDto
import com.udaan.common.utils.kotlin.logger

class DefaultMultiCartResponseBuilder @Inject constructor(
    @Named("default_exception_handler")
    private val exceptionHandler: ExceptionHandler,
    @Named("default_response_builder")
    private val cartResponseBuilder: ResponseBuilder,
) : MultiCartResponseBuilder {
    companion object {
        private val logger by logger()
    }

    override suspend fun build(
        responseBuilderData: ResponseBuilderData,
        responseFlags: ResponseFlags,
        cartContext: MultiCartContext,
        buyerOrgUnitId: String?
    ): Either<String, BaseResponseDto> {
        return kotlin.runCatching {
            val cartsResDto = cartContext.getCartContexts().map { cartContext ->
                cartResponseBuilder.build(
                    responseBuilderData,
                    responseFlags,
                    cartContext,
                    buyerOrgUnitId,
                )
            }
            when (val combinedResult = cartsResDto.sequence()) {
                is Either.Left -> combinedResult.value.left()
                is Either.Right -> CartsResDto(
                    carts = combinedResult.value as List<CartResDto>
                ).right()
            }
        }.getOrElse { e ->
            logger.error("Failed to build multi cart response: ", e)
            "Failed to build multi cart response".left()
        }
    }
}
