package com.udaan.cart.core.common.helpers

import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

/**
 * Utility class for handling time-related operations.
 */
class TimeHelper {

    companion object {
        /**
         * Defines the Indian Standard Time (IST) zone offset (+05:30).
         */
        private val ISTZone = ZoneId.of("+0530")
    }

    /**
     * Retrieves the current time in the IST timezone.
     *
     * @return The current [ZonedDateTime] in IST.
     */
    private fun getCurrentTime(): ZonedDateTime = ZonedDateTime.now(ISTZone)

    /**
     *  Extension function to convert a Unix timestamp (milliseconds) to a [ZonedDateTime]
     *  in the IST timezone.
     *
     *  @receiver [Long] The Unix timestamp in milliseconds
     *  @return [ZonedDateTime] The converted date-time in IST timezone
     */
    private fun Long.getZonedDateTime(): ZonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(this), ISTZone)
    /**
     * Converts a Unix timestamp to a formatted date string using the specified pattern
     * in the IST timezone.
     *
     * @param epoch [Long] The Unix timestamp in milliseconds to be converted
     * @param dateFormatPattern [String] The pattern to format the date
     *        (defaults to DateCallout.YEAR_MONTH_DATE.pattern)
     * @return [String] The formatted date string
     **/
    fun getDateStringFromEpoch(epoch: Long, dateFormatPattern: String = DateCallout.YEAR_MONTH_DATE.pattern): String {
        val epochZonedDateTime = epoch.getZonedDateTime()
        val formatter = DateTimeFormatter.ofPattern(dateFormatPattern)
        return epochZonedDateTime.format(formatter)
    }
    /**
     * Retrieves the current date formatted as per the provided pattern.
     *
     * @param dateFormatPattern The pattern in which the date should be formatted. Defaults to YEAR_MONTH_DATE format.
     * @return A formatted date string based on the provided pattern.
     */
    fun getCurrentDate(dateFormatPattern: String = DateCallout.YEAR_MONTH_DATE.pattern): String {
        val currentTime = getCurrentTime()
        val formatter = DateTimeFormatter.ofPattern(dateFormatPattern)
        return currentTime.format(formatter)
    }
}

/**
 * Enum representing different date format patterns.
 *
 * @property pattern The string pattern used for date formatting.
 */
enum class DateCallout(val pattern: String) {
    /** Format representing Year-Month-Date (yyyy-MM-dd). */
    YEAR_MONTH_DATE("yyyy-MM-dd")
}
