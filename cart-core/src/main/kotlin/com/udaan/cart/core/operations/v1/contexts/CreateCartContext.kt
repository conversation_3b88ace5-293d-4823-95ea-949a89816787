package com.udaan.cart.core.operations.v1.contexts

import com.udaan.cart.core.context.BaseOperationContext
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.domain.models.Cart
import com.udaan.cart.core.domain.models.CreationStrategy
import com.udaan.cart.core.selectors.CartSelector
import com.udaan.cart.core.selectors.data.SelectorData
import com.udaan.proto.representations.OrgV1

data class CreateCartContext<R>(
    val request: R,
    val creationStrategy: CreationStrategy,
    val selectorData: SelectorData,
    val cartSelector: CartSelector<Cart>,
) : BaseOperationContext() {
    private var cartContext: CartContext? = null
    internal var buyerOrg: OrgV1.OrgAccountExtendedResponse? = null
    private var isNew = false

    override suspend fun getCartContext(): CartContext? {
        return cartContext
    }

    fun setCartContext(cartContext: CartContext) {
        this.cartContext = cartContext
    }

    fun setIsNew(status: Boolean) {
        isNew = status
    }

    fun checkIsNew(): <PERSON><PERSON><PERSON> {
        return isNew
    }
}
