package com.udaan.cart.core.common.helpers

import arrow.core.Either
import arrow.core.NonEmptyList
import arrow.core.continuations.either
import arrow.core.right
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.BaseOperationContext
import com.udaan.cart.core.hooks.BaseCartHook
import com.udaan.cart.core.hooks.BaseOperationHook
import com.udaan.cart.core.hooks.Hook
import com.udaan.common.utils.kotlin.logger
import kotlin.time.DurationUnit
import kotlin.time.measureTimedValue

class LifecycleStage<E: OpError, T : BaseOperationContext>(
    private val pre: List<Hook<E, T>>,
    private val execute: suspend (T) -> Either<E, T>,
    private val post: List<Hook<E, T>>,
) {
    private suspend fun executeHooks(
        hooks: List<Hook<E, T>>,
        context: T
    ): Either<E, T> {
        val init = either<E, T> { Either.Right(context).bind() }
        return hooks.fold(init) { acc, hook ->
            when (acc) {
                is Either.Left -> acc
                is Either.Right ->
                    suspend {
                        when (hook) {
                            is BaseCartHook -> {
                                val cartContext = acc.value.getCartContext()
                                cartContext?.let {
                                    val cartContextsResult = hook.execute(cartContext)
                                    cartContextsResult.map {
                                        context
                                    }
                                } ?: context.right()
                            }
                            is BaseOperationHook -> {
                                hook.execute(acc.value)
                            }
                            else -> acc
                        }
                    }()
            }
        }
    }

    internal suspend fun run(context: T): Either<E, T> {
        val currentResult = when (val preResult = executeHooks(pre, context)) {
            is Either.Left -> preResult
            is Either.Right -> execute(preResult.value)
        }
        return when (currentResult) {
            is Either.Left -> currentResult
            is Either.Right -> executeHooks(post, currentResult.value)
        }
    }
}

class Lifecycle<E: OpError, T : BaseOperationContext>(
    private val stages: List<LifecycleStage<E, T>>
) {
    companion object {
        private val logger by logger()
    }
    suspend fun execute(
        context: T
    ): Either<E, T> {
        return either {
            val init = either<E, T> { Either.Right(context).bind() }
            stages.fold(init) { acc, stage ->
                when (acc) {
                    is Either.Left -> acc
                    is Either.Right -> {
                        val (result, duration) = measureTimedValue {
                            stage.run(acc.value)
                        }
                        logger.info("Stage duration : ${duration.toDouble(DurationUnit.MILLISECONDS)}")
                        result
                    }
                }
            }.bind()
        }
    }
}