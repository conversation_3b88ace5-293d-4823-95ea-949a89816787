package com.udaan.cart.core.operations.v1.lifecycles.product

import arrow.core.Either
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.BaseContext
import com.udaan.cart.core.operations.v1.contexts.EditCartContext
import com.udaan.cart.core.operations.v1.hooks.EditProductHookCollection
import com.udaan.cart.models.default.EditCartReqDto

interface ProductHandler {
    suspend fun <T : BaseContext> edit(
        context: EditCartContext<EditCartReqDto>,
        hooks: EditProductHookCollection<OpError, T>,
    ): Either<OpError, EditCartContext<EditCartReqDto>>
}
