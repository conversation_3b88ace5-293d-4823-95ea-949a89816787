package com.udaan.cart.core.common.providers.promotions

import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.context.CartContextKey

internal class PreTransform : Transform {
    override suspend fun apply(promoContext: PromoContext, cartContext: CartContext): PromoContext {
        cartContext.getDetailedCart(
            orgUnitId = cartContext.getValue(CartContextKey.BUYER_ORG_UNIT_ID),
            skipOOSItems = true,
        )?.let { detailedCart ->
            promoContext.updateTransformedItems(detailedCart.cart.items)
        }
        return promoContext
    }
}
