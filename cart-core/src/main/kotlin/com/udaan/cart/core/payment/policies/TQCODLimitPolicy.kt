package com.udaan.cart.core.payment.policies

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.payment.PaymentEvalContext
import com.udaan.catalog.client.CategoryTreeV2
import com.udaan.tradequality.client.TradeQualityBuyerRiskClient

@Singleton
class TQCODLimitPolicy @Inject constructor(
    private val tradeQualityClient: TradeQualityBuyerRiskClient
) : AbstractPrePaymentPolicy() {
    companion object {
        private const val POLICY_NAME = "TQCODLimitPolicy"
        private const val INSTRUCTIONS = "You have exhausted your Cash On Delivery Limit. " +
                "You will need to make online payment to place this order. " +
                "Your limit will be refreshed automatically upon delivery of any pending orders."
        private const val BPS = HUNDRED_PERCENT_BPS
    }

    override fun getName(): String = POLICY_NAME

    override suspend fun evaluate(paymentContext: PaymentEvalContext): PrePaymentResult {
        return if (CategoryTreeV2.marioCategoryGroups.contains(paymentContext.detailedCart.getCategoryGroupId())) {
            val isWithInCODLimit = tradeQualityClient.isBuyerWithinCODLimit(
                paymentContext.detailedCart.cart.buyerId,
                paymentContext.detailedCart.totalAmount(),
            ).executeAwait()
            if (isWithInCODLimit.not()) {
                prepareResult(
                    bps = BPS,
                    totalAmount = paymentContext.detailedCart.totalAmount(),
                    description = null,
                    instruction = INSTRUCTIONS,
                )
            } else defaultResult()
        } else defaultResult()
    }
}
