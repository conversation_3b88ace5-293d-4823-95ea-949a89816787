package com.udaan.cart.core.operations.v1.lifecycles

import arrow.core.Either
import arrow.core.continuations.either
import com.udaan.cart.core.common.helpers.Lifecycle
import com.udaan.cart.core.common.helpers.LifecycleStage
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.BaseContext
import com.udaan.cart.core.operations.v1.contexts.DeleteCartContext
import com.udaan.cart.core.operations.v1.hooks.DeleteCartHookCollection

abstract class AbstractDeleteCartLifecycle<R> {
    protected abstract suspend fun setupHooks(
        hooks: DeleteCartHookCollection<OpError, DeleteCartContext<R>>,
        context: DeleteCartContext<R>
    ): DeleteCartHookCollection<OpError, DeleteCartContext<R>>

    protected abstract suspend fun doFetchCart(
        context: DeleteCartContext<R>
    ): Either<OpError, DeleteCartContext<R>>

    protected abstract suspend fun doCleanOrders(
        context: DeleteCartContext<R>
    ): Either<OpError, DeleteCartContext<R>>

    protected abstract suspend fun doStoreOrder(
        context: DeleteCartContext<R>
    ): Either<OpError, DeleteCartContext<R>>

    @Suppress("UNCHECKED_CAST")
    suspend fun <T : BaseContext> execute(
        context: DeleteCartContext<R>,
        hooks: DeleteCartHookCollection<OpError, T>
    ): Either<OpError, DeleteCartContext<R>> {
        val hook = setupHooks(
            hooks as DeleteCartHookCollection<OpError, DeleteCartContext<R>>,
            context
        )
        val stages = listOf(
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doFetchCart(currentContext)
                },
                post = hook.doPostCartFetch,
            ),
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doCleanOrders(currentContext)
                },
                post = emptyList(),
            ),
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doStoreOrder(currentContext)
                },
                post = emptyList(),
            ),
        )
        return either {
            Lifecycle(stages = stages)
                .execute(context)
                .bind()
        }
    }
}
