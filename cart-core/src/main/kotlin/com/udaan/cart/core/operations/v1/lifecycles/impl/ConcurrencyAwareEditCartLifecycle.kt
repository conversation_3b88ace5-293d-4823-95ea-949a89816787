package com.udaan.cart.core.operations.v1.lifecycles.impl

import arrow.core.Either
import com.google.inject.Inject
import com.google.inject.Injector
import com.google.inject.name.Named
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.CartContextFactory
import com.udaan.cart.core.db.repo.CartWriteRepo
import com.udaan.cart.core.operations.v1.contexts.EditCartContext
import com.udaan.cart.core.operations.v1.lifecycles.AbstractCreateCartLifecycle
import com.udaan.cart.core.operations.v1.lifecycles.product.ProductHandler
import com.udaan.cart.models.default.CreateCartReqDto
import com.udaan.cart.models.default.EditCartReqDto

class ConcurrencyAwareEditCartLifecycle @Inject constructor(
    injector: Injector,
    private val cartContextFactory: CartContextFactory,
    private val productHandler: ProductHandler,
    @Named("default_create_cart")
    private val defaultCreateCartLifecycle: AbstractCreateCartLifecycle<CreateCartReqDto>,
    private val cartWriteRepo: CartWriteRepo,
) : DefaultEditCartLifecycle(
    injector,
    cartContextFactory,
    productHandler,
    defaultCreateCartLifecycle,
    cartWriteRepo,
) {
    override suspend fun doStoreCart(
        context: EditCartContext<EditCartReqDto>
    ): Either<OpError, EditCartContext<EditCartReqDto>> {
        return super.doStoreCart(context)
    }
}
