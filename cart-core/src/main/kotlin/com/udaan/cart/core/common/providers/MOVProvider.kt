package com.udaan.cart.core.common.providers

import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.constraint.client.ConstraintClient
import com.udaan.proto.models.ModelV1

interface MOVProvider {
    suspend fun find(
        platformId: ModelV1.SellingPlatform,
        categoryGroupId: String,
        buyerId: String,
        sellerId: String
    ): Long

    suspend fun find(
        platformId: ModelV1.SellingPlatform,
        categoryGroupId: String,
        buyerId: String,
        sellerId: String,
        listingIds: List<String>
    ): Long

    suspend fun find(
        platformId: ModelV1.SellingPlatform,
        categoryGroupId: String,
        buyerId: String,
        buyerOrgUnitId: String?,
        sellerId: String,
        listingIds: List<String>
    ): Long

    suspend fun find(
        platformId: ModelV1.SellingPlatform,
        categoryGroupId: String,
        buyerId: String,
        buyerOrgUnitId: String?,
        sellerId: String,
        listingIds: List<String>,
        categories: List<String>,
    ): Long
}

class MOVProviderImpl @Inject constructor(
    private val constraintClient: ConstraintClient,
) : MOVProvider {
    companion object {
        private val logger by logger()
    }

    override suspend fun find(
        platformId: ModelV1.SellingPlatform,
        categoryGroupId: String,
        buyerId: String,
        sellerId: String
    ): Long = find(
        platformId = platformId,
        categoryGroupId = categoryGroupId,
        buyerId = buyerId,
        buyerOrgUnitId = null,
        sellerId = sellerId,
        listingIds = emptyList()
    )

    override suspend fun find(
        platformId: ModelV1.SellingPlatform,
        categoryGroupId: String,
        buyerId: String,
        sellerId: String,
        listingIds: List<String>
    ): Long = find(
        platformId = platformId,
        categoryGroupId = categoryGroupId,
        buyerId = buyerId,
        buyerOrgUnitId = null,
        sellerId = sellerId,
        listingIds = listingIds
    )

    override suspend fun find(
        platformId: ModelV1.SellingPlatform,
        categoryGroupId: String,
        buyerId: String,
        buyerOrgUnitId: String?,
        sellerId: String,
        listingIds: List<String>
    ): Long = find(
        platformId = platformId,
        categoryGroupId = categoryGroupId,
        buyerId = buyerId,
        buyerOrgUnitId = buyerOrgUnitId,
        sellerId = sellerId,
        listingIds = listingIds,
        categories = emptyList(),
    )

    override suspend fun find(
        platformId: ModelV1.SellingPlatform,
        categoryGroupId: String,
        buyerId: String,
        buyerOrgUnitId: String?,
        sellerId: String,
        listingIds: List<String>,
        categories: List<String>,
    ): Long {
        return constraintClient.calculateMov(
            platformId = platformId,
            categoryGroupId = categoryGroupId,
            sellerId = sellerId,
            listings = listingIds,
            buyerId = buyerId,
            buyerOrgUnitId = buyerOrgUnitId,
            categories = categories,
        ).executeAwait(1).amountInPaise
    }
}
