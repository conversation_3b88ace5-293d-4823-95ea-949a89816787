package com.udaan.cart.core.context

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.udaan.cart.core.common.helpers.ConfigServiceHelper
import com.udaan.cart.core.common.providers.HubProvider
import com.udaan.cart.core.common.providers.LevyProvider
import com.udaan.cart.core.common.providers.OrgIdentityProvider
import com.udaan.cart.core.common.providers.PriceContextInterpreter
import com.udaan.cart.core.common.providers.promotions.PromotionsProvider
import com.udaan.cart.core.common.providers.promotions.SchemesProvider
import com.udaan.cart.core.db.repo.CartReadRepo
import com.udaan.cart.core.domain.models.Cart
import com.udaan.catalog.client.CatalogServiceClient
import com.udaan.catalog.client.helpers.VerticalCache
import com.udaan.orderform.common.providers.CatalogProvider
import com.udaan.orderform.common.providers.InventoryProvider
import com.udaan.orderform.common.providers.OrgProvider
import com.udaan.orderform.common.providers.PriceProvider
import com.udaan.user.client.RedisOrgRepository

interface CartContextFactory {
    fun createCartContext(
        cartId: String,
        cart: Cart?,
    ): CartContext

    fun createCartsContext(
        carts: List<Cart>,
    ): MultiCartContext
}

class CartContextFactoryImpl @Inject constructor(
    private val cartReadRepo: CartReadRepo,
    private val catalogProvider: CatalogProvider,
    private val orgProvider: OrgProvider,
    private val orgIdentityProvider: OrgIdentityProvider,
    private val pricingProvider: PriceProvider,
    private val pricingContextInterpreter: PriceContextInterpreter,
    private val levyProvider: LevyProvider,
    private val inventoryProvider: InventoryProvider,
    private val orgRepository: RedisOrgRepository,
    private val promotionsProvider: PromotionsProvider,
    private val catalogServiceClient: CatalogServiceClient,
    private val verticalCache: VerticalCache,
    private val schemesProvider: SchemesProvider,
    private val hubProvider: HubProvider,
    private val configServiceHelper: ConfigServiceHelper,
    private val objectMapper: ObjectMapper
): CartContextFactory {
    override fun createCartContext(
        cartId: String,
        cart: Cart?
    ): CartContext {
        return CartContextImpl(
            cartId = cartId,
            cart = cart,
            cartReadRepo = cartReadRepo,
            catalogProvider = catalogProvider,
            orgProvider = orgProvider,
            orgIdentityProvider = orgIdentityProvider,
            pricingProvider = pricingProvider,
            pricingContextInterpreter = pricingContextInterpreter,
            levyProvider = levyProvider,
            inventoryProvider = inventoryProvider,
            orgRepository = orgRepository,
            promotionsProvider = promotionsProvider,
            catalogServiceClient = catalogServiceClient,
            verticalCache = verticalCache,
            schemesProvider = schemesProvider,
            hubProvider = hubProvider,
            configServiceHelper = configServiceHelper,
            objectMapper = objectMapper
        )
    }

    override fun createCartsContext(
        carts: List<Cart>,
    ): MultiCartContext {
        return MultiCartContextImpl(
            carts = carts,
            cartContextFactory = this,
        )
    }
}
