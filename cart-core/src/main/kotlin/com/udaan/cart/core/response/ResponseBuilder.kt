package com.udaan.cart.core.response

import arrow.core.Either
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.context.MultiCartContext
import com.udaan.cart.core.operations.v1.contexts.CheckoutCartContext
import com.udaan.cart.models.BaseResponseDto
import com.udaan.cart.models.common.ResponseFlags
import com.udaan.cart.models.default.CheckoutRequestDto

interface ResponseBuilder {
    suspend fun build(
        responseBuilderData: ResponseBuilderData,
        responseFlags: ResponseFlags,
        cartContext: CartContext,
        buyerOrgUnitId: String?
    ): Either<String, BaseResponseDto>
}

interface MultiCartResponseBuilder {
    suspend fun build(
        responseBuilderData: ResponseBuilderData,
        responseFlags: ResponseFlags,
        cartContext: MultiCartContext,
        buyerOrgUnitId: String?
    ): Either<String, BaseResponseDto>
}

interface CheckoutResponseBuilder {
    suspend fun build(
        context: CheckoutCartContext<CheckoutRequestDto>
    ): Either<String, BaseResponseDto>
}
