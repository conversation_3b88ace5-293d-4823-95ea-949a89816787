package com.udaan.cart.core.operations.v1.hooks.impl

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.google.inject.Inject
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.CartContextKey
import com.udaan.cart.core.hooks.BaseOperationHook
import com.udaan.cart.core.operations.v1.contexts.CheckoutCartContext
import com.udaan.cart.core.violations.MovViolationData
import com.udaan.cart.core.violations.ViolationValidator
import com.udaan.cart.models.default.CheckoutRequestDto
import com.udaan.common.utils.kotlin.logger

@Singleton
class ViolationCheckHook @Inject constructor(
    @Named("mov_violation_validator")
    private val movViolationValidator: ViolationValidator,
) : BaseOperationHook<OpError, CheckoutCartContext<CheckoutRequestDto>> {
  companion object {
    private val logger by logger()
  }

  override suspend fun execute(
      context: CheckoutCartContext<CheckoutRequestDto>
  ): Either<OpError, CheckoutCartContext<CheckoutRequestDto>> {
    //For now, we will validate only MOV to prevent extra calls in checkout step
    return context.getCartContext()?.let { cartContext ->
      context.getCartContext()?.setValue(CartContextKey.CHECKOUT_CALL, true)
      cartContext.getCartWithPromos(
          context.request.buyerOrgUnitId,
          skipOOSItems = true,
          fetchRiderDetails = true,
          orderReadyForCheckout = true
      )?.let { detailedCart ->
        val movViolationResult = movViolationValidator.validate(cartContext, detailedCart)
        when (movViolationResult) {
          is Either.Left -> {
            logger.error("MOV violation validation failed: ${movViolationResult.value.joinToString()}")
            OpError(movViolationResult.value.joinToString()).left()
          }
          is Either.Right -> {
            val movViolation = movViolationResult.value.firstOrNull { it is MovViolationData }
            if (null == movViolation) {
              context.right()
            } else {
              val movAmount = (movViolation as? MovViolationData)?.amountInPaise ?: 0
              val buyerOrgUnitId = cartContext.getValue<String>(CartContextKey.BUYER_ORG_UNIT_ID)
              val availableItems = cartContext.getAvailableItems(detailedCart.cart, buyerOrgUnitId, false)
              val cartAmount = detailedCart.totalAmountForItems(availableItems)
              val remainingAmount = String.format("%.2f", (movAmount - cartAmount) / 100.0).toDouble()
              OpError(
                  message = "Add items worth ₹$remainingAmount to place the order"
              ).left()
            }
          }
        }
      } ?: OpError(message = "Cart not found").left()
    } ?: OpError(message = "Cart not found").left()
  }
}
