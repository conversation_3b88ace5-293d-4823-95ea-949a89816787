package com.udaan.cart.core.response.builders

import arrow.core.Either
import arrow.core.NonEmptyList
import arrow.core.left
import arrow.core.right
import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.cart.core.common.helpers.HubExperimentHelper
import com.udaan.cart.core.common.providers.ConstraintProvider
import com.udaan.cart.core.common.providers.promotions.PromoContext
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.context.CartContextKey
import com.udaan.cart.core.domain.models.DetailedCart
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.cart.core.exceptions.ExceptionHandler
import com.udaan.cart.core.exceptions.NoCartFoundException
import com.udaan.cart.core.exceptions.ViolationsBuildException
import com.udaan.cart.core.metrics.CartEventTracker
import com.udaan.cart.core.response.ResponseBuilder
import com.udaan.cart.core.response.ResponseBuilderData
import com.udaan.cart.core.violations.*
import com.udaan.cart.models.BaseResponseDto
import com.udaan.cart.models.common.*
import com.udaan.cart.models.default.CartResDto
import com.udaan.cart.models.default.InventoryInfo
import com.udaan.catalog.models.ModelV2.TradeListing
import com.udaan.common.utils.kotlin.logger
import com.udaan.orchestrator.models.maxQuantity.GroupType

@Suppress("LongParameterList")
class DefaultResponseBuilder @Inject constructor(
    @Named("default_exception_handler")
    private val exceptionHandler: ExceptionHandler,
    @Named("moq_violation_validator")
    private val moqViolationValidator: ViolationValidator,
    @Named("mov_violation_validator")
    private val movViolationValidator: ViolationValidator,
    @Named("max_inventory_validator")
    private val maxInventoryValidator: ViolationValidator,
    private val eventTracker: CartEventTracker,
    private val hubExperimentHelper: HubExperimentHelper,
    private val constraintProvider: ConstraintProvider
) : ResponseBuilder {
    companion object {
        private val logger by logger()
        private val unavailableInventoryInfo = InventoryInfo(
            available = false,
            availableQuantity = 0,
            isActive = false,
        )
    }

    override suspend fun build(
        responseBuilderData: ResponseBuilderData,
        responseFlags: ResponseFlags,
        cartContext: CartContext,
        buyerOrgUnitId: String?
    ): Either<String, BaseResponseDto> {
        // This is required as first step as inventory info is used to build promos, violations & price details
        val applyPromos = responseFlags.getOrDefault(ResponseFlag.APPLY_PROMOS, false)
        val applicableCart = if (applyPromos) {
            cartContext.getCartWithPromos(buyerOrgUnitId)
        } else {
            cartContext.getDetailedCart(buyerOrgUnitId, fetchLatestPrices = true)
        }
        val unavailabilityMap = buildUnavailabilityMap(buyerOrgUnitId, cartContext, applicableCart)
        buyerOrgUnitId?.let { cartContext.setValue(CartContextKey.BUYER_ORG_UNIT_ID, it) }
        return applicableCart?.let { detailedCart ->
            val items = detailedCart.cart.items.sortedBy { it.createdAt }.map { item ->
                val levies = detailedCart.leviesMap[item.id] ?: emptyList()
                val prePromoItem = detailedCart.prePromoCart?.items?.find { it.id == item.id }
                if (prePromoItem == null) {
                    logger.info("prePromoItem null for ${item.id}")
                }

                CartItem(
                    id = item.id,
                    product = ListingProduct(
                        listingId = (item.product as ListingProductItem).listingId,
                        salesUnitId = item.product.salesUnitId,
                        quantity = item.quantity,
                        freebieInfo = item.product.freebieInfo,
                        creditTenure = item.product.creditTenure,
                        creditLineId = item.product.creditLineId
                    ),
                    priceDetails = PriceDetails(
                        subTotal = item.perUnitAmountInPaise * item.quantity,
                        discount = prePromoItem?.let {
                            detailedCart.getPrePromoItemTotalWithTax(it) - detailedCart.getItemTotalWithTax(item)
                        } ?: 0L,
                        taxTotal = levies.sumOf { it.levyAmountPaise },
                        taxBps = levies.sumOf { it.levyBps },
                        totalAmount = detailedCart.getItemTotalWithTax(item),
                        prePromoSubTotal = prePromoItem?.let { it.perUnitAmountInPaise * it.quantity } ?: 0L,
                        prePromoTaxTotal = prePromoItem?.let { detailedCart.getPrePromoItemTax(it) } ?: 0L,
                        prePromoTotalAmount = prePromoItem?.let { detailedCart.getPrePromoItemTotalWithTax(it) } ?: 0L,
                        puInfo = item.priceDetails.puInfo?.let { puInfo ->
                            val pricingPackagingUnitDetails = cartContext.getPackagingUnitDetails(item.id)
                            PUResDto(
                                userPuType = puInfo.userPU,
                                assortment = pricingPackagingUnitDetails?.assortment,
                                multiplier = pricingPackagingUnitDetails?.multiplier
                            )
                        }
                    ),
                    levies = levies.map {
                        LevyLine(it.levyType.name, it.levyAmountPaise)
                    },
                )
            }
            val promoContext: PromoContext? = cartContext.getValue(CartContextKey.PROMOTION_CONTEXT)
            val offers = if (applyPromos) {
                OfferBuilder(detailedCart, promoContext).build()
            } else emptyList()
            val unavailableItemIdsDueToNoInventory = unavailabilityMap.keys
            val (availableItems, unavailableCartItemsDueToPrices) = items.filter { item ->
                !unavailableItemIdsDueToNoInventory.contains(item.id)
            }.run {
                // Validate for prices being 0
                val unavailableCartItemsDueToZeroPrice = this.mapNotNull { item ->
                    validatePriceForCartItem(item, detailedCart.cart.id)
                }
                val cartItemIdsWithNoPrice = detailedCart.itemsWithInvalidPrice.map { it.id }
                val unavailableCartItemsDueToNoPrice = items.filter {
                    it.id in cartItemIdsWithNoPrice
                }.map { item ->
                    handleCartItemsWithInvalidPrice(item, detailedCart.cart.id)
                }
                val unavailableCartItemsIdDueToPrices =
                    unavailableCartItemsDueToZeroPrice.map { it.first }.plus(cartItemIdsWithNoPrice)
                val finalItems = this.filter { item ->
                    !unavailableCartItemsIdDueToPrices.contains(item.id)
                }
                Pair(
                    finalItems,
                    (
                        unavailableCartItemsDueToZeroPrice.plus(unavailableCartItemsDueToNoPrice)
                    ).associate { it.first to it.second }
                )
            }

            //fetching the available items once, to ensure we don't require to fetch it for each violation individually
            cartContext.getAvailableItems(
                existingCart = applicableCart.cart,
                buyerOrgUnitId = buyerOrgUnitId,
                forceFetch = true
            )
            val deliveryChargeResponse = constraintProvider.evaluateDeliveryCharges(cartContext)

            CartResDto(
                cartId = detailedCart.cart.id,
                buyerId = detailedCart.cart.buyerId,
                platformId = detailedCart.cart.platformId.name,
                cartSelection = detailedCart.cart.selection,
                cartItems = items,
                priceDetails = PriceDetails(
                    subTotal = availableItems.sumOf { it.priceDetails.subTotal },
                    discount = availableItems.sumOf { it.priceDetails.discount },
                    taxTotal = availableItems.sumOf { it.priceDetails.taxTotal },
                    totalAmount = availableItems.sumOf { it.priceDetails.totalAmount },
                    prePromoSubTotal = availableItems.sumOf { it.priceDetails.prePromoSubTotal },
                    prePromoTaxTotal = availableItems.sumOf { it.priceDetails.prePromoTaxTotal },
                    prePromoTotalAmount = availableItems.sumOf { it.priceDetails.prePromoTotalAmount },
                ),
                unavailabilityMap = if (responseFlags.getOrDefault(
                        ResponseFlag.CHECK_INVENTORY_AVAILABILITY,
                        true
                    )
                ) {
                    unavailabilityMap.plus(unavailableCartItemsDueToPrices)
                } else unavailableCartItemsDueToPrices,
                violations = if (responseFlags.getOrDefault(ResponseFlag.CHECK_VIOLATIONS, false)) {
                    buildViolations(cartContext, applicableCart)
                } else {
                    emptyList()
                },
                offers = offers,
                additionalData = emptyMap(),
                schemeDetailsV2 = promoContext?.getSchemeDetails().orEmpty(),
                deliveryChargeResponse = deliveryChargeResponse,
            ).right()
        } ?: exceptionHandler.handle(NoCartFoundException("No cart found")).left()
    }

    private suspend fun buildUnavailabilityMap(
        buyerOrgUnitId: String?,
        cartContext: CartContext,
        detailedCart: DetailedCart?
    ): Map<String, InventoryInfo> {
        val cartItems = detailedCart?.getCartItems() ?: emptyList()
        val inactiveListingIds =
            cartContext.getListingsMap().values.filter { it.status != TradeListing.Status.ACTIVE }.map { it.listingId }

        val listingUnavailabilityResponseList = cartContext.getUnavailabilityInfo(
            buyerOrgUnitId = buyerOrgUnitId,
            cart = detailedCart?.cart,
            forceFetch = true
        )
        val freebieCartItemIdParentCartItemIdMap = fetchFreebieItemIdParentItemIdMap(cartItems)

        val listingUnavailabilityMap =  mutableMapOf<String, InventoryInfo>()
        // create unavailability map for the products
        listingUnavailabilityResponseList.map { listingUnavailability ->
            cartItems.firstOrNull {
                (it.product as ListingProductItem).salesUnitId == listingUnavailability.salesUnitId
            }?.let { item ->
                item.product as ListingProductItem
                val isActive = inactiveListingIds.contains(item.product.listingId).not()
                listingUnavailabilityMap.put(item.id, InventoryInfo(
                    available = listingUnavailability.isAvailable,
                    availableQuantity = listingUnavailability.availableQuantity,
                    isActive = isActive,
                ))
            }
        }
        val unavailableListingIds = listingUnavailabilityMap.keys
        freebieCartItemIdParentCartItemIdMap.map { (freebieCartItemId, parentCartItemId) ->
            // if freebie item is unavailable but parent is available, move parent also to unavailable section
            // Don't do any special handling for case when parent inventory is unavailable as it is handled in edit call
            if ((freebieCartItemId in unavailableListingIds) && (parentCartItemId !in unavailableListingIds)) {
                val parentCartItem = cartItems.firstOrNull { it.id == parentCartItemId } ?: return@map
                parentCartItem.product as ListingProductItem
                val isActive = inactiveListingIds.contains(parentCartItem.product.listingId).not()
                logger.info("CART_RESPONSE_BUILDER::FREEBIE_INVENTORY_UNAVAILABLE ${
                    mapOf(
                        "freebieCartItemId" to freebieCartItemId,
                        "parentCartItem" to parentCartItem,
                        "parentListingId" to parentCartItem.product.listingId
                    )
                }"
                )
                listingUnavailabilityMap[parentCartItemId] = InventoryInfo(
                    available = false,
                    availableQuantity = 0,
                    isActive = isActive,
                )
                listingUnavailabilityMap.remove(freebieCartItemId)
            }
        }
        return listingUnavailabilityMap
    }

    private suspend fun buildViolations(cartContext: CartContext, detailedCart: DetailedCart?): List<ViolationDto> {
        return buildViolationDto(moqViolationValidator.validate(cartContext, detailedCart)) { violationData ->
            (violationData as MoqViolationData)
            ViolationDto(
                type = ViolationType.MOQ_LISTING,
                metaData = MoqViolation(
                    listingId = violationData.listingId,
                    salesUnitId = violationData.salesUnitId,
                    listingMoq = violationData.listingMoq,
                    salesUnitMoq = violationData.salesUnitMoq,
                ),
            )
        }.plus(
            buildViolationDto(movViolationValidator.validate(cartContext, detailedCart)) { violationData ->
                (violationData as MovViolationData)
                ViolationDto(
                    type = ViolationType.MOV,
                    metaData = MovViolation(
                        amountInPaise = violationData.amountInPaise,
                    ),
                )
            }
        ).plus(
            buildViolationDto(maxInventoryValidator.validate(cartContext, detailedCart)) { violationData ->
                if (violationData is MaxUnitInventoryData) {
                    ViolationDto(
                        type = ViolationType.MAX_INVENTORY,
                        metaData = MaxInventoryViolation(
                            listingId = violationData.itemSummary.listingId,
                            salesUnitId = violationData.itemSummary.salesUnitId,
                            maxQuantity = violationData.maxUnits,
                        )
                    )
                } else {
                    violationData as MaxGroupInventoryData
                    val groupType = when (violationData.groupType) {
                        GroupType.PRODUCT_GROUP -> MaxGroupType.PRODUCT_GROUP
                        GroupType.VERTICAL -> MaxGroupType.VERTICAL
                        GroupType.SALES_UNIT -> MaxGroupType.SALES_UNIT
                    }
                    MeasureUnit.valueOf(violationData.measureUnit.name)
                    ViolationDto(
                        type = ViolationType.MAX_GROUP_INVENTORY,
                        metaData = MaxGroupInventoryViolation(
                            items = violationData.items.map {
                                ListingSalesUnitData(
                                    listingId = it.listingId,
                                    salesUnitId = it.salesUnitId,
                                )
                            },
                            maxQuantity = violationData.maxUnits,
                            measureUnit = MeasureUnit.WEIGHT_KG,
                            groupType = groupType,
                        )
                    )
                }
            }
        ).also {
            logger.info("[buildViolations] Violations for the cart => ${it.map { violation -> violation }}")
        }
    }

    private suspend fun validatePriceForCartItem(item: CartItem, cartId: String): Pair<String,InventoryInfo>? {
        val isNotFreebie = (item.product as ListingProduct).freebieInfo == null
        val isNegativePrice = item.priceDetails.subTotal < 0
        val isZeroPrice = item.priceDetails.subTotal == 0L
        return if (isNegativePrice || (isNotFreebie && isZeroPrice)) {
            handleCartItemsWithInvalidPrice(
                cartItem = item,
                cartId = cartId
            )
        } else null
    }

    private suspend fun handleCartItemsWithInvalidPrice(
        cartItem: CartItem,
        cartId: String
    ): Pair<String, InventoryInfo> {
        val cartProduct = cartItem.product as ListingProduct
        trackInvalidPriceForCartItem(
            cartId = cartId,
            cartItemId = cartItem.id,
            perUnitAmountInPaise = cartItem.priceDetails.subTotal / cartProduct.quantity,
            listingId = cartProduct.listingId,
            salesUnitId = cartProduct.salesUnitId
        )
        val inventoryInfo = unavailableInventoryInfo
        return Pair(cartItem.id, inventoryInfo)
    }

    private suspend fun trackInvalidPriceForCartItem(
        cartId: String,
        cartItemId: String,
        perUnitAmountInPaise: Long,
        listingId: String,
        salesUnitId: String
    ) {
        eventTracker.trackCartItemWithInvalidPrice(
            cartId = cartId,
            cartItemId = cartItemId,
            perUnitAmountInPaise = perUnitAmountInPaise,
            listingId = listingId,
            salesUnitId = salesUnitId
        )
    }

    private fun buildViolationDto(
        violationResult: Either<NonEmptyList<String>, List<ViolationData>>,
        apply: (violations: ViolationData) -> ViolationDto,
    ): List<ViolationDto> {
        return when (violationResult) {
            is Either.Left -> throw ViolationsBuildException(violationResult.value.joinToString())
            is Either.Right -> {
                violationResult.value.map { violationData ->
                    apply(violationData)
                }
            }
        }
    }

    // Get freebie cart item Id - parent cart item id map
    private fun fetchFreebieItemIdParentItemIdMap(
        cartItems: List<com.udaan.cart.core.domain.models.CartItem>
    ): Map<String, String> {
        val freebieItems = cartItems.filter { cartItem ->
            cartItem.isFreebie()
        }
        return freebieItems.map { freebieItem ->
            freebieItem.product as ListingProductItem
            val parentCartItemId = freebieItem.product.freebieInfo?.parentCartLineId!!
            freebieItem.id to parentCartItemId
        }.toMap()
    }
}
