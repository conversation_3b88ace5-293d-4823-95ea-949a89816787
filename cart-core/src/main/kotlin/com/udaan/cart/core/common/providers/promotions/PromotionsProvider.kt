package com.udaan.cart.core.common.providers.promotions

import com.google.inject.Inject
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.context.CartContextKey
import com.udaan.cart.core.db.repo.CartWriteRepo
import com.udaan.cart.core.domain.CartItemBuilder
import com.udaan.cart.core.domain.models.Cart
import com.udaan.cart.core.domain.models.totalAmountInPaise
import com.udaan.cart.core.metrics.CartEventTracker
import com.udaan.common.client.UdaanClientException
import com.udaan.common.utils.kotlin.logger
import com.udaan.orderform.common.providers.CatalogProvider
import com.udaan.promotions.client.PromotionsServiceClientV2
import com.udaan.promotions.dto.cart.*
import com.udaan.promotions.dto.v2.cart.CartResponse
import com.udaan.proto.models.ModelV1
import kotlinx.coroutines.TimeoutCancellationException
import java.util.concurrent.ExecutionException
import java.util.concurrent.TimeoutException

interface PromotionsProvider {
    suspend fun apply(
        cartContext: CartContext,
    ): PromoContext?
}

class PromotionsProviderImpl @Inject constructor(
    private val promotionsServiceClientV2: PromotionsServiceClientV2,
    private val cartWriteRepo: CartWriteRepo,
    private val eventTracker: CartEventTracker,
    catalogProvider: CatalogProvider,
    cartItemBuilder: CartItemBuilder,
) : PromotionsProvider {
    companion object {
        private val logger by logger()
    }

    private val promoTransformers = listOf(
        PreTransform(),
        DiscountTransform(),
        FreebieTransform(catalogProvider, cartItemBuilder)
    )

    @Suppress("LongMethod")
    override suspend fun apply(
        cartContext: CartContext,
    ): PromoContext? {
        val cartDetails =
            cartContext.getDetailedCart(
                cartContext.getValue(CartContextKey.BUYER_ORG_UNIT_ID),
                skipOOSItems = true,
            )
        if (cartDetails?.cart?.items?.isEmpty() == true) {
            logger.info("Not applying promotions, cart items are empty")
            return null
        }
        logger.info("Applying promotions")
        return cartDetails?.let { detailedCart ->
            logger.info("Applying promotions for cart")
            val cart = detailedCart.cart
            val categoryGroupId = detailedCart.getCategoryGroupId()
            val platform = when (cart.platformId) {
                ModelV1.SellingPlatform.UDAAN_MARKETPLACE -> SupportedPlatform.UDAAN_MARKETPLACE
                ModelV1.SellingPlatform.WONDERMART -> SupportedPlatform.WONDERMART
                ModelV1.SellingPlatform.SOCIALMART -> SupportedPlatform.SOCIALMART
                else -> SupportedPlatform.UDAAN_MARKETPLACE
            }
            val linetItems = detailedCart.cart.items.map { item ->
                item.product as com.udaan.cart.core.domain.models.ListingProductItem
                LineItem(
                    lineItemId = item.id,
                    itemDetail = ListingItem(
                        sellerOrgId = item.product.sellerId,
                        listingId = item.product.listingId,
                        salesUnitId = item.product.salesUnitId,
                    ),
                    units = item.quantity,
                    totalPreTaxAmountInPaise = item.totalAmountInPaise,
                    totalTaxInPaise = detailedCart.getItemTax(item),
                )
            }
            val promoRes = getApplicablePromotions(cartContext, cart, platform, categoryGroupId, linetItems)
            logger.info("promoRes: $promoRes")
            val promoContext = PromoContext(
                promosResponse = promoRes,
            )
            promoContext.setIsReady(true)
            promoContext.setCategoryGroupId(categoryGroupId)
            val appliedPromoTransform = promoTransformers.fold(promoContext) { appliedPromoContext, transformer ->
                transformer.apply(appliedPromoContext, cartContext)
            }
            updateFreebieCartItemOnDb(appliedPromoTransform, cartContext)
            appliedPromoTransform
        }
    }

    private suspend fun updateFreebieCartItemOnDb(updatePromoContext: PromoContext, cartContext: CartContext) {
        val cart = cartContext.getCart() ?: return
        val existingFreebieCartItems = cart.items
            .filter { (it.product as com.udaan.cart.core.domain.models.ListingProductItem).freebieInfo != null }
        val existingFreebieCartItemIds = existingFreebieCartItems.map { it.id }
        logger.info(
            "PromotionsProviderImpl::updateFreebieCartItemOnDb::existingFreebieCartItemIds " +
            existingFreebieCartItemIds.joinToString { it }
        )

        val transformedFreebieCartItems = updatePromoContext.getTransformedItems()
            .filter { (it.product as com.udaan.cart.core.domain.models.ListingProductItem).freebieInfo != null }
        logger.info(
            "PromotionsProviderImpl::updateFreebieCartItemOnDb::transformedFreebieCartItems " +
            transformedFreebieCartItems.joinToString { it.id }
        )

        // newly added freebie cart lines
        val newFreebieCartItems = transformedFreebieCartItems.filter {
            existingFreebieCartItemIds.contains(it.id).not()
        }
        // updated existing freebie cart lines
        val updatedFreebieCartItems = transformedFreebieCartItems.filter {
            existingFreebieCartItemIds.contains(it.id)
        }
        // deleted invalid freebie cart lines
        val deletedFreebieCartItems = existingFreebieCartItems.filter { existingFreebieCartItem ->
            updatedFreebieCartItems.all { it.id != existingFreebieCartItem.id }
        }

        cartWriteRepo.update(cart, newFreebieCartItems, updatedFreebieCartItems, deletedFreebieCartItems)
        logger.info(
            "PromotionsProviderImpl::updateFreebieCartItemOnDb::" +
            "new freebie cart items ${newFreebieCartItems.joinToString(", ") { it.id }}::" +
            "updated freebie cart items ${updatedFreebieCartItems.joinToString(", ") { it.id }}::" +
            "deleted freebie cart items ${deletedFreebieCartItems.joinToString(", ") { it.id }}"
        )
    }

    private suspend fun getApplicablePromotions(
        cartContext: CartContext,
        cart: Cart,
        platform: SupportedPlatform,
        categoryGroupId: String,
        linetItems: List<LineItem>
    ): CartResponse {
        return try {
            promotionsServiceClientV2.getApplicablePromotionsForCartV2(
                cartRequest = CartRequest(
                    cartId = cart.id,
                    buyerContext = BuyerContext(
                        buyerOrgId = cart.buyerId,
                        buyerOrgUnitId = null,
                    ),
                    platform = platform,
                    categoryGroupId = categoryGroupId,
                    lineItems = linetItems,
                    couponIds = cartContext.getValue<Set<String>>(CartContextKey.COUPON_IDS) ?: emptySet(),
                    excludedCouponIds = emptySet(),
                    couponApplicationPolicy = CouponApplicationPolicy.AUTO_APPLY_ALL,
                )
            ).executeAwait()
        } catch (e: Exception) {
            when (e) {
                is TimeoutCancellationException, is TimeoutException -> {
                    eventTracker.trackTimeout(
                        buyerId = cart.buyerId,
                        cartId = cart.id,
                        itemCount = cart.items.size,
                        categoryGroupId = categoryGroupId,
                        keySelection = cartContext.getCart()?.selection
                    )
                    throw e
                }
                is ExecutionException -> throw e.cause ?: UdaanClientException(e)
                is InterruptedException -> throw UdaanClientException(e)
                else -> throw e
            }
        }
    }
}
