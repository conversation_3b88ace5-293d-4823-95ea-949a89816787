package com.udaan.cart.core.operations.v1.hooks.impl

import arrow.core.Either
import arrow.core.right
import com.google.inject.Singleton
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.domain.models.CartState
import com.udaan.cart.core.exceptions.EmptyCartException
import com.udaan.cart.core.exceptions.NoCartFoundException
import com.udaan.cart.core.hooks.BaseOperationHook
import com.udaan.cart.core.operations.v1.contexts.CheckoutCartContext
import com.udaan.cart.models.default.CheckoutRequestDto
import com.udaan.common.utils.kotlin.logger

@Singleton
class CartStateCheckHook: BaseOperationHook<OpError, CheckoutCartContext<CheckoutRequestDto>> {

  companion object {
    private val logger by logger()
  }

  override suspend fun execute(
      context: CheckoutCartContext<CheckoutRequestDto>
  ): Either<OpError, CheckoutCartContext<CheckoutRequestDto>> {
    return context.getCartContext()?.let { cartContext ->
      cartContext.getCart()?.let { cart ->
        if (cart.state != CartState.ACTIVE) {
          logger.error("[CartStateCheckHook] Cart id: ${cart.id} state is " + "${cart.state}, not active")
          throw NoCartFoundException("No active cart found, please restart app to see active cart")
        } else {
          context.right()
        }
      }
    } ?: throw EmptyCartException("Cart is empty, kindly add items to cart")
  }
}
