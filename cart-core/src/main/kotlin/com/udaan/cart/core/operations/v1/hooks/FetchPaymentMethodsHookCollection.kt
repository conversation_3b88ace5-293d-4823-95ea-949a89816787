package com.udaan.cart.core.operations.v1.hooks

import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.BaseContext
import com.udaan.cart.core.hooks.Hook

data class FetchPaymentMethodsHookCollection<E: OpError, T : BaseContext>(
    val doValidationHooks: List<Hook<E, T>>,
    val postCartFetchHooks: List<Hook<E, T>>,
    val postPaymentMethods: List<Hook<E, T>>,
    val preFindPaymentMethodsHooks: List<Hook<E,T>>
)
