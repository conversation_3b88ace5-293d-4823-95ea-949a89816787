package com.udaan.cart.core.operations.v1.lifecycles.impl

import arrow.core.*
import com.google.inject.Inject
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.CartContextFactory
import com.udaan.cart.core.db.repo.CartWriteRepo
import com.udaan.cart.core.domain.CartBuilder
import com.udaan.cart.core.domain.models.Cart
import com.udaan.cart.core.domain.models.CreationStrategy
import com.udaan.cart.core.operations.v1.contexts.CreateCartContext
import com.udaan.cart.core.operations.v1.hooks.CreateCartHookCollection
import com.udaan.cart.core.operations.v1.lifecycles.AbstractCreateCartLifecycle
import com.udaan.cart.core.operations.v1.validators.CartCreationValidator
import com.udaan.cart.models.default.CreateCartReqDto
import com.udaan.common.utils.kotlin.logger

class DefaultCreateCartLifecycle @Inject constructor(
    private val cartCreationValidator: CartCreationValidator,
    private val cartContextFactory: CartContextFactory,
    private val cartWriteRepo: CartWriteRepo,
) : AbstractCreateCartLifecycle<CreateCartReqDto>() {
    companion object {
        private val logger by logger()
    }

    override suspend fun setupHooks(
        hooks: CreateCartHookCollection<OpError, CreateCartContext<CreateCartReqDto>>,
        context: CreateCartContext<CreateCartReqDto>
    ): CreateCartHookCollection<OpError, CreateCartContext<CreateCartReqDto>> {
        return hooks
    }

    override suspend fun doValidations(
        context: CreateCartContext<CreateCartReqDto>
    ): Either<OpError, CreateCartContext<CreateCartReqDto>> {
        return cartCreationValidator.validate(context)
    }

    override suspend fun doCreate(
        context: CreateCartContext<CreateCartReqDto>
    ): Either<OpError, CreateCartContext<CreateCartReqDto>> {
        val request = context.request
        val (cart, isNew) = when (context.creationStrategy) {
            CreationStrategy.CART_CREATE_UNIQUE -> {
                buildCart(context.request) to true
            }
            else -> {
                val cart = context.cartSelector.find(context.selectorData)
                cart?.let {
                    it to false
                } ?: (buildCart(context.request) to true)
            }
        }

        context.setCartContext(
            cartContextFactory.createCartContext(cart = cart, cartId = cart.id)
        )
        context.setIsNew(isNew)
        return context.right()
    }

    override suspend fun doStore(
        context: CreateCartContext<CreateCartReqDto>
    ): Either<OpError, CreateCartContext<CreateCartReqDto>> {
        return kotlin.runCatching {
            val cart = context.getCartContext()?.getCart()
            logger.info("Cart from create lifecycle: $cart")
            if (context.checkIsNew()) {
                cart?.let {
                    cartWriteRepo.create(cart)
                }
            }
            context.right()
        }.getOrElse { e ->
            logger.error("Failed to store new cart: ${e.message}", e)
            OpError(message = "Failed to store new cart", exception = e).left()
        }
    }

    private fun buildCart(request: CreateCartReqDto): Cart {
        // TODO: Improve cart selection creation based on platform and category group
        val selection = request.cartSelection
        return CartBuilder().build(
            buyerId = request.buyerId,
            platformId = request.platformId,
            selection = selection,
        )
    }
}
