package com.udaan.cart.core.common.providers

import com.google.inject.Inject
import com.udaan.cart.core.exceptions.OrgIdentityModelException
import com.udaan.common.client.UdaanServiceException
import com.udaan.common.utils.kotlin.logger
import com.udaan.model.orgs.OrgInternalIdentityModel
import com.udaan.user.client.OrgInternalIdentityCacheClient
import com.udaan.user.client.OrgServiceClient
import kotlinx.coroutines.withTimeout
import kotlin.time.measureTimedValue

interface OrgIdentityProvider {
    suspend fun getOrgIdentityModel(orgId: String): OrgInternalIdentityModel?
    suspend fun getOrgIdentityModelNonCached(orgId: String): OrgInternalIdentityModel?
}

class OrgIdentityProviderImpl @Inject constructor(
    private val orgInternalIdentityCacheClient: OrgInternalIdentityCacheClient,
    private val orgServiceClient: OrgServiceClient
) : OrgIdentityProvider {

    companion object {
        private val logger by logger()
        private const val MAX_TIMEOUT = 500L
    }

    override suspend fun getOrgIdentityModel(orgId: String): OrgInternalIdentityModel? {
        return try {
            withTimeout(MAX_TIMEOUT) {
                val (orgIdentityModel, duration) = measureTimedValue {
                    orgInternalIdentityCacheClient.getOrgInternalIdentityByOrgId(orgId)
                }
                if (null != orgIdentityModel) {
                    logger.info(
                        "[OrgIdentityProvider.getOrgIdentityModel] For orgId $orgId Business Type = ${orgIdentityModel.businessType} " +
                                "Business class = ${orgIdentityModel.businessClass} Duration = $duration"
                    )
                } else {
                    logger.info(
                        "[OrgIdentityProvider.getOrgIdentityModel] orgServiceClient returns null for CachedOrgInternalIdentity for " +
                                "org $orgId Duration = $duration"
                    )
                }
                orgIdentityModel
            }
        } catch (ex: UdaanServiceException) {
            if(ex.httpStatusCode == 404) {
                logger.error("[OrgIdentityProvider.getOrgIdentityModel] org $orgId is not populated in OrgInternalIdentity")
                null
            } else {
                logger.error("[OrgIdentityProvider.getOrgIdentityModel] Exception while fetching orgIdentity model for org $orgId : $ex")
                throw OrgIdentityModelException("Unable to fetch orgIdentity model for org $orgId")
            }
        } catch (ex: Exception) {
            logger.error("[OrgIdentityProvider.getOrgIdentityModel] Exception while fetching orgIdentity model for org $orgId : $ex")
            throw OrgIdentityModelException("Unable to fetch orgIdentity model for org $orgId")
        }
    }

    /**
     * Get org identity model non cached
     * This is a call that should ideally be called only from OrgShopTimingsProvider or if there are other use
     * cases. This was done so that post the update preferred slot call to avoid inconsistencies between
     * responses
     *
     * @param orgId
     * @return
     */
    override suspend fun getOrgIdentityModelNonCached(orgId: String): OrgInternalIdentityModel? {
        return try {
            withTimeout(MAX_TIMEOUT) {
                val (orgIdentityModel, duration) = measureTimedValue {
                    orgServiceClient.getOrgInternalIdentity(orgId = orgId).executeAwait()
                }
                logger.info(
                    "[OrgIdentityProvider.getOrgIdentityModelNonCached] For orgId $orgId Business Type = ${orgIdentityModel.businessType} " +
                            "Business class = ${orgIdentityModel.businessClass} Duration = $duration"
                )
                orgIdentityModel
            }
        } catch (ex: UdaanServiceException) {
            if(ex.httpStatusCode == 404) {
                logger.error("[OrgIdentityProvider.getOrgIdentityModelNonCached] org $orgId is not populated in OrgInternalIdentity")
                null
            } else {
                logger.error("[OrgIdentityProvider.getOrgIdentityModelNonCached] Exception while fetching orgIdentity model for org $orgId : $ex")
                throw OrgIdentityModelException("Unable to fetch orgIdentity model for org $orgId")
            }
        } catch (ex: Exception) {
            logger.error("[OrgIdentityProvider.getOrgIdentityModelNonCached] Exception while fetching orgIdentity model for org $orgId : $ex")
            throw OrgIdentityModelException("Unable to fetch orgIdentity model for org $orgId")
        }
    }
}