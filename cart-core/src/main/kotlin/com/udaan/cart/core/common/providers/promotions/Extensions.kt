package com.udaan.cart.core.common.providers.promotions

import com.udaan.instrumentation.TelemetryScope
import com.udaan.model.orgs.BusinessClass
import com.udaan.model.orgs.OrgBusinessType
import com.udaan.model.orgs.OrgInternalIdentityModel
import com.udaan.promotions.dto.promotion.MinifiedPromotionTier
import com.udaan.promotions.dto.promotion.RewardClass
import com.udaan.promotions.dto.promotion.RewardType
import java.math.RoundingMode
import java.text.DecimalFormat

//fun CartResponse.getCartPromosByRewardClass(rewardClass: RewardClass): List<AppliedCartPromotion> =
//    this.appliedCartPromos.filter { it.appliedPromotion.tier.rewardClass == rewardClass }

fun getDiscountPercentage(discount: Int): String {
    val decimalFormat = DecimalFormat("#.##")
    decimalFormat.roundingMode = RoundingMode.HALF_UP
    return "${decimalFormat.format(discount.toFloat().div(100))}%"
}

fun MinifiedPromotionTier.getGeneratedTitleForOrderLinePromos(): String? =
    if (rewardClass == RewardClass.DISCOUNT && rewardType == RewardType.INSTANT && isFlatDiscount) {
        // This case is for handling DOTD Callout
        "${getDiscountPercentage(discount?.discountBps!!)} discount"
    } else null

