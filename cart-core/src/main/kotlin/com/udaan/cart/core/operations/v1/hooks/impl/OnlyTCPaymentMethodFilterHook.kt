package com.udaan.cart.core.operations.v1.hooks.impl

import arrow.core.Either
import arrow.core.right
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.domain.models.PaymentCollectionType
import com.udaan.cart.core.hooks.BaseOperationHook
import com.udaan.cart.core.operations.v1.contexts.PaymentMethodsContext
import com.udaan.cart.core.payment.addCheckoutExperienceTypeInfo
import com.udaan.cart.core.utils.BuyerProfile
import com.udaan.cart.core.utils.getBuyerProfile
import com.udaan.cart.models.common.CheckoutExperienceType
import com.udaan.cart.models.common.MetadataVariables
import com.udaan.cart.models.default.PaymentMethodsReqDto
import com.udaan.common.utils.kotlin.logger
import com.udaan.tradecredit.models.enum.TradeCreditLineType
import com.udaan.user.client.OrgInternalIdentityCacheClient

@Singleton
class OnlyTCPaymentMethodFilterHook : BaseOperationHook<OpError, PaymentMethodsContext<PaymentMethodsReqDto>> {

    companion object {
        private val logger by logger()
    }

    @Inject
    private lateinit var orgInternalIdentityCacheClient: OrgInternalIdentityCacheClient

    override suspend fun execute(
        context: PaymentMethodsContext<PaymentMethodsReqDto>
    ): Either<OpError, PaymentMethodsContext<PaymentMethodsReqDto>> {
        val orgInternalIdentity = orgInternalIdentityCacheClient.getOrgInternalIdentityByOrgId(context.request.buyerId)
        val availablePaymentMethods = context.getPaymentMethods()
        val tcPaymentMethod =
            availablePaymentMethods.firstOrNull { it.collectionType == PaymentCollectionType.TRADE_CREDIT }
        
        // Get TC line type if TC payment method exists
        val creditLineType = tcPaymentMethod?.metaData?.get(MetadataVariables.LINE_TYPE.name)
            ?.toString()?.let { TradeCreditLineType.valueOf(it) }

        // TC sets this flag for cases where they explicitly want to disable CPOD exp - Not for all non CPOD buyers
        val disableCPODExperienceForBuyer = tcPaymentMethod?.metaData
            ?.get(MetadataVariables.DISABLE_CPOD_EXPERIENCE.name)?.toString()?.toBoolean() ?: false

        if (tcPaymentMethod != null && (
            orgInternalIdentity.getBuyerProfile() == BuyerProfile.HORECA_AB ||
            creditLineType == TradeCreditLineType.FOOD_FMCG_FPS_SAHAY || disableCPODExperienceForBuyer
        )) {
            context.setPaymentMethods(
                listOf(tcPaymentMethod).addCheckoutExperienceTypeInfo(CheckoutExperienceType.NON_CPOD)
            )
            logger.info("OnlyTCPaymentMethodFilterHook: Filtering out all payment methods except Trade Credit for buyer ${context.request.buyerId}")
        }
        return context.right()
    }

}
