package com.udaan.cart.core.operations.v1.lifecycles

import arrow.core.Either
import arrow.core.continuations.either
import com.udaan.cart.core.common.helpers.Lifecycle
import com.udaan.cart.core.common.helpers.LifecycleStage
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.BaseContext
import com.udaan.cart.core.operations.v1.contexts.CheckoutCartContext
import com.udaan.cart.core.operations.v1.hooks.CheckoutCartHookCollection

abstract class AbstractCheckoutCartLifecycle<R> {
    protected abstract suspend fun setupHooks(
        hooks: CheckoutCartHookCollection<OpError, CheckoutCartContext<R>>,
        context: CheckoutCartContext<R>
    ): CheckoutCartHookCollection<OpError, CheckoutCartContext<R>>

    protected abstract suspend fun doFetchCart(
        context: CheckoutCartContext<R>
    ): Either<OpError, CheckoutCartContext<R>>

    protected abstract suspend fun doCheckoutCart(
        context: CheckoutCartContext<R>
    ): Either<OpError, CheckoutCartContext<R>>

    protected abstract suspend fun doClearCart(
        context: CheckoutCartContext<R>
    ): Either<OpError, CheckoutCartContext<R>>

    @Suppress("UNCHECKED_CAST")
    suspend fun <T : BaseContext> execute(
        context: CheckoutCartContext<R>,
        hooks: CheckoutCartHookCollection<OpError, T>
    ): Either<OpError, CheckoutCartContext<R>> {
        val hook = setupHooks(
            hooks as CheckoutCartHookCollection<OpError, CheckoutCartContext<R>>,
            context
        )

        val stages = listOf(
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doFetchCart(currentContext)
                },
                post = hook.doPostCartFetch,
            ),
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doCheckoutCart(currentContext)
                },
                post = emptyList(),
            ),
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doClearCart(currentContext)
                },
                post = emptyList(),
            ),
        )

        return either {
            Lifecycle(stages = stages)
                .execute(context)
                .bind()
        }
    }
}
