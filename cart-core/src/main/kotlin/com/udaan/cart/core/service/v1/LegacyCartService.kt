package com.udaan.cart.core.service.v1

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.common.providers.promotions.PromoContext
import com.udaan.cart.core.context.CartContextKey
import com.udaan.cart.core.db.repo.CartReadRepo
import com.udaan.cart.core.db.repo.CartWriteRepo
import com.udaan.cart.core.domain.models.CartItem
import com.udaan.cart.core.domain.models.CartOrderInfo
import com.udaan.cart.core.domain.models.CartState
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.cart.core.exceptions.*
import com.udaan.cart.core.operations.v1.contexts.FetchCartContext
import com.udaan.cart.core.operations.v1.hooks.FetchCartHookCollection
import com.udaan.cart.core.response.ResponseDataBuilder
import com.udaan.cart.core.selectors.data.DefaultSelectorData
import com.udaan.cart.models.BaseRequestDto
import com.udaan.cart.models.BaseResponseDto
import com.udaan.cart.models.common.toOrderFormPaymentMethod
import com.udaan.cart.models.default.*
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.orderform.cart.models.dto.*
import com.udaan.orderform.client.CartClient
import com.udaan.orderform.client.HorecaOrderFormClientV1
import com.udaan.orderform.client.MultiSellerCartClient
import com.udaan.orderform.client.OrderFormClientV2
import com.udaan.orderform.models.dto.MultiSellerDto
import com.udaan.proto.models.ModelV1.SellingPlatform
import kotlinx.coroutines.future.await
import org.joda.time.DateTime

class LegacyCartService @Inject constructor(
    private val serviceSelectorFactory: CartServiceSelectorFactory,
    @Named("default_exception_handler") private val exceptionHandler: ExceptionHandler,
    private val orderFormClientV2: OrderFormClientV2,
    private val horecaOrderFormClient: HorecaOrderFormClientV1,
    private val cartClient: CartClient,
    private val multiSellerCartClient: MultiSellerCartClient,
    private val cartWriteRepo: CartWriteRepo,
    private val cartReadRepo: CartReadRepo,
) {
    companion object {
        private val logger by logger()
    }

    suspend fun fetchHorecaPaymentMethods(
        request: BaseRequestDto
    ): Either<String, BaseResponseDto> {
        request as FetchCartReqDto
        return when (val fetchResponse = fetchCart(request)) {
            is Either.Left -> fetchResponse.value.message.left()
            is Either.Right -> {
                kotlin.runCatching {
                    fetchResponse.value.getCartContext()?.let { cartContext ->
                        cartContext.getCart()?.let { cart ->
                            if (cart.state != CartState.ACTIVE) {
                                logger.error(
                                    "Cart id: ${cart.id} state is " + "${cart.state}, not active"
                                )
                                throw NoCartFoundException("No active cart found, please restart app to see active cart")
                            }
                            kotlin.runCatching {
                                cart.orderInfo?.orderIds?.parallelMap { orderId ->
                                    cartClient.removeOrderForm(orderId).executeAwait()
                                }
                            }.getOrElse { e ->
                                logger.error("Cart id: ${cart.id}, failed to delete orders ${e.message}", e)
                                kotlin.runCatching {
                                    val placedCart =
                                        cart.copy(state = CartState.ORDER_PLACED, updatedAt = DateTime.now().toDate())
                                    cartWriteRepo.update(placedCart, emptyList(), emptyList(), emptyList())
                                }.getOrElse { ex ->
                                    logger.error("Failed to mark cart(${cart.id}) as completed ${ex.message}", ex)
                                }
                                throw GenericCartException(
                                    e.message ?: "Your order is already placed, please check orders history"
                                )
                            }
                        }
                        cartContext.getCartWithPromos(request.buyerOrgUnitId, skipOOSItems = true)
                            ?.let { detailedCart ->
                                val cart = detailedCart.cart
                                logger.info("Cart items: ${cart.items.joinToString(", ") { it.id }}")
                                val promoContext = cartContext.getValue<PromoContext>(CartContextKey.PROMOTION_CONTEXT)
                                val couponIds = promoContext?.getAppliedCouponIds() ?: emptyList()
                                //TODO: Apply validations
                                val itemsBySeller = detailedCart.getCartItems(skipItemsWithPriceIssues = true).groupBy {
                                    (it.product as ListingProductItem).sellerId
                                }
                                val orderIds = itemsBySeller.map { (sellerId, items) ->
                                    val categoryGroupId = detailedCart.getCategoryGroupId()
                                    createOrder(
                                        sellerId = sellerId,
                                        buyerId = request.buyerId,
                                        categoryGroupId = categoryGroupId,
                                        platformId = cart.platformId,
                                        items = items,
                                        buyerOrgUnitId = request.buyerOrgUnitId,
                                    )
                                }.map { it.orderId }
                                logger.info("Created orders: $orderIds")
                                val paymentMethodsReq = MultiSellerDto.PaymentMethodsReqDto(
                                    buyerId = request.buyerId,
                                    orderIds = orderIds,
                                    selectedOrgUnitId = cartContext.getBuyerOrgUnit(request.buyerOrgUnitId)?.orgUnitId
                                        ?: "",
                                    selectedDeliverySlotId = null,
                                    platformId = cart.platformId,
                                    deliverySlotMov = true,
                                    isHorecaBuyer = true,
                                    logChargesApplicable = true,
                                    useRewardsV2 = true,
                                    useUdaanCoins = true,
                                    couponIds = couponIds.toSet(),
                                    fetchMovViolations = false,
                                )
                                logger.info("paymentMethodsReq: $paymentMethodsReq")
                                val paymentMethodsRes =
                                    horecaOrderFormClient.fetchOrderPaymentMethods(paymentMethodsReq).execute().await()
                                logger.info("Passed after res")
                                val cartWithOrderInfo = cart.copy(
                                    orderInfo = CartOrderInfo(orderIds = orderIds),
                                    updatedAt = DateTime.now().toDate(),
                                )
                                cartWriteRepo.update(cartWithOrderInfo, emptyList(), emptyList(), emptyList())
                                Response(status = true, data = paymentMethodsRes).right()
                            }
                    } ?: Response(status = true, data = null).right()
                }.getOrElse { e ->
                    when (e) {
                        is NoCartFoundException -> Response(status = true, data = null).right()
                        else -> {
                            logger.error("Failed to build horeca payments response: ", e)
                            exceptionHandler.handle(e).left()
                        }
                    }
                }
            }
        }
    }

    // Note: Mario is expected to have single seller cart
    suspend fun fetchMarioPaymentMethods(
        request: BaseRequestDto
    ): Either<String, BaseResponseDto> {
        request as FetchCartReqDto
        return when (val fetchResponse = fetchCart(request)) {
            is Either.Left -> fetchResponse.value.message.left()
            is Either.Right -> {
                kotlin.runCatching {
                    fetchResponse.value.getCartContext()?.let { cartContext ->
                        cartContext.getDetailedCart(request.buyerOrgUnitId, skipOOSItems = true)?.let { detailedCart ->
                            val cart = detailedCart.cart
                            val promoContext = cartContext.getValue<PromoContext>(CartContextKey.PROMOTION_CONTEXT)
                            val couponIds = promoContext?.getAppliedCouponIds() ?: emptyList()
                            val item = cart.items.firstOrNull() ?: throw EmptyCartException("Cart is empty")
                            val listingProductItem = item.product as ListingProductItem
                            val categoryGroupId = detailedCart.getCategoryGroupId()
                            val cartRes = serviceSelectorFactory.getResponseBuilder(request.platformId).build(
                                responseBuilderData = ResponseDataBuilder.create(),
                                responseFlags = request.responseFlags,
                                cartContext = cartContext,
                                buyerOrgUnitId = request.buyerOrgUnitId,
                            )
                            when (cartRes) {
                                is Either.Left -> cartRes.value.left()
                                is Either.Right -> {
                                    val orderFormResponseV2Dto = createOrder(
                                        sellerId = listingProductItem.sellerId,
                                        buyerId = request.buyerId,
                                        categoryGroupId = categoryGroupId,
                                        platformId = cart.platformId,
                                        items = cart.items,
                                        buyerOrgUnitId = request.buyerOrgUnitId,
                                    )
                                    logger.info("Created order ${orderFormResponseV2Dto.orderId}")
                                    val paymentMethods = cartClient.prepareOrderFormV3(
                                        sellerCartId = orderFormResponseV2Dto.orderId,
                                        buyerOrgId = request.buyerId,
                                        isPlrSession = false,
                                        couponId = couponIds.firstOrNull() ?: request.couponIds.firstOrNull(),
                                    ).executeAwait()
                                    val cartWithOrderInfo = cart.copy(
                                        orderInfo = CartOrderInfo(orderIds = listOf(orderFormResponseV2Dto.orderId)),
                                        updatedAt = DateTime.now().toDate(),
                                    )
                                    val paymentResponse = CartPaymentResDto(
                                        cart = cartRes.value as CartResDto,
                                        paymentMethods = paymentMethods.toOrderFormPaymentMethod()
                                    )
                                    cartWriteRepo.update(cartWithOrderInfo, emptyList(), emptyList(), emptyList())
                                    Response(status = true, data = paymentResponse).right()
                                }
                            }
                        }
                    } ?: throw NoCartFoundException("No valid cart found")
                }.getOrElse { e ->
                    when (e) {
                        is NoCartFoundException -> Response(status = true, data = null).right()
                        else -> {
                            logger.error("Failed to build mario payments response: ", e)
                            exceptionHandler.handle(e).left()
                        }
                    }
                }
            }
        }
    }

    suspend fun pacmanCheckout(
        request: BaseRequestDto,
    ): Either<String, BaseResponseDto> {
        request as LegacyMultiSellerPlaceReqDto
        return kotlin.runCatching {
            val placeMultiOrderRequest = request.placeMultiOrder
            val couponIds = if (request.placeMultiOrder.couponIds.isEmpty()) {
                val cartsMap = placeMultiOrderRequest.orderIds.parallelMap { orderId ->
                    cartReadRepo.findByOrderId(request.buyerId, orderId)
                }.filterNotNull().groupBy { it.id }
                logger.info("Checkout carts for ${placeMultiOrderRequest.orderIds} are ${cartsMap.keys}")
                if (cartsMap.isEmpty()) {
                    emptyList()
                } else {
                    val fetchResponse = fetchCart(
                        FetchCartReqDto(
                            buyerId = request.buyerId,
                            platformId = request.platformId,
                            requester = com.udaan.cart.models.common.Requester(
                                userId = "",
                                orgId = request.buyerId,
                            ),
                            cartId = cartsMap.keys.first(),
                            buyerOrgUnitId = placeMultiOrderRequest.buyerOrgUnitId,
                            cartSelection = null,
                        )
                    )
                    when (fetchResponse) {
                        is Either.Left -> emptyList()
                        is Either.Right -> {
                            kotlin.runCatching {
                                fetchResponse.value.getCartContext()?.let { cartContext ->
                                    cartContext.getCartWithPromos(
                                        placeMultiOrderRequest.buyerOrgUnitId, skipOOSItems = true
                                    )
                                    val promoContext =
                                        cartContext.getValue<PromoContext>(CartContextKey.PROMOTION_CONTEXT)
                                    promoContext?.getAppliedCouponIds() ?: emptyList()
                                } ?: emptyList()
                            }.getOrElse { e ->
                                logger.error("Error while cart coupons: ${e.message}", e)
                                emptyList()
                            }
                        }
                    }
                }
            } else placeMultiOrderRequest.couponIds
            logger.info("Coupon ids for checkout: $couponIds")
            logger.info("Legacy checkout request: $request")
            val placeOrderResponse = multiSellerCartClient.placeMultiOrder(
                orderIds = placeMultiOrderRequest.orderIds,
                paymentMode = placeMultiOrderRequest.paymentMode,
                buyerOrgUnitId = placeMultiOrderRequest.buyerOrgUnitId,
                isGstinRequested = placeMultiOrderRequest.gstinRequested,
                gstin = placeMultiOrderRequest.gstin,
                slotId = placeMultiOrderRequest.slotId,
                notUseUdaanCoins = placeMultiOrderRequest.notUseUdaanCoins,
                buyerIpAddr = placeMultiOrderRequest.ipAddress,
                platformId = placeMultiOrderRequest.platformId,
                deliverySlotMov = true,
                billToOrgUnitId = placeMultiOrderRequest.billToOrgUnitId,
                useRewardsV2 = false,
                orderSlotMap = placeMultiOrderRequest.orderSlotMap,
                couponIds = couponIds,
                itemLevelDeliverySlot = placeMultiOrderRequest.itemLevelDeliverySlot
            ).executeAwait()
            logger.info("Legacy checkout response: $placeOrderResponse")
            Response(status = true, data = placeOrderResponse).right()
        }.getOrElse { e ->
            logger.error("Error while checking out: ${e.message}", e)
            exceptionHandler.handle(e).left()
        }
    }

    suspend fun markPlaced(
        request: BaseRequestDto,
    ): Either<String, BaseResponseDto> {
        request as MarkPlacedReqDto
        return kotlin.runCatching {
            val cartsMap = request.orderIds.parallelMap { orderId ->
                cartReadRepo.findByOrderId(request.buyerId, orderId)
            }.filterNotNull().groupBy { it.id }
            logger.info("Carts by order ids: ${request.orderIds} are $cartsMap")
            cartsMap.map { (_, carts) ->
                carts.firstOrNull()?.let { cart ->
                    val placedCart = cart.copy(state = CartState.ORDER_PLACED, updatedAt = DateTime.now().toDate())
                    cartWriteRepo.update(placedCart, emptyList(), emptyList(), emptyList())
                }
            }
            Response(status = true, data = null).right()
        }.getOrElse { e ->
            exceptionHandler.handle(e).left()
        }
    }

    private suspend fun createOrder(
        sellerId: String,
        buyerId: String,
        categoryGroupId: String,
        platformId: SellingPlatform,
        items: List<CartItem>,
        buyerOrgUnitId: String? = null,
    ): OrderFormResponseV2Dto {
        val order = orderFormClientV2.createOrderForm(
            createOrderFormReqV2Dto = CreateOrderFormReqV2Dto(
                platformId = platformId,
                categoryGroupId = categoryGroupId,
                buyerId = buyerId,
                sellerId = sellerId,
                creationStrategy = CreationStrategy.ORDER_FORM_CREATE_DUPLICATE,
                requester = Requester(
                    userId = "",
                    orgId = buyerId,
                )
            )
        ).executeAwait()

        val listingRequests = items.map { item ->
            val product = item.product as ListingProductItem
            ListingRequest(listingId = product.listingId,
                salesUnitId = product.salesUnitId,
                quantity = item.quantity,
                puInfo = item.priceDetails.puInfo?.userPU?.let { userSelectedPU ->
                    PUReqDto(
                        puType = userSelectedPU
                    )
                })
        }
        return orderFormClientV2.editOrderForm(
            EditOrderFormReqV2Dto(
                orderId = order.orderId,
                buyerId = buyerId,
                sellerId = sellerId,
                categoryGroupId = categoryGroupId,
                buyerOrgUnitId = buyerOrgUnitId,
                platformId = platformId,
                listingRequests = listingRequests,
                promotionInfo = null,
                requester = Requester(
                    userId = "", orgId = buyerId
                )
            )
        ).executeAwait()
    }

    private suspend fun fetchCart(
        request: FetchCartReqDto
    ): Either<OpError, FetchCartContext<FetchCartReqDto>> {
        val selectorData = DefaultSelectorData(
            buyerId = request.buyerId,
            platformId = request.platformId,
            cartSelection = request.cartSelection,
            cartId = request.cartId,
        )

        val fetchContext = FetchCartContext(
            request = request,
            selectorData = selectorData,
            cartSelector = serviceSelectorFactory.getCartSelector(request.platformId),
        )

        return serviceSelectorFactory.getFetchLifecycle(request).execute(
            fetchContext, FetchCartHookCollection(
                postCartFetchHooks = emptyList(),
            )
        )
    }
}
