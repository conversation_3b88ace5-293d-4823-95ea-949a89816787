package com.udaan.cart.core.operations.v1.validators

import arrow.core.Either
import arrow.core.NonEmptyList
import arrow.core.left
import arrow.core.nonEmptyListOf
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.common.helpers.Validator
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.operations.v1.contexts.CreateCartContext
import com.udaan.cart.models.default.CreateCartReqDto
import com.udaan.orderform.common.providers.OrgProvider
import kotlinx.coroutines.future.await

@Singleton
class CartCreationValidator @Inject constructor(
    private val orgProvider: OrgProvider,
    private val buyerOrgValidator: BuyerOrgValidator,
) : Validator<CreateCartContext<CreateCartReqDto>, CreateCartContext<CreateCartReqDto>> {
    override suspend fun validate(
        data: CreateCartContext<CreateCartReqDto>
    ): Either<OpError, CreateCartContext<CreateCartReqDto>> {
        val request = data.request
        if (request.buyerId.isEmpty()) {
            return OpError(message = "Empty buyer id received").left()
        }
        val buyerOrgExtended = orgProvider.fetchOrgDetails(request.buyerId).await()
        data.buyerOrg = buyerOrgExtended
        return buyerOrgValidator.validate(buyerOrgExtended).map {
            data
        }
    }
}
