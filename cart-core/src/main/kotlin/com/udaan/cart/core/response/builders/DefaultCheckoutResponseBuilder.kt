package com.udaan.cart.core.response.builders

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.google.inject.Singleton
import com.udaan.cart.core.common.helpers.TimeHelper
import com.udaan.cart.core.common.providers.CheckoutDetails
import com.udaan.cart.core.operations.v1.contexts.CheckoutCartContext
import com.udaan.cart.core.response.CheckoutResponseBuilder
import com.udaan.cart.models.BaseResponseDto
import com.udaan.cart.models.default.CheckoutRequestDto
import com.udaan.cart.models.default.CheckoutResponseDto
import com.udaan.cart.models.default.RequestExtraDataKeys
import com.udaan.common.utils.kotlin.logger
import java.security.MessageDigest
import javax.inject.Inject
import kotlin.math.absoluteValue

@Singleton
class DefaultCheckoutResponseBuilder @Inject constructor(
    private val timeHelper: TimeHelper
) : CheckoutResponseBuilder {
    override suspend fun build(
        context: CheckoutCartContext<CheckoutRequestDto>
    ): Either<String, BaseResponseDto> {
        val earliestSla = context.getValue<CheckoutDetails>(CheckoutCartContext.CHECKOUT_DETAILS)
                            ?.orderSlaMap
                            ?.values
                            ?.minOfOrNull { it.sla }

        val orderLineOTP = earliestSla?.let {
            generateDeliveryCodeByOrgAndDate(context.request.buyerOrgUnitId, context.request.extraData,earliestSla)
                .also {
                    logger().value.info("Generated OTP for orgUnitId ${context.request.buyerOrgUnitId}: $it")
                }
        }
        return context.getValue<CheckoutDetails>(CheckoutCartContext.CHECKOUT_DETAILS)?.let { checkoutDetails ->
            CheckoutResponseDto(
                placedOrderIds = checkoutDetails.placedOrderIds,
                prePaymentId = checkoutDetails.prePaymentId,
                mainOrderId = checkoutDetails.mainOrderId,
                orderSlasMap = checkoutDetails.orderSlaMap,
                otp = orderLineOTP
            ).right()
        } ?: "Unable to complete cart checkout".left()
    }

    /**
     * Generates a 6-digit delivery code based on the organization unit ID and current date.
     *
     * @param orgUnitId The unique identifier of the buyer's organization unit.
     * @param extraData
     * @param deliverysla Delivery SLA for Order
     * @return A 6-digit delivery code as a string.
     */
    private fun generateDeliveryCodeByOrgAndDate(
        orgUnitId: String,
        extraData: Map<RequestExtraDataKeys, Any> = emptyMap(),
        deliverysla: Long
    ): String? {
        return kotlin.runCatching {
            if (extraData[RequestExtraDataKeys.OTP_ENABLED].toString().toBoolean().not()) return null
            val deliveryDate = timeHelper.getDateStringFromEpoch(deliverysla)
            val combinedInput = orgUnitId + deliveryDate

            val hashBytes = MessageDigest.getInstance("SHA-256").digest(combinedInput.toByteArray())
            // Use the first 8 bytes of the hash and convert it into a long number
            val number = hashBytes.take(8).fold(0L) { acc, byte -> acc * 256 + (byte.toInt() and 0xFF) }
            (number % 1_000_000).absoluteValue.toString().padStart(length = 6, padChar = '0')
        }.getOrElse {
            logger().value.error("Failed to generate delivery code", it)
            null
        }
    }
}
