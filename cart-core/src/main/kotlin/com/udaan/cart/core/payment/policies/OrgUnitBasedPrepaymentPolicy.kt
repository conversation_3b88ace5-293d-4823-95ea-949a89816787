package com.udaan.cart.core.payment.policies

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.payment.PaymentEvalContext
import com.udaan.catalog.client.CategoryGroupV2
import com.udaan.common.utils.kotlin.logger
import com.udaan.proto.models.ModelV1.OrgUnit

@Singleton
class OrgUnitBasedPrepaymentPolicy  @Inject constructor(
    private val policyExemptionValidator: PolicyExemptionValidator,
) : AbstractPrePaymentPolicy() {

    companion object {
        private val logger by logger()
        val POLICY_NAME = this::class.java.simpleName
        val pharmaCitiesWithNoPrepayment = listOf("Bangalore","Indore","Jaipur","Kolkata","Pune")
        const val BPS = HUNDRED_PERCENT_BPS
        const val INSTRUCTIONS = "You are placing a high value order on cash payment. " +
                "Thank you for your trust. We would need to 100% pre-pay on these order to cover for logistics risk."
    }

    override fun getName(): String = POLICY_NAME

    override suspend fun evaluate(paymentContext: PaymentEvalContext): PrePaymentResult {
        val buyerId = paymentContext.detailedCart.cart.buyerId
        return if (policyExemptionValidator.isExempted(POLICY_NAME, buyerId)) {
            defaultResult()
        } else {
            val prepaymentRequired = isPrepaymentRequired(paymentContext)
            if (prepaymentRequired) {
                preparePrepaymentResult(paymentContext)
            } else {
                defaultResult()
            }
        }
    }

    private fun isPrepaymentRequired(paymentContext: PaymentEvalContext): Boolean {
        val categoryGroupId = paymentContext.detailedCart.getCategoryGroupId()
        return if (categoryGroupId == CategoryGroupV2.Pharma.id) {
            val buyerCity = getBuyerCity(paymentContext)
            if (buyerCity == null) {
                logger.info("Unable to determine buyerCity for buyer ${paymentContext.buyerOrg?.orgAccount?.orgId} &" +
                    " orgUnit = ${paymentContext.buyerOrgUnit?.orgUnitId}. Default to prepayment")
                return true
            }
            buyerCity !in pharmaCitiesWithNoPrepayment
        } else {
            false
        }
    }

    private fun getBuyerCity(paymentContext: PaymentEvalContext): String? {
        return paymentContext.buyerOrgUnit?.let { orgUnit ->
            getCityForOrgUnit(orgUnit) ?: run {
                paymentContext.buyerOrg?.orgAccount?.orgUnitsMap?.let { orgUnitsMap ->
                    val headOrgUnitResponse = orgUnitsMap[paymentContext.buyerOrg.orgAccount?.headOfficeOrgUnitRef]
                    getCityForOrgUnit(headOrgUnitResponse)
                }
            }
        }
    }

    private fun preparePrepaymentResult(paymentContext: PaymentEvalContext): PrePaymentResult {
        val cartAmount = paymentContext.detailedCart.totalAmount()
        return prepareResult(
            bps = BPS,
            totalAmount = cartAmount,
            description = null,
            instruction = INSTRUCTIONS,
        )
    }

    private fun getCityForOrgUnit(orgUnit: OrgUnit?) = orgUnit?.unitAddress?.city
}
