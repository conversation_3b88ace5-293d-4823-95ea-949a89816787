package com.udaan.cart.core.common.providers

import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.catalog.models.ModelV2
import com.udaan.orderform.common.providers.CatalogProvider
import kotlinx.coroutines.future.await

data class MoqData(
    val listingMoq: Int,
    val salesUnitMoq: Int
)

interface MOQProvider {
    suspend fun find(
        listingId: String,
        salesUnitId: String
    ): MoqData

    suspend fun find(
        listing: ModelV2.TradeListing,
        salesUnitId: String
    ): MoqData
}

class MOQProviderImpl @Inject constructor(
    private val catalogProvider: CatalogProvider
) : MOQProvider {
    override suspend fun find(
        listingId: String,
        salesUnitId: String
    ): MoqData {
        val listing = catalogProvider.getListing(listingId, salesUnitId)
        return find(listing, salesUnitId)
    }

    override suspend fun find(
        listing: ModelV2.TradeListing,
        salesUnitId: String
    ): MoqData = MoqData(
        listingMoq = listing.listingMoq.moq,
        salesUnitMoq = listing.salesUnitList.firstOrNull {
            it.salesUnitId == salesUnitId
        }?.moqPolicy?.moq ?: 1
    )
}
