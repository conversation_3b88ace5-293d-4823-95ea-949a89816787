package com.udaan.cart.core.exceptions

interface ExceptionHandler {
    fun handle(exception: Throwable): String
}

abstract class BaseCartException(message: String, cause: Throwable?) : Exception(message, cause) {
    constructor(message: String) : this(message, null)
}

class InvalidCartSelectionException(message: String, cause: Throwable? = null) : BaseCartException(message, cause)

class NoContextException(message: String, cause: Throwable? = null) : BaseCartException(message, cause)

class NoCartFoundException(message: String, cause: Throwable? = null) : BaseCartException(message, cause)

class NoPaymentMethodsException(message: String, cause: Throwable? = null) : BaseCartException(message, cause)

class EmptyCartException(message: String, cause: Throwable? = null) : BaseCartException(message, cause)

class LevyGenerationException(message: String, cause: Throwable? = null) : BaseCartException(message, cause)

class ViolationsBuildException(message: String, cause: Throwable? = null) : BaseCartException(message, cause)

class GenericCartException(message: String, cause: Throwable? = null) : BaseCartException(message, cause)

class ConcurrencyException(message: String, cause: Throwable? = null) : BaseCartException(message, cause)

class OrgIdentityModelException(message: String, cause: Throwable? = null) : BaseCartException(message, cause)
