package com.udaan.cart.core.operations.v1.lifecycles.impl

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.google.inject.Inject
import com.google.inject.Injector
import com.google.inject.name.Named
import com.udaan.cart.core.context.CartContextFactory
import com.udaan.cart.core.db.repo.CartWriteRepo
import com.udaan.cart.core.domain.models.CartItem
import com.udaan.cart.core.domain.models.CreationStrategy
import com.udaan.cart.core.exceptions.InvalidCartSelectionException
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.exceptions.NoCartFoundException
import com.udaan.cart.core.hooks.Hook
import com.udaan.cart.core.operations.v1.contexts.CreateCartContext
import com.udaan.cart.core.operations.v1.contexts.EditCartContext
import com.udaan.cart.core.operations.v1.hooks.CreateCartHookCollection
import com.udaan.cart.core.operations.v1.hooks.EditCartHookCollection
import com.udaan.cart.core.operations.v1.hooks.EditProductHookCollection
import com.udaan.cart.core.operations.v1.hooks.impl.MaxCartLinesCheckHook
import com.udaan.cart.core.operations.v1.lifecycles.AbstractCreateCartLifecycle
import com.udaan.cart.core.operations.v1.lifecycles.AbstractEditCartLifecycle
import com.udaan.cart.core.operations.v1.lifecycles.product.ProductHandler
import com.udaan.cart.models.default.CreateCartReqDto
import com.udaan.cart.models.default.EditCartReqDto
import com.udaan.common.utils.kotlin.logger

open class DefaultEditCartLifecycle @Inject constructor(
    injector: Injector,
    private val cartContextFactory: CartContextFactory,
    private val productHandler: ProductHandler,
    @Named("default_create_cart")
    private val defaultCreateCartLifecycle: AbstractCreateCartLifecycle<CreateCartReqDto>,
    private val cartWriteRepo: CartWriteRepo,
) : AbstractEditCartLifecycle<EditCartReqDto>() {
    companion object {
        private val logger by logger()
    }

    @Suppress("UNCHECKED_CAST")
    private val postCartValidationHooks = listOf(
        injector.getInstance(MaxCartLinesCheckHook::class.java) as Hook<OpError, EditCartContext<EditCartReqDto>>
    )

    override suspend fun setupHooks(
        hooks: EditCartHookCollection<OpError, EditCartContext<EditCartReqDto>>,
        context: EditCartContext<EditCartReqDto>
    ): EditCartHookCollection<OpError, EditCartContext<EditCartReqDto>> {
        return hooks.copy(
            doValidationHooks = hooks.doValidationHooks,
            doPostCartFetch = hooks.doPostCartFetch,
            doPostEditHooks = hooks.doPostEditHooks.plus(postCartValidationHooks)
        )
    }

    override suspend fun doPreValidations(
        context: EditCartContext<EditCartReqDto>
    ): Either<OpError, EditCartContext<EditCartReqDto>> {
        return context.right()
    }

    override suspend fun doFetchCart(
        context: EditCartContext<EditCartReqDto>
    ): Either<OpError, EditCartContext<EditCartReqDto>> {
        val cart = context.cartSelector.find(context.selectorData)
        return cart?.let {
            val newCartContext = cartContextFactory.createCartContext(
                cartId = cart.id, cart = cart
            )
            context.setCartContext(newCartContext)
            context.right()
        } ?: kotlin.run {
            if (context.request.cartId.isNullOrBlank().not()) {
                throw NoCartFoundException("[Edit cart] No active cart exists for cartId - ${context.request.cartId}")
            }
            val cartSelection = context.request.cartSelection
                ?: throw InvalidCartSelectionException("Invalid cart selection parameters received")
            val createReqDto = CreateCartReqDto(
                buyerId = context.request.buyerId,
                platformId = context.request.platformId,
                cartSelection = cartSelection,
            )
            val createContext = CreateCartContext(
                request = createReqDto,
                creationStrategy = CreationStrategy.CART_CREATE_UNIQUE,
                selectorData = context.selectorData,
                cartSelector = context.cartSelector
            )
            val createResponse = defaultCreateCartLifecycle.execute(
                context = createContext,
                hooks = CreateCartHookCollection(
                    doPostOrderCreate = emptyList(),
                ),
            )
            when (createResponse) {
                is Either.Left -> createResponse.value.left()
                is Either.Right -> {
                    val createdCartContext = createResponse.value.getCartContext()
                    createdCartContext?.let {
                        val newCartContext = cartContextFactory.createCartContext(
                            cartId = it.getCartId(), cart = it.getCart()
                        )
                        context.setCartContext(newCartContext)
                    }
                    context.right()
                }
            }
        }
    }

    override suspend fun doEditCart(
        context: EditCartContext<EditCartReqDto>
    ): Either<OpError, EditCartContext<EditCartReqDto>> {
        return productHandler.edit(
            context, hooks = EditProductHookCollection<OpError, EditCartContext<EditCartReqDto>>(
                doValidationHooks = emptyList(),
                doPostCartFetch = emptyList(),
                doPostEditHooks = emptyList(),
                doPostProductFetch = emptyList(),
                doPostPriceFetch = emptyList(),
            )
        )
    }

    override suspend fun doStoreCart(
        context: EditCartContext<EditCartReqDto>
    ): Either<OpError, EditCartContext<EditCartReqDto>> {
        context.getEditedItems()?.let { editedItems ->
            cartWriteRepo.update(
                cart = context.getCartContext()?.getCart()!!,
                newItems = editedItems.newItems,
                updatedItems = editedItems.editedItems,
                discardedItems = editedItems.discardedItems,
            )
        }
        context.getCartContext()?.getCartId()?.let { cartId ->
            context.setCartContext(cartContextFactory.createCartContext(cartId, null))
        }

        return context.right()
    }
}

internal data class EditCartItemsHolder(
    val discardedItems: List<CartItem> = emptyList(),
    val editedItems: List<CartItem> = emptyList(),
    val newItems: List<CartItem> = emptyList(),
) {
    fun addNewItem(item: CartItem): EditCartItemsHolder {
        val newItems = this.newItems.toMutableList()
        newItems.add(item)
        return this.copy(newItems = newItems)
    }

    fun addDiscardedItem(item: CartItem): EditCartItemsHolder {
        val discardItems = this.discardedItems.toMutableList()
        discardItems.add(item)
        return this.copy(discardedItems = discardItems)
    }

    fun addEditedItem(item: CartItem): EditCartItemsHolder {
        val editedItems = this.editedItems.toMutableList()
        editedItems.add(item)
        return this.copy(editedItems = editedItems)
    }
}
