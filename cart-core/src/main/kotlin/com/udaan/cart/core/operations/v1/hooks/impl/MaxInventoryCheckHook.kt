package com.udaan.cart.core.operations.v1.hooks.impl

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.cart.core.hooks.BaseOperationHook
import com.udaan.cart.core.operations.v1.contexts.EditCartContext
import com.udaan.cart.models.common.ListingProduct
import com.udaan.cart.models.default.EditCartReqDto
import com.udaan.common.utils.kotlin.logger
import com.udaan.orderform.common.models.ListingItemData
import com.udaan.orderform.common.providers.InventoryProvider

@Singleton
class MaxInventoryCheckHook @Inject constructor(
    private val inventoryProvider: InventoryProvider
) : BaseOperationHook<OpError, EditCartContext<EditCartReqDto>> {

    companion object {
        private val logger by logger()
    }

    override suspend fun execute(
        context: EditCartContext<EditCartReqDto>
    ): Either<OpError, EditCartContext<EditCartReqDto>> {
        return kotlin.runCatching {
            context.getCartContext()?.getCart()?.let { cart ->
                context.getCartContext()?.getBuyerOrgUnit(context.request.buyerOrgUnitId)?.let { buyerOrgUnit ->
                    val listingsMap = context.getListingsMap()
                    val listingItemsData = context.request.products.filter {
                        (it as ListingProduct).quantity > 0
                    }.map { product ->
                        product as ListingProduct
                        cart.items.find { item ->
                            val productItem = item.product as ListingProductItem
                            product.listingId == productItem.listingId &&
                                    product.salesUnitId == productItem.salesUnitId &&
                                    product.quantity > item.quantity
                        }?.let {
                            it.product as ListingProductItem
                            ListingItemData(
                                listingId = it.product.listingId,
                                salesUnitId = it.product.salesUnitId,
                                quantity = product.quantity,
                                itemId = it.id
                            )
                        } ?: ListingItemData(
                            listingId = product.listingId,
                            salesUnitId = product.salesUnitId,
                            quantity = product.quantity,
                            itemId = product.salesUnitId
                        )
                    }
                    if (listingItemsData.isNotEmpty()) {
                        val groupInventory = inventoryProvider.fetchMaxAllowedInventory(
                            buyerOrgUnitId = buyerOrgUnit.orgUnitId,
                            deliveryPincode = buyerOrgUnit.unitAddress.pincode,
                            listingItemsData = listingItemsData,
                            platform = context.request.platformId,
                        )
                        logger.info("[MaxInventoryCheckHook] Group Inventory: $groupInventory")
                        val invalidListingsData =
                            groupInventory.itemsInventory.filter { !it.hasValidQuantity }
                                .mapNotNull { maxItemInventory ->
                                    if (maxItemInventory.listingItemsData.size > 1) {
                                        val listingTitles =
                                            maxItemInventory.listingItemsData.mapNotNull { listingItemData ->
                                                listingsMap[listingItemData.listingId]?.let {
                                                    it.generatedTitle.ifEmpty { it.title }
                                                }
                                            }.joinToString(", ")
                                        "$listingTitles together can't cross ${maxItemInventory.maxAllowedQuantity} " +
                                                maxItemInventory.measureUnit.name
                                    } else {
                                        maxItemInventory.listingItemsData.firstOrNull()?.let { listingItemData ->
                                            listingsMap[listingItemData.listingId]?.let {
                                                it.generatedTitle.ifEmpty { it.title }
                                            }?.let { listingTitle ->
                                                "Max allowed quantity for $listingTitle is ${maxItemInventory.maxAllowedQuantity}"
                                            }
                                        }
                                    }
                                }
                        if (invalidListingsData.isNotEmpty()) {
                            OpError(
                                message = invalidListingsData.take(2).joinToString(", "),
                            ).left()
                        } else context.right()
                    } else context.right()
                } ?: context.right()
            } ?: context.right()
        }.getOrElse { exception ->
            logger.error("Failed to validate max inventory: ${exception.message}", exception)
            context.right()
        }
    }
}
