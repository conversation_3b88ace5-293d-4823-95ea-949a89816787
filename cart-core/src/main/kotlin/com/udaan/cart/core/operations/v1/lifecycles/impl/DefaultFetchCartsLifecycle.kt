package com.udaan.cart.core.operations.v1.lifecycles.impl

import arrow.core.*
import com.google.inject.Inject
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.CartContextFactory
import com.udaan.cart.core.context.MultiCartContext
import com.udaan.cart.core.operations.v1.contexts.FetchCartsContext
import com.udaan.cart.core.operations.v1.lifecycles.AbstractFetchCartsLifecycle
import com.udaan.cart.models.default.FetchCartReqDto
import com.udaan.common.utils.kotlin.logger

class DefaultFetchCartsLifecycle @Inject constructor(
    private val cartContextFactory: CartContextFactory,
) : AbstractFetchCartsLifecycle<FetchCartReqDto>() {
    companion object {
        private val logger by logger()
    }

    override suspend fun doFetch(
        context: FetchCartsContext<FetchCartReqDto>
    ): Either<OpError, FetchCartsContext<FetchCartReqDto>> {
        return kotlin.runCatching {
            val carts = context.cartSelector.find(context.selectorData) ?: emptyList()
            val cartsContext = cartContextFactory.createCartsContext(carts)
            context.setCartsContext(cartsContext)
            context.right()
        }.getOrElse { ex ->
            logger.error("Failed to fetch cart", ex)
            OpError(message = "Failed to fetch cart").left()
        }
    }
}
