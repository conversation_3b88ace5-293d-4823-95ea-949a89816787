package com.udaan.cart.core.operations.v1.hooks.impl

import arrow.core.*
import com.google.inject.Singleton
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.hooks.BaseOperationHook
import com.udaan.cart.core.operations.v1.contexts.EditCartContext
import com.udaan.cart.models.common.ListingProduct
import com.udaan.cart.models.default.EditCartReqDto
import com.udaan.catalog.models.ModelV2
import com.udaan.common.utils.kotlin.logger

@Singleton
class IndividuallyBuyableCheckHook : BaseOperationHook<OpError, EditCartContext<EditCartReqDto>> {

    companion object {
        private val logger by logger()
    }

    override suspend fun execute(
        context: EditCartContext<EditCartReqDto>
    ): Either<OpError, EditCartContext<EditCartReqDto>> {
        return kotlin.runCatching {
            logger.info("Checking IndividuallyBuyableCheckHook")
            context.getCartContext()?.getCart()?.let { cart ->
                val listingsMap = context.getListingsMap()
                val notIndividuallyBuayableMap = context.request.products.filter {
                    (it as ListingProduct).quantity > 0
                }.map { product ->
                    product as ListingProduct
                    listingsMap[product.listingId]?.let {
                        it.listingId to isListingNotIndividuallyBuyable(it)
                    } ?: (product.listingId to false)
                }
                val buyabilityErrors = notIndividuallyBuayableMap.filter { it.second }.map { (listingId, _) ->
                    listingsMap[listingId]?.let { listing ->
                        listing.generatedTitle.ifEmpty { listing.title }
                    }
                }
                if (buyabilityErrors.isNotEmpty()) {
                    val listingNames = buyabilityErrors.joinToString(", ")
                    OpError(message = "$listingNames not individually buyable").left()
                } else context.right()
            } ?: context.right()
        }.getOrElse { e ->
            logger.error("Failed to check individual buyability while editing cart: ${e.message}", e)
            context.right()
        }
    }

    private fun isListingNotIndividuallyBuyable(tradeListing: ModelV2.TradeListing): Boolean {
        return tradeListing.config.individuallyBuyable.blockIndividualBuyability
    }
}