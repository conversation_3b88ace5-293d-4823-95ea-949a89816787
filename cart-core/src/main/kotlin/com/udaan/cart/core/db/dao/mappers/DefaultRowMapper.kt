package com.udaan.cart.core.db.dao.mappers

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import microsoft.sql.DateTimeOffset
import org.jdbi.v3.core.mapper.RowMapper
import org.jdbi.v3.core.statement.StatementContext
import org.joda.time.DateTime
import java.sql.ResultSet
import java.sql.Timestamp
import java.time.Instant
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.reflect.KParameter
import kotlin.reflect.full.primaryConstructor
import kotlin.reflect.jvm.isAccessible
import kotlin.reflect.jvm.javaType

open class DefaultRowMapper<C : Any> constructor(clazz: Class<C>) : RowMapper<C> {
    companion object {
        val mapper = ObjectMapper().registerKotlinModule()
    }

    private val klass = clazz.kotlin

    override fun map(rs: ResultSet?, ctx: StatementContext?): C? {
        return rs?.let {
            val constructor = klass.primaryConstructor!!
            constructor.isAccessible = true
            val validParametersByName = constructor.parameters
                .filter { it.kind == KParameter.Kind.VALUE && it.name != null }.associateBy {
                    it.name!!.lowercase(Locale.getDefault())
                }
            val matchingParams: MutableList<Pair<KParameter, Any?>> = mutableListOf()
            for (i: Int in 1..rs.metaData.columnCount) {
                rs.metaData.getColumnLabel(i).replace("_", "").lowercase(Locale.getDefault())
                    .let { colLabel ->
                        validParametersByName[colLabel]
                            ?.let { param: KParameter ->
                                val res = mapField(i, rs, param)
                                matchingParams.add(res)
                            }
                    }
            }

            val paramsThatArePresent = matchingParams.map { it.first }.toHashSet()

            // things missing from the result set that are Nullable and not optional should be set to Null
            val nullablesThatAreAbsent =
                constructor.parameters.filter {
                    !it.isOptional && it.type.isMarkedNullable && it !in paramsThatArePresent
                }.map {
                    Pair(it, null)
                }

            // things that are missing from the result set but are defaultable
            val defaultableThatAreAbsent =
                constructor.parameters.filter {
                    it.isOptional && !it.type.isMarkedNullable && it !in paramsThatArePresent
                }.toSet()

            val finalParams = (matchingParams + nullablesThatAreAbsent)
                .filterNot { it.first in defaultableThatAreAbsent }
                .toMap()
            constructor.callBy(finalParams)
        }
    }

    private fun mapField(
        index: Int,
        rs: ResultSet,
        param: KParameter,
    ): Pair<KParameter, Any?> {
        return if (param.type.isMarkedNullable && rs.getObject(index) == null) {
            Pair(param, null)
        } else {
            when (val paramType = param.type.javaType) {
                Boolean::class.java -> {
                    Pair(param, rs.getBoolean(index))
                }
                String::class.java -> {
                    Pair(param, rs.getString(index))
                }
                java.lang.Integer::class.java,
                Int::class.java -> {
                    Pair(param, rs.getInt(index))
                }
                java.lang.Long::class.java,
                Long::class.java -> {
                    val value = rs.getObject(index)
                    if (value is Number) {
                        Pair(param, rs.getLong(index))
                    } else {
                        val long = getTimestampUTC(index, value, param)
                        Pair(param, long)
                    }
                }
                java.sql.Date::class.java -> {
                    val value = rs.getObject(index)
                    val long = getTimestampUTC(index, value, param)
                    Pair(param, java.sql.Date(long))
                }
                java.util.Date::class.java -> {
                    val value = rs.getObject(index)
                    val long = getTimestampUTC(index, value, param)
                    Pair(param, Date(long))
                }
                DateTime::class.java -> {
                    val value = rs.getObject(index)
                    val long = getTimestampUTC(index, value, param)
                    Pair(param, DateTime(long))
                }
                ZonedDateTime::class.java -> {
                    val value = rs.getObject(index)
                    val dt = getZonedDateTime(index, value, param)
                    Pair(param, dt)
                }
                java.lang.Double::class.java,
                Double::class.java -> {
                    Pair(param, rs.getDouble(index))
                }
                java.lang.Float::class.java,
                Float::class.java -> {
                    Pair(param, rs.getFloat(index))
                }
                else -> {
                    val value = rs.getString(index)
                    if ((param.type.javaType as Class<*>).isEnum) {
                        val enumClass = (param.type.javaType as Class<*>)
                        Pair(
                            param,
                            enumClass.getMethod("valueOf", String::class.java)
                                .invoke(null, value)
                        )
                    } else {
                        Pair(
                            param,
                            mapper.readValue(
                                value,
                                Class.forName(paramType.typeName)
                            )
                        )
                    }
                }
            }
        }
    }

    private fun getTimestampUTC(i: Int, value: Any?, param: KParameter): Long {
        return when (value) {
            is Timestamp ->
                value.time + TimeZone.getDefault().rawOffset
            is DateTimeOffset ->
                value.timestamp.time
            else -> throw RuntimeException("Type mismatch: Result set at column number $i " +
                    "cannot be converted to Long Type. param: $param")
        }
    }

    private fun getZonedDateTime(i: Int, value: Any?, param: KParameter): ZonedDateTime {
        return when (value) {
            is Timestamp ->
                ZonedDateTime.ofInstant(
                    Instant.ofEpochMilli(value.time + TimeZone.getDefault().rawOffset), ZoneId.of(
                        ZoneOffset.UTC.id
                    )
                )
            is DateTimeOffset ->
                ZonedDateTime.ofInstant(
                    Instant.ofEpochMilli(value.timestamp.time), ZoneId.of(
                        ZoneOffset.ofTotalSeconds(
                            TimeUnit.MINUTES.toSeconds(value.minutesOffset.toLong()).toInt()
                        ).id
                    )
                )
            else -> throw RuntimeException("Type mismatch: Result set at column number $i " +
                    "cannot be converted to Long Type. param: $param")
        }
    }
}
