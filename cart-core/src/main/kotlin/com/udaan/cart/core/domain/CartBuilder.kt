package com.udaan.cart.core.domain

import com.udaan.cart.core.domain.models.*
import com.udaan.cart.core.utils.IdUtils
import com.udaan.cart.models.common.CartSelection
import com.udaan.proto.models.ModelV1

class CartBuilder {
    fun build(
        buyerId: String,
        platformId: ModelV1.SellingPlatform,
        selection: CartSelection,
        items: List<CartItem> = emptyList(),
        type: CartType = CartType.FORWARD,
    ): Cart {
        return Cart(
            id = IdUtils.getCartId(),
            type = type,
            buyerId = buyerId,
            platformId = platformId,
            selection = selection,
            state = CartState.ACTIVE,
            items = items,
        )
    }
}
