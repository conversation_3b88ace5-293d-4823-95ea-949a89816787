package com.udaan.cart.core.response.builders

import arrow.core.Either
import arrow.core.foldLeft
import arrow.core.left
import arrow.core.right
import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.domain.models.PaymentMethod
import com.udaan.cart.core.exceptions.ExceptionHandler
import com.udaan.cart.core.exceptions.NoPaymentMethodsException
import com.udaan.cart.core.response.ResponseBuilder
import com.udaan.cart.core.response.ResponseBuilderData
import com.udaan.cart.core.response.getPaymentMethodsContext
import com.udaan.cart.models.BaseResponseDto
import com.udaan.cart.models.common.MetadataVariables
import com.udaan.cart.models.common.PaymentCollectionType
import com.udaan.cart.models.common.ResponseFlags
import com.udaan.cart.models.default.PaymentMethodDto
import com.udaan.cart.models.default.PaymentMethodsResDto
import com.udaan.cart.models.default.RewardsPaymentDetails
import com.udaan.common.utils.bpsToPercent
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.paiseToRsStr

class DefaultPaymentMethodsResponseBuilder @Inject constructor(
    @Named("default_exception_handler")
    private val exceptionHandler: ExceptionHandler,
) : ResponseBuilder {

    enum class Variables {
        PENDING_AMOUNT, PAYABLE_AMOUNT, BPS_PERCENTAGE, CREDIT_TYPE, AVAILABLE_CREDIT,
        AVAILABLE_CREDIT_PREFIX, ID, AVAILABLE_CREDIT_OR_OVERDUE
    }

    companion object {
        private val paymentInfoMap = mapOf(
            "ADVANCE" to PaymentMethodInfo(
                title = "Pay Online",
                ctaTitle = "Pay Online - ₹{PAYABLE_AMOUNT}",
                displayName = "Pay Online - ₹{PAYABLE_AMOUNT}",
                description = "Using Credit or Debit cards, UPI, Netbanking or Bank Transfer",
                instructions = "",
                metaData = emptyMap(),
            ),
            "TOKEN" to PaymentMethodInfo(
                title = "Pay Online",
                ctaTitle = "{BPS_PERCENTAGE}% Online Pre-Payment - ₹{PAYABLE_AMOUNT}",
                displayName = "{BPS_PERCENTAGE}% Online Pre-Payment - ₹{PAYABLE_AMOUNT}",
                description = "Balance amount of ₹{PENDING_AMOUNT} is to be paid before or at the time of delivery",
                instructions = "You have exhausted your Cash on Delivery limit. Your limit will be refreshed automatically" +
                        "upon delivery of any pending orders. Your current(max) COD limit is ₹20,000",
                metaData = emptyMap(),
            ),
            "COD" to PaymentMethodInfo(
                title = "Pay on Delivery",
                ctaTitle = "Pay on Delivery - ₹{PAYABLE_AMOUNT}",
                displayName = "Pay on Delivery - ₹{PAYABLE_AMOUNT}",
                description = "Pay via Cash, UPI or QR Code on Delivery",
                instructions = "",
                metaData = emptyMap(),
            ),
            "CREDIT" to PaymentMethodInfo(
                title = "Pay via {CREDIT_TYPE}",
                ctaTitle = "Pay - ₹{PAYABLE_AMOUNT}",
                displayName = "Pay via Credit - ₹{PAYABLE_AMOUNT}",
                description = "Powered by udaanCapital",
                instructions = "",
                metaData = emptyMap(),
            ),
            "TRADE_CREDIT" to PaymentMethodInfo(
                title = "{CATEGORY} Credit",
                ctaTitle = "",
                displayName = "{CATEGORY} Credit",
                description = "{AVAILABLE_CREDIT_PREFIX} Credit: ₹{AVAILABLE_CREDIT_OR_OVERDUE}",
                instructions = "",
                metaData = emptyMap(),
            )
        )
        private const val CREDIT_INSTRUMENT_KEY = "cart_credit_instrument_id"
        private val logger by logger()
    }

    override suspend fun build(
        responseBuilderData: ResponseBuilderData,
        responseFlags: ResponseFlags,
        cartContext: CartContext,
        buyerOrgUnitId: String?
    ): Either<String, BaseResponseDto> {
        return responseBuilderData.getPaymentMethodsContext()?.let { paymentMethodsContext ->
            val paymentMethods = paymentMethodsContext.getPaymentMethods().map { paymentMethod ->
                val paymentInfo = preparePaymentInfo(paymentMethod)
                PaymentMethodDto(
                    title = paymentInfo?.title ?: "",
                    ctaTitle = paymentInfo?.ctaTitle ?: "",
                    collectionType = PaymentCollectionType.valueOf(paymentMethod.collectionType.name),
                    displayName = paymentInfo?.displayName ?: "",
                    description = paymentInfo?.description ?: "",
                    instructions = paymentInfo?.instructions ?: "",
                    offers = emptyList(),
                    payableAmountInPaise = paymentMethod.payableTotalInPaise,
                    remainingPayableAmountInPaise = paymentMethod.remainingTotalInPaise,
                    prepaymentBps = paymentMethod.prepaymentBps,
                    metaData = paymentInfo?.metaData ?: paymentMethod.metaData,
                    rewardsPaymentDetails = paymentMethod.rewardBasedPayment?.let {
                        RewardsPaymentDetails(
                            coins = it.coins,
                            rewardAmountInPaise = it.rewardAmountInPaise,
                            payableAmountInPaise = it.payableAmountInPaise,
                        )
                    }
                )
            }
            PaymentMethodsResDto(
                paymentMethods = paymentMethods
            ).right()
        } ?: exceptionHandler.handle(NoPaymentMethodsException("No applicable payment methods found")).left()
    }

    private fun preparePaymentInfo(paymentMethod: PaymentMethod): PaymentMethodInfo? {
        val paymentInfo = paymentInfoMap[paymentMethod.collectionType.name]
        val availablePurchaseAmount = paymentMethod.metaData[MetadataVariables.AVAILABLE_PURCHASE_AMOUNT.name]?.toString()?.toLong() ?: 0L
        val remainingOverdue = paymentMethod.metaData[MetadataVariables.REMAINING_OVERDUE.name]?.toString()?.toLong() ?: 0L
        val creditType = paymentMethod.getCreditType() ?: paymentMethod.getDefaultCreditType()
        val completeOverdue = remainingOverdue > 0L && availablePurchaseAmount == 0L
        val noOverdue = remainingOverdue == 0L && availablePurchaseAmount > 0
        val variables = mapOf(
            Variables.PAYABLE_AMOUNT.name to paymentMethod.payableTotalInPaise.paiseToRsStr(),
            Variables.PENDING_AMOUNT.name to paymentMethod.remainingTotalInPaise.paiseToRsStr(),
            Variables.BPS_PERCENTAGE.name to paymentMethod.prepaymentBps.bpsToPercent(),
            Variables.CREDIT_TYPE.name to creditType,
            Variables.AVAILABLE_CREDIT.name to availablePurchaseAmount.paiseToRsStr(),
            Variables.AVAILABLE_CREDIT_OR_OVERDUE.name to
                    if(completeOverdue) remainingOverdue.paiseToRsStr()
                    else availablePurchaseAmount.paiseToRsStr(),
            Variables.AVAILABLE_CREDIT_PREFIX.name to
                    when {
                        noOverdue -> "Available"
                        completeOverdue -> "Overdue"
                        else -> "Limited"
                    }
        )
        val metaData =
            if (paymentMethod.collectionType == com.udaan.cart.core.domain.models.PaymentCollectionType.CREDIT) {
                paymentMethod.getCreditInstrumentId()?.let { creditInstrumentId ->
                    logger.info("Credit payment instrument id: $creditInstrumentId")
                    val mutableMetaData = paymentMethod.metaData.toMutableMap()
                    mutableMetaData[CREDIT_INSTRUMENT_KEY] = creditInstrumentId
                    mutableMetaData
                } ?: paymentMethod.metaData
            } else paymentMethod.metaData
        return paymentInfo?.copy(
            title = applyVariables(variables, paymentInfo.title),
            ctaTitle = applyVariables(variables, paymentInfo.ctaTitle),
            displayName = applyVariables(variables, paymentInfo.displayName),
            description = applyVariables(variables,
                paymentMethod.description?.let {
                    it.ifEmpty { paymentInfo.description }
                } ?: paymentInfo.description
            ),
            instructions = applyVariables(variables, paymentMethod.instruction.ifEmpty { paymentInfo.instructions }),
            metaData = metaData,
        )
    }

    private fun applyVariables(variables: Map<String, String>, data: String): String {
        return variables.foldLeft(data) { message, mapKey ->
            message.replace("{${mapKey.key}}", mapKey.value)
        }
    }
}

private data class PaymentMethodInfo(
    val title: String,
    val ctaTitle: String,
    val displayName: String,
    val description: String,
    val instructions: String,
    val metaData: Map<String, Any>,
)
