package com.udaan.cart.core.metrics

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.udaan.cart.models.BaseRequestDto
import com.udaan.cart.models.common.CartSelection
import com.udaan.common.utils.kotlin.logger
import kotlin.time.Duration
import kotlin.time.DurationUnit

data class TrackerContext(
    val platformId: String,
    val request: BaseRequestDto,
    val timeTaken: Duration? = null,
)

interface CartEventTracker {
    suspend fun trackCreate(
        context: TrackerContext,
    )

    suspend fun trackEdit(
        context: TrackerContext,
    )

    suspend fun trackDelete(
        context: TrackerContext,
    )

    suspend fun trackFetchById(
        context: TrackerContext,
    )

    suspend fun trackFetchMultiple(
        context: TrackerContext,
    )

    suspend fun trackDeliverySlots(
        context: TrackerContext,
    )

    suspend fun trackPaymentMethods(
        context: TrackerContext,
    )

    suspend fun trackCheckout(
        context: TrackerContext,
    )

    suspend fun trackCreditLineUpdate(
        context: TrackerContext,
    )

    suspend fun trackConfirm(
        context: TrackerContext
    )

    suspend fun trackRewardsMismatch(
        cartId: String,
        cartAmountInPaise: Long,
        rewardsAmountInPaise: Long,
    )

    suspend fun trackCartItemWithInvalidPrice(
        cartId: String,
        cartItemId: String,
        listingId: String,
        salesUnitId: String,
        perUnitAmountInPaise: Long
    )

    suspend fun trackBuyerCodOrderAboveLimit(
        buyerId: String,
        cartId: String,
        cartValue: Long,
        totalCodValue: Long,
        existingOrders: List<String>,
    )

    suspend fun trackEmptyItemDeliverySlot(
        buyerId: String,
        listingId: String,
        salesUnitId: String,
        source: String
    )

    suspend fun trackMov(
        buyerId: String,
        cartId: String,
        cartValue: Long,
        movValue: Long,
        existingOrdersExists: Boolean,
    )

    suspend fun trackTimeout(
        buyerId: String,
        cartId: String,
        itemCount: Int,
        categoryGroupId: String,
        keySelection: CartSelection? = null
    )
}

class CartEventTrackerImpl @Inject constructor(
    private val objectMapper: ObjectMapper,
    private val eventTracker: EventTracker
) : CartEventTracker {
    companion object {
        private val logger by logger()

        private const val CART_ID = "CART_ID"
        private const val CART_ITEM_ID = "CART_ITEM_ID"
        private const val CART_TOTAL = "CART_TOTAL"
        private const val REWARDS_TOTAL = "REWARDS_TOTAL"
        private const val CART_CREATE = "CART_CREATE"
        private const val CART_DELETE = "CART_DELETE"
        private const val CART_EDIT = "CART_EDIT"
        private const val CART_FETCH_SINGLE = "CART_FETCH_SINGLE"
        private const val CART_FETCH_MULTIPLE = "CART_FETCH_MULTIPLE"
        private const val CART_PAYMENT_METHODS_CONFIRM = "CART_PAYMENT_METHODS_CONFIRM"
        private const val CART_CONFIRM = "CART_CONFIRM"
        private const val CART_DELIVERY_SLOTS = "DELIVERY_SLOTS"
        private const val CART_CHECKOUT = "CART_CHECKOUT"
        private const val CREDIT_LINE_UPDATE = "CREDIT_LINE_UPDATE"
        private const val CATEGORY_GROUP_ID = "CATEGORY_GROUP_ID"
        private const val SELECTION_STRATEGY = "SELECTION_STRATEGY"
        private const val PLATFORM_ID = "PLATFORM_ID"
        private const val TIME_TAKEN = "TIME_TAKEN"
        private const val REQUEST = "REQUEST"
        private const val REWARDS_MISMATCH = "REWARDS_MISMATCH"
        private const val CART_ITEM_INVALID_PRICE = "CART_ITEM_INVALID_PRICE"
        private const val BUYER_COD_ABOVE_LIMIT = "BUYER_COD_ABOVE_LIMIT"
        private const val LISTING_ID = "LISTING_ID"
        private const val SALES_UNIT_ID = "SALES_UNIT_ID"
        private const val PER_UNIT_AMOUNT_IN_PAISE = "PER_UNIT_AMOUNT_IN_PAISE"
        private const val BUYER_ORG_ID = "BUYER_ORG_ID"
        private const val TOTAL_COD_VALUE = "TOTAL_COD_VALUE"
        private const val EXISTING_COD_ORDERS = "EXISTING_COD_ORDERS"
        private const val TIMEOUT_EVENT = "TIMEOUT_EVENT"
        private const val CART_ITEM_COUNT = "CART_ITEM_COUNT"
        private const val SOURCE = "SOURCE"
        private const val EMPTY_ITEM_DELIVERY_SLOT_EVENT = "EMPTY_ITEM_DELIVERY_SLOT_EVENT"
        private const val MOV_IN_CHECKOUT_EVENT = "MOV_IN_CHECKOUT_EVENT"
        private const val MOV_VALUE = "MOV_VALUE"
        private const val EXISTING_DROPSLOT_ORDERS = "EXISTING_DROPSLOT_ORDERS"

    }

    override suspend fun trackCreate(
        context: TrackerContext,
    ) {
        track(
            CART_CREATE,
            mapOf(
                SELECTION_STRATEGY to context.request.getReqCartSelection().toString(),
                PLATFORM_ID to context.platformId,
                REQUEST to objectMapper.writeJsonValue(context.request)
            ),
            context.timeTaken
        )
    }

    override suspend fun trackEdit(
        context: TrackerContext,
    ) {
        track(
            CART_EDIT,
            mapOf(
                SELECTION_STRATEGY to context.request.getReqCartSelection().toString(),
                PLATFORM_ID to context.platformId,
                REQUEST to objectMapper.writeJsonValue(context.request)
            ),
            context.timeTaken
        )
    }

    override suspend fun trackDelete(
        context: TrackerContext,
    ) {
        track(
            CART_DELETE,
            mapOf(
                SELECTION_STRATEGY to context.request.getReqCartSelection().toString(),
                PLATFORM_ID to context.platformId,
                REQUEST to objectMapper.writeJsonValue(context.request)
            ),
            context.timeTaken
        )
    }

    override suspend fun trackFetchById(
        context: TrackerContext,
    ) {
        track(
            CART_FETCH_SINGLE,
            mapOf(
                SELECTION_STRATEGY to context.request.getReqCartSelection().toString(),
                PLATFORM_ID to context.platformId,
                REQUEST to objectMapper.writeJsonValue(context.request)
            ),
            context.timeTaken
        )
    }

    override suspend fun trackFetchMultiple(
        context: TrackerContext,
    ) {
        track(
            CART_FETCH_MULTIPLE,
            mapOf(
                SELECTION_STRATEGY to context.request.getReqCartSelection().toString(),
                PLATFORM_ID to context.platformId,
                REQUEST to objectMapper.writeJsonValue(context.request)
            ),
            context.timeTaken
        )
    }

    override suspend fun trackDeliverySlots(
        context: TrackerContext,
    ) {
        track(
            CART_DELIVERY_SLOTS,
            mapOf(
                SELECTION_STRATEGY to context.request.getReqCartSelection().toString(),
                PLATFORM_ID to context.platformId,
                REQUEST to objectMapper.writeJsonValue(context.request)
            ),
            context.timeTaken
        )
    }

    override suspend fun trackPaymentMethods(
        context: TrackerContext,
    ) {
        track(
            CART_PAYMENT_METHODS_CONFIRM,
            mapOf(
                SELECTION_STRATEGY to context.request.getReqCartSelection().toString(),
                PLATFORM_ID to context.platformId,
                REQUEST to objectMapper.writeJsonValue(context.request)
            ),
            context.timeTaken
        )
    }

    override suspend fun trackCheckout(
        context: TrackerContext,
    ) {
        track(
            CART_CHECKOUT,
            mapOf(
                SELECTION_STRATEGY to context.request.getReqCartSelection().toString(),
                PLATFORM_ID to context.platformId,
                REQUEST to objectMapper.writeJsonValue(context.request)
            ),
            context.timeTaken
        )
    }

    override suspend fun trackConfirm(
        context: TrackerContext
    ) {
        track(
            CART_CONFIRM,
            mapOf(
                SELECTION_STRATEGY to context.request.getReqCartSelection().toString(),
                PLATFORM_ID to context.platformId,
                REQUEST to objectMapper.writeJsonValue(context.request)
            ),
            context.timeTaken
        )
    }

    override suspend fun trackCreditLineUpdate(
        context: TrackerContext
    ) {
        track(
            CREDIT_LINE_UPDATE,
            mapOf(
                BUYER_ORG_ID to context.request.getReqBuyerId(),
                PLATFORM_ID to context.platformId,
                REQUEST to objectMapper.writeJsonValue(context.request)
            ),
            context.timeTaken
        )
    }

    override suspend fun trackRewardsMismatch(
        cartId: String,
        cartAmountInPaise: Long,
        rewardsAmountInPaise: Long,
    ) {
        track(
            REWARDS_MISMATCH,
            mapOf(
                CART_ID to cartId,
                CART_TOTAL to cartAmountInPaise.toString(),
                REWARDS_TOTAL to rewardsAmountInPaise.toString(),
            ),
            null,
        )
    }

    override suspend fun trackCartItemWithInvalidPrice(
        cartId: String,
        cartItemId: String,
        listingId: String,
        salesUnitId: String,
        perUnitAmountInPaise: Long
    ) {
        track(
            CART_ITEM_INVALID_PRICE,
            mapOf(
                CART_ID to cartId,
                CART_ITEM_ID to cartItemId,
                LISTING_ID to listingId,
                SALES_UNIT_ID to salesUnitId,
                PER_UNIT_AMOUNT_IN_PAISE to perUnitAmountInPaise.toString()
            ),
            null
        )
    }

    override suspend fun trackBuyerCodOrderAboveLimit(
        buyerId: String,
        cartId: String,
        cartTotal: Long,
        totalCodValue: Long,
        existingOrders: List<String>
    ) {
        track(
            BUYER_COD_ABOVE_LIMIT,
            mapOf(
                BUYER_ORG_ID to buyerId,
                CART_ID to cartId,
                CART_TOTAL to cartTotal.toString(),
                TOTAL_COD_VALUE to totalCodValue.toString(),
                EXISTING_COD_ORDERS to existingOrders.toString()
            ),
            null
        )
    }

    override suspend fun trackEmptyItemDeliverySlot(
        buyerId: String,
        listingId: String,
        salesUnitId: String,
        source: String
    ) {
        track(
            EMPTY_ITEM_DELIVERY_SLOT_EVENT,
            mapOf(
                BUYER_ORG_ID to buyerId,
                LISTING_ID to listingId,
                SALES_UNIT_ID to salesUnitId,
                SOURCE to source
            ),
            null
        )
    }

    override suspend fun trackMov(
        buyerId: String,
        cartId: String,
        cartValue: Long,
        movValue: Long,
        existingOrdersExists: Boolean,
    ) {
        track(
            MOV_IN_CHECKOUT_EVENT,
            mapOf(
                BUYER_ORG_ID to buyerId,
                CART_ID to cartId,
                CART_TOTAL to cartValue.toString(),
                MOV_VALUE to movValue.toString(),
                EXISTING_DROPSLOT_ORDERS to existingOrdersExists.toString()
            ),
            null
        )
    }

    override suspend fun trackTimeout(
        buyerId: String,
        cartId: String,
        itemCount: Int,
        categoryGroupId: String,
        keySelection: CartSelection?
    ) {
        track(
            TIMEOUT_EVENT,
            mapOf(
                CART_ID to cartId,
                BUYER_ORG_ID to buyerId,
                CART_ITEM_COUNT to itemCount.toString(),
                CATEGORY_GROUP_ID to categoryGroupId,
                SELECTION_STRATEGY to keySelection.toString()
            ),
            null
        )
    }

    private suspend fun track(
        name: String,
        properties: Map<String, String>,
        timeTaken: Duration?
    ) {
        try {
            eventTracker.trackEvent(
                name,
                properties,
                timeTaken?.let {
                    mapOf(
                        TIME_TAKEN to it.toDouble(DurationUnit.MILLISECONDS)
                    )
                } ?: emptyMap()
            )
            eventTracker.incrementCounter(
                name,
                properties
            )
        } catch (e: Exception) {
            logger.error("Failed to track events: ${e.message}", e)
        }
    }
}

// This is to prevent "Inappropriate blocking method call" warning
inline fun <reified T> ObjectMapper.writeJsonValue(value: T): String = writeValueAsString(value)
