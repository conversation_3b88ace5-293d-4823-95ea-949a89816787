package com.udaan.cart.core.operations.v1.lifecycles.product.impl

import arrow.core.Either
import com.google.inject.Inject
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.operations.v1.contexts.EditCartContext
import com.udaan.cart.core.operations.v1.hooks.EditProductHookCollection
import com.udaan.cart.core.operations.v1.lifecycles.product.AbstractEditProductLifecycle
import com.udaan.cart.models.default.EditCartReqDto

class DefaultEditComboLifecycleImpl @Inject constructor() : AbstractEditProductLifecycle<EditCartReqDto>() {
    override suspend fun setupHooks(
        hooks: EditProductHookCollection<OpError, EditCartContext<EditCartReqDto>>,
        context: EditCartContext<EditCartReqDto>
    ): EditProductHookCollection<OpError, EditCartContext<EditCartReqDto>> {
        TODO("Not yet implemented")
    }

    override suspend fun doLoadProducts(
        context: EditCartContext<EditCartReqDto>
    ): Either<OpError,  EditCartContext<EditCartReqDto>> {
        TODO("Not yet implemented")
    }

    override suspend fun doLoadPrices(
        context: EditCartContext<EditCartReqDto>
    ): Either<OpError, EditCartContext<EditCartReqDto>> {
        TODO("Not yet implemented")
    }

    override suspend fun doEditCart(
        context: EditCartContext<EditCartReqDto>
    ): Either<OpError, EditCartContext<EditCartReqDto>> {
        TODO("Not yet implemented")
    }
}
