package com.udaan.cart.core.common.providers

import com.udaan.cart.core.domain.models.CartItem
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.cart.core.domain.models.ProductItem
import com.udaan.cart.core.domain.models.ProductType
import com.udaan.common.utils.kotlin.logger
import com.udaan.orderform.common.providers.contexts.PriceContext
import com.udaan.pricing.PriceInPaisa
import com.udaan.pricing.QtyBasedPrice
import com.udaan.pricing.RiderCode

data class PackagingUnit(
    val assortment: Int,
    val multiplier: Int,
)

data class PricedCartItem(
    val item: CartItem,
    val packagingUnit: PackagingUnit?,
    val pricingAuditId: String? = null
)

interface PriceContextInterpreter {
    suspend fun applyPrice(
        context: PriceContext,
        item: CartItem
    ): PricedCartItem

    suspend fun hasValidPrice(
        context: PriceContext,
        item: CartItem
    ): Boolean

    suspend fun getRiderPromos(
        context: PriceContext,
        item: CartItem
    ): Map<String, Int>
}

class PriceContextInterpreterImpl : PriceContextInterpreter {
    companion object {
        private val logger by logger()
    }

    // Not using pricing options based price
    override suspend fun applyPrice(context: PriceContext, item: CartItem): PricedCartItem {
        return extractListingPrice(context, item)?.let { priceDetails ->
            val unitPrice = priceDetails.priceInPaisa.defaultPrice
            val productItem = item.product as ListingProductItem
            val pricingAuditId = context.getListingPrice(productItem.listingId)?.find {
                it.saleUnitId == productItem.salesUnitId
            }?.pricingAuditId
            PricedCartItem(
                item.copy(
                    perUnitAmountInPaise = unitPrice,
                    priceDetails = item.priceDetails.copy(unitPrice = unitPrice)
                ), priceDetails.packagingUnit?.let {
                    PackagingUnit(
                        assortment = it.assortment,
                        multiplier = it.multiplier,
                    )
                },
                pricingAuditId = pricingAuditId
            )
        } ?: run {
            val cartProduct = item.product as ListingProductItem
            logger.info(
                "No price details found: " +
                        "${context.getAllPrices().filter { it.listingId == cartProduct.listingId }}"
            )
            val pricingAuditId =
                context.getListingPrice(cartProduct.listingId)
                    ?.find { it.saleUnitId == cartProduct.salesUnitId }?.pricingAuditId
            PricedCartItem(item, null, pricingAuditId = pricingAuditId)
        }
    }

    override suspend fun hasValidPrice(
        context: PriceContext,
        item: CartItem
    ): Boolean {
        return extractListingPrice(context, item) != null
    }

    override suspend fun getRiderPromos(context: PriceContext, item: CartItem): Map<String, Int> {
        return extractListingPrice(context, item)
            ?.let { priceDetails ->
                priceDetails.priceInPaisa.priceRiders?.filter { rider ->
                    rider?.riderCode == RiderCode.PROMOTION.name
                }?.filter { it?.riderDetails != null }?.mapNotNull { priceRiderForListing ->
                    logger.info("priceRiderForListing: ${priceRiderForListing?.riderDetails}")
                    priceRiderForListing?.riderDetails?.let { riderDetails ->
                        val riderMap = (riderDetails as Map<*, *>)
                        riderMap["promotionId"]?.let { promotionId ->
                            riderMap["bps"]?.let { bps ->
                                promotionId as String to bps as Int
                            }
                        }
                    }
                }?.toMap()
            } ?: emptyMap()
    }

    private suspend fun extractListingPrice(context: PriceContext, item: CartItem): QtyBasedPrice? {
        item.product as ListingProductItem
        // setting freebie price to 0
        if (item.isFreebie()) return QtyBasedPrice(
            minQty = 1, maxQty = Int.MAX_VALUE, pricePerKgInPaisa = null, taxableAmountPaise = null,
            priceInPaisa = PriceInPaisa(
                onCredit = 0, onCOD = 0, onPrepayment = 0, basicPrice = null, priceRiders = emptyList()
            )
        )
        return context.getAllPrices()
            .find { it.listingId == item.product.listingId && it.saleUnitId == item.product.salesUnitId }
            ?.let { priceForListing ->
                val priceDetails = priceForListing.prices.firstOrNull {
                    item.quantity >= it.minQty && item.quantity <= it.maxQty
                }
                priceDetails
            }
    }
}
