package com.udaan.cart.core.db.dao

import com.udaan.cart.core.domain.models.*
import com.udaan.cart.models.common.CartSelection
import com.udaan.proto.models.ModelV1
import org.jdbi.v3.json.Json
import org.jdbi.v3.sqlobject.customizer.Bind
import org.jdbi.v3.sqlobject.statement.SqlUpdate
import java.util.*

class CartDao {
    interface Update {
        @SqlUpdate(
            """
           insert into cart_items (
            id, 
            cart_id, 
            quantity, 
            product, 
            per_unit_amount_in_paisa, 
            current_active, 
            price_details,
            properties,
            order_info, 
            created_at, 
            updated_at
           ) VALUES (
            :id, 
            :cart_id, 
            :quantity, 
            :product, 
            :per_unit_amount_in_paisa, 
            :current_active, 
            :price_details,
            :properties,
            :order_info, 
            :created_at, 
            :updated_at 
           )
        """
        )
        fun createCartItem(
            @Bind("id") id: String,
            @Bind("cart_id") cartId: String,
            @Bind("quantity") quantity: Int,
            @Json @Bind("product") product: ProductItem,
            @Bind("per_unit_amount_in_paisa") perUnitAmountInPaisa: Long,
            @Json @Bind("price_details") priceDetails: ItemPriceDetails,
            @Json @Bind("properties") properties: ItemProperties,
            @Bind("current_active") currentActive: Boolean,
            @Json @Bind("order_info") orderInfo: CartItemOrderInfo?,
            @Bind("created_at") createdAt: Date,
            @Bind("updated_at") updatedAt: Date,
        ): Int

        @SqlUpdate(
            """
           insert into carts (
            id, 
            type, 
            buyer_id, 
            platform_id,
            selection, 
            state, 
            current_active, 
            order_info, 
            created_at, 
            updated_at
           ) VALUES (
            :id, 
            :type, 
            :buyer_id, 
            :platform_id,
            :selection,
            :state,
            :current_active,
            :order_info, 
            :created_at, 
            :updated_at 
           )
        """
        )
        fun createCart(
            @Bind("id") id: String,
            @Bind("type") type: String,
            @Bind("buyer_id") buyerId: String,
            @Bind("platform_id") platformId: ModelV1.SellingPlatform,
            @Json @Bind("selection") selection: CartSelection,
            @Bind("state") state: String,
            @Bind("current_active") currentActive: Boolean,
            @Json @Bind("order_info") orderInfo: CartOrderInfo?,
            @Bind("created_at") createdAt: Date,
            @Bind("updated_at") updatedAt: Date,
        ): Int

        @SqlUpdate(
            """
           update carts set
            type = :type, 
            state = :state, 
            current_active = :current_active,  
            order_info = :order_info, 
            updated_at = :updated_at
           where
            id = :id
        """
        )
        fun updateCart(
            @Bind("id") id: String,
            @Bind("type") type: CartType,
            @Bind("state") state: CartState,
            @Bind("current_active") currentActive: Boolean,
            @Json @Bind("order_info") orderInfo: CartOrderInfo?,
            @Bind("updated_at") updatedAt: Date,
        ): Int

        @SqlUpdate(
            """
           update cart_items set
            quantity = :quantity, 
            product = :product, 
            per_unit_amount_in_paisa = :per_unit_amount_in_paisa, 
            price_details = :price_details,
            properties = :properties,
            current_active = :current_active, 
            order_info = :order_info, 
            updated_at = :updated_at
           where
            id = :item_id
        """
        )
        fun updateCartItem(
            @Bind("item_id") itemId: String,
            @Bind("quantity") quantity: Int,
            @Json @Bind("product") product: ProductItem,
            @Bind("per_unit_amount_in_paisa") perUnitAmountInPaisa: Long,
            @Json @Bind("price_details") priceDetails: ItemPriceDetails,
            @Json @Bind("properties") properties: ItemProperties,
            @Bind("current_active") currentActive: Boolean,
            @Json @Bind("order_info") orderInfo: CartItemOrderInfo?,
            @Bind("updated_at") updatedAt: Date,
        ): Int

        @SqlUpdate(
            """
           update carts set 
            current_active = false,  
            updated_at = :updated_at
           where
            id = :id
        """
        )
        fun deleteCart(
            @Bind("id") id: String,
            @Bind("updated_at") updatedAt: Date,
        ): Int

        @SqlUpdate(
            """
           update cart_items set 
            current_active = false,  
            updated_at = :updated_at
           where
            cart_id = :cart_id
        """
        )
        fun deleteCartItemByCartId(
            @Bind("cart_id") cartId: String,
            @Bind("updated_at") updatedAt: Date,
        ): Int

        @SqlUpdate(
            """
           update cart_items set 
            current_active = false,  
            updated_at = :updated_at
           where
            id = :item_id
        """
        )
        fun deleteCartItem(
            @Bind("item_id") itemId: String,
            @Bind("updated_at") updatedAt: Date,
        ): Int
    }
}
