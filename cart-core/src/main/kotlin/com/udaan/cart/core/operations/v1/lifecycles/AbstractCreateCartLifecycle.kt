package com.udaan.cart.core.operations.v1.lifecycles

import arrow.core.Either
import arrow.core.continuations.either
import com.udaan.cart.core.common.helpers.Lifecycle
import com.udaan.cart.core.common.helpers.LifecycleStage
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.BaseContext
import com.udaan.cart.core.operations.v1.contexts.CreateCartContext
import com.udaan.cart.core.operations.v1.hooks.CreateCartHookCollection

abstract class AbstractCreateCartLifecycle<R> {
    protected abstract suspend fun setupHooks(
        hooks: CreateCartHookCollection<OpError, CreateCartContext<R>>,
        context: CreateCartContext<R>
    ): Create<PERSON>artHookCollection<OpError, CreateCartContext<R>>

    protected abstract suspend fun doValidations(
        context: CreateCartContext<R>
    ): Either<OpError, CreateCartContext<R>>

    protected abstract suspend fun doCreate(
        context: CreateCartContext<R>
    ): Either<OpError, CreateCartContext<R>>

    protected abstract suspend fun doStore(
        context: CreateCartContext<R>
    ): Either<OpError, CreateCartContext<R>>

    @Suppress("UNCHECKED_CAST")
    suspend fun <T : BaseContext> execute(
        context: CreateCartContext<R>,
        hooks: CreateCartHookCollection<OpError, T>
    ): Either<OpError, CreateCartContext<R>> {
        val hook = setupHooks(
            hooks as CreateCartHookCollection<OpError, CreateCartContext<R>>,
            context
        )
        val stages = listOf(
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doValidations(currentContext)
                },
                post = emptyList()
            ),
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doCreate(currentContext)
                },
                post = hook.doPostOrderCreate
            ),
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doStore(currentContext)
                },
                post = emptyList(),
            ),
        )
        return either {
            Lifecycle(stages = stages)
                .execute(context)
                .bind()
        }
    }
}
