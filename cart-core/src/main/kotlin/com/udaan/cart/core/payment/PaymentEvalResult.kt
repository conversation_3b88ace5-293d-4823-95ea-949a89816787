package com.udaan.cart.core.payment

import com.udaan.cart.core.domain.models.PaymentCollectionType

data class UserPaymentDetails(
    val description: String?,
    val instruction: String,
)

data class RewardBasedPayment(
    val coins: Int,
    val rewardAmountInPaise: Long,
    val payableAmountInPaise: Long
)

data class PaymentEvalResult(
    val collectionType: PaymentCollectionType,
    val allowOtherMethods: Boolean,
    val isValidMethod: Boolean,
    val metaData: Map<String, Any>,
    val paymentBps: Long = 0L,
    val payableAmountInPaise: Long,
    val remainingAmountInPaise: Long,
    val userPaymentDetails: UserPaymentDetails? = null,
    val rewardBasedPayment: RewardBasedPayment? = null,
)
