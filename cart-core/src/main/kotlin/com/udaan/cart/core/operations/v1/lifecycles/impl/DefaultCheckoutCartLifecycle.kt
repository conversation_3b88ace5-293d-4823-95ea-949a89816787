package com.udaan.cart.core.operations.v1.lifecycles.impl

import arrow.core.*
import com.google.inject.Inject
import com.google.inject.Injector
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.common.providers.CheckoutDetails
import com.udaan.cart.core.common.providers.OrderCheckoutProvider
import com.udaan.cart.core.common.providers.promotions.PromoContext
import com.udaan.cart.core.context.CartContextFactoryImpl
import com.udaan.cart.core.context.CartContextKey
import com.udaan.cart.core.db.repo.CartWriteRepo
import com.udaan.cart.core.domain.models.CartOrderInfo
import com.udaan.cart.core.domain.models.CartState
import com.udaan.cart.core.exceptions.EmptyCartException
import com.udaan.cart.core.hooks.Hook
import com.udaan.cart.core.operations.v1.contexts.CheckoutCartContext
import com.udaan.cart.core.operations.v1.hooks.CheckoutCartHookCollection
import com.udaan.cart.core.operations.v1.hooks.impl.CartStateCheckHook
import com.udaan.cart.core.operations.v1.hooks.impl.ViolationCheckHook
import com.udaan.cart.core.operations.v1.lifecycles.AbstractCheckoutCartLifecycle
import com.udaan.cart.core.utils.CartContextUtils
import com.udaan.cart.models.default.CheckoutRequestDto
import com.udaan.cart.models.default.RequestExtraDataKeys
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.TelemetryScope
import org.joda.time.DateTime

class DefaultCheckoutCartLifecycle @Inject constructor(
    injector: Injector,
    private val cartContextFactory: CartContextFactoryImpl,
    private val orderCheckoutProvider: OrderCheckoutProvider,
    private val cartWriteRepo: CartWriteRepo,
    private val cartContextUtils: CartContextUtils
) : AbstractCheckoutCartLifecycle<CheckoutRequestDto>() {

    companion object {
        private val logger by logger()
    }

    @Suppress("UNCHECKED_CAST")
    private val postCartFetchHooks = listOf(
        injector.getInstance(CartStateCheckHook::class.java) as Hook<OpError, CheckoutCartContext<CheckoutRequestDto>>,
        injector.getInstance(ViolationCheckHook::class.java) as Hook<OpError, CheckoutCartContext<CheckoutRequestDto>>,
    )

    override suspend fun setupHooks(
        hooks: CheckoutCartHookCollection<OpError, CheckoutCartContext<CheckoutRequestDto>>,
        context: CheckoutCartContext<CheckoutRequestDto>
    ): CheckoutCartHookCollection<OpError, CheckoutCartContext<CheckoutRequestDto>> {
        return hooks.copy(
            doPostCartFetch = postCartFetchHooks
        )
    }

    override suspend fun doFetchCart(
        context: CheckoutCartContext<CheckoutRequestDto>
    ): Either<OpError, CheckoutCartContext<CheckoutRequestDto>> {
        val cart = context.cartSelector.find(context.selectorData)
        if (cart == null || (cart.items.isEmpty())) {
            throw EmptyCartException("Cart is empty, kindly add items to cart")
        }
        val cartContext = cartContextFactory.createCartContext(cart = cart, cartId = cart.id)
        context.setCartContext(cartContext)
        return context.right()
    }

    override suspend fun doCheckoutCart(
        context: CheckoutCartContext<CheckoutRequestDto>
    ): Either<OpError, CheckoutCartContext<CheckoutRequestDto>> {
        return kotlin.runCatching {
            val cpodExpEnabledDef = TelemetryScope.async {
                context.getCartContext()?.let {cartContextUtils.isCPODEnabled(it)} ?: false
            }
            context.getCartContext()?.let { cartContext ->
                val cartWithPromos = cartContext.getPreloadedCartWithPromos() ?: cartContext.getCartWithPromos(
                        context.request.buyerOrgUnitId,
                        skipOOSItems = true,
                        fetchRiderDetails = true,
                        orderReadyForCheckout = true
                    )
                cartWithPromos?.let { detailedCart ->
                    val promoContext = cartContext.getValue<PromoContext>(CartContextKey.PROMOTION_CONTEXT)
                    val deliveryChargeIdentifier = cartContext.fetchDeliveryChargeIdentifier()                    
                    val checkoutDetails = orderCheckoutProvider.checkout(
                        detailedCart = detailedCart,
                        promoContext = promoContext,
                        riderPromos = cartContext.getRiderPromos(),
                        request = context.request.copy(
                            extraData = context.request.extraData + mapOf(
                                RequestExtraDataKeys.CPOD_EXP_ENABLED to cpodExpEnabledDef.await()
                            )
                        ),
                        deliveryChargeCartIdentifier = deliveryChargeIdentifier
                    )
                    context.setValue(CheckoutCartContext.CHECKOUT_DETAILS, checkoutDetails)
                    context.right()
                }
            } ?: OpError(message = "Cart is empty, kindly add items to cart").left()
        }.getOrElse { e ->
            logger.error("Failed to checkout cart: ${e.message}", e)
            OpError(message = e.message ?: "Failed to checkout cart", exception = e).left()
        }
    }

    override suspend fun doClearCart(
        context: CheckoutCartContext<CheckoutRequestDto>
    ): Either<OpError, CheckoutCartContext<CheckoutRequestDto>> {
        return kotlin.runCatching {
            context.getCartContext()?.getCart()?.let { cart ->
                context.getValue<CheckoutDetails>(CheckoutCartContext.CHECKOUT_DETAILS)?.let { checkoutDetails ->
                    val placedCart = cart.copy(
                        state = CartState.ORDER_PLACED, orderInfo = CartOrderInfo(
                            orderIds = checkoutDetails.placedOrderIds
                        ), updatedAt = DateTime.now().toDate()
                    )
                    cartWriteRepo.update(placedCart, emptyList(), emptyList(), emptyList())
                }
            }
            context.right()
        }.getOrElse { e ->
            logger.error("Failed to clear cart: ${e.message}", e)
            context.right()
        }
    }
}
