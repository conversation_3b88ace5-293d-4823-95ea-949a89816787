package com.udaan.cart.core.db.utils

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import org.jdbi.v3.core.argument.AbstractArgumentFactory
import org.jdbi.v3.core.argument.Argument
import org.jdbi.v3.core.config.ConfigRegistry
import org.postgresql.util.PGobject
import org.skife.jdbi.v2.sqlobject.Binder
import org.skife.jdbi.v2.sqlobject.BinderFactory
import org.skife.jdbi.v2.sqlobject.BindingAnnotation
import java.sql.Types
import java.util.*

@BindingAnnotation(BindJson.JsonBinderFactory::class)
@kotlin.annotation.Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.VALUE_PARAMETER)
annotation class BindJson(val value: String) {
    class JsonBinderFactory<A> : BinderFactory<Annotation> {
        override fun build(annotation: Annotation?): Binder<*, *> {
            return Binder<BindJson, A> { q, bind, json ->
                try {
                    val objectMapper = ObjectMapper()
                    val jsonString = objectMapper.writeValueAsString(json)
                    q.bind(bind.value, jsonString)
                } catch (ex: JsonProcessingException) {
                    throw IllegalStateException("Error Binding JSON", ex)
                }
            }
        }
    }
}

@BindingAnnotation(BindJsonb.JsonBinderFactory::class)
@kotlin.annotation.Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.VALUE_PARAMETER)
annotation class BindJsonb(val value: String) {
    class JsonBinderFactory<A> : BinderFactory<Annotation> {
        override fun build(annotation: Annotation?): Binder<*, *> {
            return Binder<BindJsonb, A> { q, bind, json ->
                try {
                    val objectMapper = ObjectMapper()
                    val data = PGobject()
                    data.type = "jsonb"
                    val jsonString = objectMapper.writeValueAsString(json)
                    data.value = jsonString
                    q.bind(bind.value, data)
                } catch (ex: JsonProcessingException) {
                    throw IllegalStateException("Error Binding JSONB", ex)
                }
            }
        }
    }
}

//class JsonbArgumentFactory<A>: AbstractArgumentFactory<A>(Types.OTHER) {
//    override fun build(value: A?, config: ConfigRegistry?): Argument {
//        return Unit { position, statement, ctx ->
//            val objectMapper = ObjectMapper()
//            val data = PGobject()
//            data.type = "jsonb"
//            val jsonString = objectMapper.writeValueAsString(value)
//            data.value = jsonString
//            statement.setString(position, value.toString())
//        }
//    }
//}
