package com.udaan.cart.core.operations.v1.lifecycles.product.impl

import arrow.core.Either
import arrow.core.continuations.either
import com.google.inject.Inject
import com.google.inject.Injector
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.BaseContext
import com.udaan.cart.core.operations.v1.contexts.EditCartContext
import com.udaan.cart.core.operations.v1.hooks.EditProductHookCollection
import com.udaan.cart.core.operations.v1.lifecycles.product.AbstractEditProductLifecycle
import com.udaan.cart.core.operations.v1.lifecycles.product.ProductHandler
import com.udaan.cart.core.operations.v1.lifecycles.product.filters.ComboProductFilter
import com.udaan.cart.core.operations.v1.lifecycles.product.filters.ListingProductFilter
import com.udaan.cart.core.operations.v1.lifecycles.product.filters.ProductFilter
import com.udaan.cart.models.default.EditCartReqDto
import com.udaan.common.utils.kotlin.logger

class ProductHandlerImpl @Inject constructor(
    injector: Injector
) : ProductHandler {
    companion object {
        private val logger by logger()
    }

    private val lifecycleImplementations = listOf(
        ProductLifecycle(
            filter = injector.getInstance(ListingProductFilter::class.java),
            lifecycle = injector.getInstance(DefaultEditListingLifecycleImpl::class.java)
        ),
        ProductLifecycle(
            filter = injector.getInstance(ComboProductFilter::class.java),
            lifecycle = injector.getInstance(DefaultEditComboLifecycleImpl::class.java)
        ),
    )

    override suspend fun <T : BaseContext> edit(
        context: EditCartContext<EditCartReqDto>,
        hooks: EditProductHookCollection<OpError, T>
    ): Either<OpError, EditCartContext<EditCartReqDto>> {
        val initData = either<OpError, EditCartContext<EditCartReqDto>> {
            Either.Right(context).bind()
        }
        val originalRequest = context.request
        return lifecycleImplementations.fold(initData) { currentContext, productLifecycle ->
            when (currentContext) {
                is Either.Left -> currentContext
                is Either.Right -> {
                    val applicableProducts = productLifecycle.filter.filter(context.request.products)
                    if (applicableProducts.isNotEmpty()) {
                        val updatedRequest = currentContext.value.request.copy(products = applicableProducts)
                        currentContext.value.request = updatedRequest
                        val updatedContext = productLifecycle.lifecycle.execute(currentContext.value, hooks).map { responseContext ->
                            // replace request with original one before passing to other handlers
                            responseContext.request = originalRequest
                            responseContext
                        }
                        when(updatedContext) {
                            is Either.Left -> {
                                logger.error("Error: ${updatedContext.value}")
                            }
                            is Either.Right -> {
                                logger.info("Items: ${updatedContext.value.getEditedItems()}")
                            }
                        }
                        updatedContext
                    } else currentContext
                }
            }
        }
    }
}

private data class ProductLifecycle(
    val filter: ProductFilter,
    val lifecycle: AbstractEditProductLifecycle<EditCartReqDto>,
)
