package com.udaan.cart.core.operations.v1.lifecycles

import arrow.core.Either
import arrow.core.continuations.either
import com.udaan.cart.core.common.helpers.Lifecycle
import com.udaan.cart.core.common.helpers.LifecycleStage
import com.udaan.cart.core.context.BaseContext
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.operations.v1.contexts.EditCartContext
import com.udaan.cart.core.operations.v1.hooks.EditCartHookCollection

abstract class AbstractEditCartLifecycle<R> {
    protected abstract suspend fun setupHooks(
        hooks: EditCartHookCollection<OpError, EditCartContext<R>>,
        context: EditCartContext<R>
    ): EditCartHookCollection<OpError, EditCartContext<R>>

    protected abstract suspend fun doPreValidations(
        context: EditCartContext<R>
    ): Either<OpError, EditCartContext<R>>

    protected abstract suspend fun doFetchCart(
        context: EditCartContext<R>
    ): Either<OpError, EditCartContext<R>>

    protected abstract suspend fun doEditCart(
        context: EditCartContext<R>
    ): Either<OpError, EditCartContext<R>>

    protected abstract suspend fun doStoreCart(
        context: EditCartContext<R>
    ): Either<OpError, EditCartContext<R>>

    @Suppress("UNCHECKED_CAST")
    suspend fun <T : BaseContext> execute(
        context: EditCartContext<R>,
        hooks: EditCartHookCollection<OpError, T>
    ): Either<OpError, EditCartContext<R>> {
        val hook = setupHooks(
            hooks as EditCartHookCollection<OpError, EditCartContext<R>>,
            context
        )
        val stages = listOf(
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doPreValidations(currentContext)
                },
                post = hook.doValidationHooks,
            ),
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doFetchCart(currentContext)
                },
                post = hook.doPostCartFetch,
            ),
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doEditCart(currentContext)
                },
                post = hook.doPostEditHooks,
            ),
            LifecycleStage(
                pre = emptyList(),
                execute = { currentContext ->
                    doStoreCart(currentContext)
                },
                post = emptyList(),
            ),
        )
        return either {
            Lifecycle(stages = stages)
                .execute(context)
                .bind()
        }
    }
}
