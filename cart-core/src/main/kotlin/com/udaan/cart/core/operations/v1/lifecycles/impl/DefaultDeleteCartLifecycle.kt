package com.udaan.cart.core.operations.v1.lifecycles.impl

import arrow.core.*
import com.google.inject.Inject
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.CartContextFactory
import com.udaan.cart.core.db.repo.CartWriteRepo
import com.udaan.cart.core.operations.v1.contexts.DeleteCartContext
import com.udaan.cart.core.operations.v1.hooks.DeleteCartHookCollection
import com.udaan.cart.core.operations.v1.lifecycles.AbstractDeleteCartLifecycle
import com.udaan.cart.models.default.DeleteCartReqDto
import com.udaan.common.utils.kotlin.logger

class DefaultDeleteCartLifecycle @Inject constructor(
    private val cartContextFactory: CartContextFactory,
    private val cartWriteRepo: CartWriteRepo,
) : AbstractDeleteCartLifecycle<DeleteCartReqDto>() {

    companion object {
        private val logger by logger()
    }

    override suspend fun setupHooks(
        hooks: DeleteCartHookCollection<OpError, DeleteCartContext<DeleteCartReqDto>>,
        context: DeleteCartContext<DeleteCartReqDto>
    ): DeleteCartHookCollection<OpError, DeleteCartContext<DeleteCartReqDto>> {
        return hooks
    }

    override suspend fun doFetchCart(
        context: DeleteCartContext<DeleteCartReqDto>
    ): Either<OpError, DeleteCartContext<DeleteCartReqDto>> {
        val cart = context.cartSelector.find(context.selectorData)
        return cart?.let {
            val cartContext = cartContextFactory.createCartContext(cart.id, cart)
            context.setCartContext(cartContext)
            context.right()
        } ?: OpError(message = "No active cart found").left()
    }

    override suspend fun doCleanOrders(
        context: DeleteCartContext<DeleteCartReqDto>
    ): Either<OpError, DeleteCartContext<DeleteCartReqDto>> {
        // TODO: Check if any orders associated and send request to OF to delete them
        // If those orders are placed, report error
        return context.right()
    }

    override suspend fun doStoreOrder(
        context: DeleteCartContext<DeleteCartReqDto>
    ): Either<OpError, DeleteCartContext<DeleteCartReqDto>> {
        return context.getCartContext()?.let { cartContext ->
            cartContext.getCart()?.let { cart ->
                kotlin.runCatching {
                    cartWriteRepo.delete(cart.id)
                }.getOrElse { e ->
                    logger.error("Failed to delete cart: ${e.message}", e)
                    OpError(message = "Failed to delete cart").left()
                }
                context.right()
            } ?: OpError(message = "Requested cart not found").left()
        } ?: OpError(message = "Requested cart not found").left()
    }
}
