package com.udaan.cart.core.operations.v1.hooks.impl

import arrow.core.*
import arrow.core.left
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.common.models.InventoryListingDetails
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.cart.core.hooks.BaseOperationHook
import com.udaan.cart.core.operations.v1.contexts.EditCartContext
import com.udaan.cart.models.common.ListingProduct
import com.udaan.cart.models.default.EditCartReqDto
import com.udaan.common.utils.kotlin.logger
import com.udaan.orchestrator.models.ListingUnavailabilityReasonType
import com.udaan.orderform.common.models.ListingData
import com.udaan.orderform.common.providers.InventoryProvider
import kotlin.collections.flatMap

@Singleton
class EditInventoryCheckHook @Inject constructor(
    private val inventoryProvider: InventoryProvider,
) : BaseOperationHook<OpError, EditCartContext<EditCartReqDto>> {

    companion object {
        private val logger by logger()
    }

    override suspend fun execute(
        context: EditCartContext<EditCartReqDto>
    ): Either<OpError, EditCartContext<EditCartReqDto>> {
        return kotlin.runCatching {
            context.getCartContext()?.getCart()?.let { cart ->
                val productsForInventoryChecks = context.request.products.filter {
                    (it as ListingProduct).quantity > 0
                }.map { product ->
                    product as ListingProduct
                    cart.items.find { item ->
                        val productItem = item.product as ListingProductItem
                        product.listingId == productItem.listingId &&
                                product.salesUnitId == productItem.salesUnitId &&
                                product.quantity > item.quantity
                    }?.let {
                        it.product as ListingProductItem
                        InventoryListingDetails(
                            orgId = "",
                            listingId = it.product.listingId,
                            salesUnitId = it.product.salesUnitId,
                            quantity = product.quantity,
                        )
                    } ?: InventoryListingDetails(
                        orgId = "",
                        listingId = product.listingId,
                        salesUnitId = product.salesUnitId,
                        quantity = product.quantity,
                    )
                }
                val listingsMap = context.getListingsMap()
                val inventoryListingDetails = productsForInventoryChecks.mapNotNull { item ->
                    listingsMap[item.listingId]?.let { listing ->
                        item.copy(orgId = listing.orgId)
                    }
                }
                val unavailability =
                    context.getCartContext()?.getBuyerOrgUnit(context.request.buyerOrgUnitId)?.let { buyerOrgUnit ->
                        inventoryListingDetails.groupBy { it.orgId }
                            .flatMap { (sellerId, items) ->
                                inventoryProvider.checkAvailabilityAsync(
                                    sellerOrgId = sellerId,
                                    buyerOrgUnitId = buyerOrgUnit.orgUnitId,
                                    deliveryPincode = buyerOrgUnit.unitAddress.pincode,
                                    platform = cart.platformId,
                                    listingsData = items.map { item ->
                                        ListingData(
                                            item.listingId,
                                            item.salesUnitId,
                                            item.quantity
                                        )
                                    }
                                ).await().run {
                                    logger.info("listingUnavailability: $this")
                                    this.filter { (_, listingAvailability) ->
                                        listingAvailability.isAvailable.not() || (listingAvailability.isAvailable.not() &&
                                                listingAvailability.unavailableReasonType != ListingUnavailabilityReasonType.HUB_CAPACITY_BREACHED)
                                    }
                                }.values.toList()
                            }
                    }?.map { listingUnavailability ->
                        listingsMap[listingUnavailability.listingId]?.let {
                            it.generatedTitle.ifEmpty { it.title }
                        } ?: ""
                    }?.filter { it.isNotEmpty() }?.joinToString()?.let { listingTitles ->
                        if (listingTitles.isNotEmpty()) {
                            "Requested inventory is not available for $listingTitles"
                        } else ""
                    }
                if (!unavailability.isNullOrEmpty()) {
                    OpError(message = unavailability).left()
                } else context.right()
            } ?: context.right()
        }.getOrElse { e ->
            logger.error("Failed to check inventory while editing cart: ${e.message}", e)
            context.right()
        }
    }
}
