package com.udaan.cart.core.service.v1

import arrow.core.Either
import com.udaan.cart.models.BaseRequestDto
import com.udaan.cart.models.BaseResponseDto

interface CartService<T : BaseRequestDto, R : BaseResponseDto> {
    suspend fun edit(request: T): Either<String, R>

    suspend fun delete(request: T): Either<String, R>

    suspend fun find(request: T): Either<String, R>

    suspend fun findAll(request: T): Either<String, R>

    suspend fun fetchDeliverySlots(request: T): Either<String, R>

    suspend fun fetchPaymentMethods(request: T): Either<String, R>

    suspend fun checkout(request: T): Either<String, R>

    suspend fun updateCreditLine(request: T): Either<String, R>
}
