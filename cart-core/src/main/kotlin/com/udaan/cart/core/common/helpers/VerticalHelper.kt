package com.udaan.cart.core.common.helpers

import com.udaan.cart.core.common.helpers.BaseVerticals.Dairy
import com.udaan.cart.core.common.helpers.BaseVerticals.FMCG
import com.udaan.cart.core.common.helpers.BaseVerticals.Food
import com.udaan.cart.core.common.helpers.BaseVerticals.Meat
import com.udaan.cart.core.common.helpers.BaseVerticals.Medicine
import com.udaan.common.utils.kotlin.logger
import com.udaan.orchestrator.models.v2.SupplyChainCategory
import com.udaan.orchestrator.models.v2.SupplyChainCategory.*

object BaseVerticals {
    const val Dairy = "CN-Dairy"
    const val Eggs = "CN-Eggs"
    const val FMCG = "FMCG"
    const val Food = "Food"
    const val Meat = "CN-Meat"
    const val Medicine = "CN-Medicines"
}

fun com.udaan.catalog.models.ModelV1.Vertical.isFresh(): Boolean {
    return this.baseVerticalList
        .firstOrNull {
            it.baseVerticalName == "FreshAttribute"
                    || it.baseVerticalName == "FruitandVegetable"
        } != null
}

fun com.udaan.catalog.models.ModelV1.Vertical.isMeat(): Boolean {
    return this.baseVerticalList
        .firstOrNull {
            it.baseVerticalName == "Meat" || it.baseVerticalName == Meat
        } != null
}

fun com.udaan.catalog.models.ModelV1.Vertical.isPharma(): Boolean {
    return this.baseVerticalList
        .firstOrNull {
            it.baseVerticalName == "PharmaAttributes" || it.baseVerticalName == Medicine
        } != null
}

fun com.udaan.catalog.models.ModelV1.Vertical.isFood(): Boolean =
        this.baseVerticalList.any { it.baseVerticalName == Food }

fun com.udaan.catalog.models.ModelV1.Vertical.isFMCG(): Boolean =
        this.baseVerticalList.any { it.baseVerticalName == FMCG } || this.isDairy()

fun com.udaan.catalog.models.ModelV1.Vertical.isDairy(): Boolean =
        this.baseVerticalList.any { it.baseVerticalName == Dairy }

fun com.udaan.catalog.models.ModelV1.Vertical.isFoodOrFMCG() = this.isFood() or this.isFMCG()

fun com.udaan.catalog.models.ModelV1.Vertical.toSupplyChainCategory(): SupplyChainCategory {
    return when {
        this.isFoodOrFMCG() -> FOOD
        this.isFresh() -> FRESH
        this.isMeat() -> MEAT
        this.isPharma() -> PHARMA
        else -> {
            logger().value.warn("Vertical ${this.name} not found in any category, defaulting to INTERCITY")
            INTERCITY
        }
    }
}
