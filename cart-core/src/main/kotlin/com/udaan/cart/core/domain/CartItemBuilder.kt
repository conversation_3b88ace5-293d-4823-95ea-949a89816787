package com.udaan.cart.core.domain

import com.udaan.cart.core.domain.models.*
import com.udaan.cart.core.utils.IdUtils
import java.time.Instant
import java.util.*

class CartItemBuilder {
    fun build(
        cartId: String,
        quantity: Int,
        product: ProductItem,
        perUnitAmountInPaise: Long,
        puInfo: ItemPUInfo? = null,
    ): CartItem {
        return CartItem(
            id = IdUtils.getCartItemId(),
            cartId = cartId,
            quantity = quantity,
            product = product,
            perUnitAmountInPaise = perUnitAmountInPaise,
            priceDetails = ItemPriceDetails(
                perUnitAmountInPaise,
                puInfo = puInfo
            ),
            properties = ItemProperties(),
            createdAt = Date.from(Instant.now()),
            updatedAt = Date.from(Instant.now()),
        )
    }
}
