package com.udaan.cart.core.selectors.data

import com.udaan.cart.models.common.CartSelection
import com.udaan.proto.models.ModelV1

class DefaultSelectorData(
    private val buyerId: String,
    private val platformId: ModelV1.SellingPlatform,
    private val cartSelection: CartSelection? = null,
    val cartId: String? = null,
) : SelectorData {
    override fun getBuyerId() = buyerId
    override fun getPlatformId() = platformId
    override fun getCartSelection() = cartSelection
}
