package com.udaan.cart.core.payment

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.context.CartContextKey
import com.udaan.cart.core.domain.models.DetailedCart
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.cart.core.domain.models.PaymentCollectionType
import com.udaan.cart.core.utils.BuyerProfile
import com.udaan.cart.core.utils.getBuyerProfile
import com.udaan.cart.models.common.CategoryGroupKeyBasedSelection
import com.udaan.cart.models.common.MetadataVariables
import com.udaan.cart.models.common.PaymentMethodStatus
import com.udaan.common.utils.kotlin.logger
import com.udaan.credit.client.CreditServiceClient
import com.udaan.credit.models.CreditEligibleReqOrderDetail
import com.udaan.credit.models.CreditEligibleReqOrderLineDetail
import com.udaan.credit.models.CreditEligibleRequestV2
import com.udaan.credit.utils.showCreditPaymentMethodInCart
import com.udaan.instrumentation.TelemetryScope
import com.udaan.model.orgs.BusinessClass
import com.udaan.model.orgs.OrgBusinessType
import com.udaan.model.orgs.toHorecaBusinessClass
import com.udaan.tradecredit.client.TradeCreditClient
import com.udaan.tradecredit.models.req.CreditOrderEligibleRequest
import com.udaan.tradecredit.models.res.CreditOrderEligibleResponse
import com.udaan.tradecredit.models.res.PendingTaskResponse
import com.udaan.tradecredit.models.req.CreditEligibleReqOrderDetail as TradeCapitalCreditEligibleReqOrderDetail

@Singleton
class CreditMethodEvaluator @Inject constructor(
    private val creditServiceClient: CreditServiceClient,
    private val tradeCreditClient: TradeCreditClient
) : PaymentMethodEvaluator {
    companion object {
        private val logger by logger()
        private val defaultEvalResult = PaymentEvalResult(
            collectionType = PaymentCollectionType.CREDIT,
            allowOtherMethods = true,
            isValidMethod = false,
            metaData = emptyMap(),
            payableAmountInPaise = 0L,
            remainingAmountInPaise = 0L,
            paymentBps = 0L,
        )
        private val DISABLED_TC_LINE_STATES = listOf("SHUTDOWN", "RECOVERY")
    }

    override suspend fun evaluate(context: PaymentEvalContext): PaymentEvalResult {
        val detailedCart = context.detailedCart
        val listingProductItem = detailedCart.cart.items.firstOrNull()?.product as? ListingProductItem
        val tradeCapitalCreditLineId = listingProductItem?.creditLineId
        val creditTenure = listingProductItem?.creditTenure
        if (detailedCart.cart.items.isEmpty()) return defaultEvalResult
        return if (tradeCapitalCreditLineId == null) {
            checkUdaanCapitalCreditEligibility(detailedCart, context)
        } else {
            checkTradeCapitalCreditEligibility(detailedCart, context, tradeCapitalCreditLineId, creditTenure)
        }
    }

    @Suppress("ReturnCount")
    private suspend fun checkTradeCapitalCreditEligibility(
        detailedCart: DetailedCart,
        context: PaymentEvalContext,
        tradeCapitalCreditLineId: String,
        creditTenure: String?
    ): PaymentEvalResult {
        val orgId = context.buyerOrg?.orgAccount?.orgId
        // TC payment option is not enabled for null creditTenure
        if (creditTenure == null) {
            val cartId = detailedCart.cart.id
            logger.warn(
                "checkTradeCapitalCreditEligibility::TC payment option is disabled due to null creditTenure::" +
                "creditLineId $tradeCapitalCreditLineId, orgId $orgId, cartId $cartId"
            )
            return defaultEvalResult.copy(payableAmountInPaise = detailedCart.totalAmount())
        }

        val creditTncInfoDeferred = TelemetryScope.async {
            orgId?.let {
                tradeCreditClient.isTnCAccepted(buyerOrgId = it, lineId = tradeCapitalCreditLineId,).executeAwait()
            }
        }
        val ordersDetails = detailedCart.cart.items.groupBy { item ->
            item.product as ListingProductItem
            item.product.sellerId
        }.map {  (sellerId, items) ->
            val itemsAmountInPaise = items.sumOf { item ->
                detailedCart.getItemTotalWithTax(item)
            }
            TradeCapitalCreditEligibleReqOrderDetail(
                orderId = sellerId,
                amountPaisa = itemsAmountInPaise
            )
        }
        val creditOrderEligibleRequest = CreditOrderEligibleRequest(
            creditLineId = tradeCapitalCreditLineId,
            ordersDetails = ordersDetails,
            cpodOrder = context.cartContext.getValue<Boolean>(CartContextKey.CPOD_ENABLED) ?: false
        )
        logger.info("CPOD flag sent in checkCreditEligibilityForOrders request $orgId ${creditOrderEligibleRequest.cpodOrder}")
        val creditEligibilityResponse = try {
            tradeCreditClient.checkCreditEligibilityForOrders(
                creditOrderEligibleRequest
            ).executeAwait().also {
                logger.info("tradeCreditEligibilityResponse : $it")
            }
        } catch (e: Exception) {
            logger.error("Failed to check credit eligibility for $tradeCapitalCreditLineId: ${e.message}", e)
            return defaultEvalResult.copy(payableAmountInPaise = detailedCart.totalAmount())
        }

        if (creditEligibilityResponse.eligible.not()) {
            val isCreditLineDisabled = creditEligibilityResponse.metaData[MetadataVariables.STATE.name] in DISABLED_TC_LINE_STATES
            logger.info(
                "Trade credit line $tradeCapitalCreditLineId not eligible for orgId $orgId, " +
                "error ${creditEligibilityResponse.metaData[MetadataVariables.ERROR.name]}"
            )
            /**
             * Earlier we were not showing the Credit payment method on app if
             * eligibility is not met.
             * Now we have decided to show the method in disabled state with
             * appropriate callout for lines that are not in Recovery or Shutdown states
             */
            if(isCreditLineDisabled) return defaultEvalResult.copy(payableAmountInPaise = detailedCart.totalAmount())
        }

        return PaymentEvalResult(
            collectionType = PaymentCollectionType.TRADE_CREDIT,
            allowOtherMethods = true,
            isValidMethod = true,
            metaData = buildMetadataForTradeCapital(
                detailedCart, creditEligibilityResponse, creditTncInfoDeferred.await()
            ),
            payableAmountInPaise = detailedCart.totalAmount(),
            remainingAmountInPaise = 0L,
            paymentBps = 0L
        )
    }

    private fun buildMetadataForTradeCapital(
        detailedCart: DetailedCart,
        creditEligibilityResponse: CreditOrderEligibleResponse,
        creditTncInfo: PendingTaskResponse?
    ): Map<String, String> {
        val metaData = creditEligibilityResponse.metaData as MutableMap<String, String>
        val remainingOverdue = metaData[MetadataVariables.REMAINING_OVERDUE.name].toString().toLongOrNull() ?: 0
        val availableLimit = metaData[MetadataVariables.AVAILABLE_PURCHASE_AMOUNT.name].toString().toLongOrNull() ?: 0
        val breachAmount = creditEligibilityResponse.metaData[MetadataVariables.BREACH_AMOUNT.name]?.takeIf {
            it.isNotBlank()
        }
        val isAmountBreached = (breachAmount == null || breachAmount.toLong() == 0L).not()
        when {
            isAmountBreached || creditEligibilityResponse.eligible.not() ->
                metaData[MetadataVariables.STATUS.name] = PaymentMethodStatus.INACTIVE.name
            creditTncInfo?.tncAccepted == false -> {
                metaData[MetadataVariables.STATUS.name] = PaymentMethodStatus.TNC.name
                metaData[MetadataVariables.TNC_LINK.name] = creditTncInfo.tcnLink.toString()
            }
            else -> metaData[MetadataVariables.STATUS.name] = PaymentMethodStatus.ACTIVE.name
        }

        if (detailedCart.cart.selection is CategoryGroupKeyBasedSelection) {
            metaData[MetadataVariables.CART_SELECTION_KEY.name] = detailedCart.cart.selection.key
        }

        metaData[MetadataVariables.LINE_TYPE.name]=
                creditEligibilityResponse.metaData[MetadataVariables.LINE_TYPE.name].toString()

        val isCompleteOverdue = remainingOverdue > 0L && availableLimit == 0L
        val statusCallout = when(metaData[MetadataVariables.STATUS.name]) {
            /**
             * Sending TnC callout as null so UI can show the formatted
             * TnC callout with link, that is hardcoded on UI.
             * In case we want to update that message we can just start
             * sending a non-null value from here, and it will reflect on UI
             */
            PaymentMethodStatus.TNC.name -> null
            PaymentMethodStatus.INACTIVE.name ->
                if(isCompleteOverdue) "Clear Overdues to avail Credit"
                else "Available credit is insufficient. Clear dues to use credit for ordering"
            else -> null
        }
        if(statusCallout != null) metaData[MetadataVariables.STATUS_CALLOUT.name] = statusCallout


        return metaData.toMap()
    }

    @Suppress("LongMethod", "ReturnCount")
    private suspend fun checkUdaanCapitalCreditEligibility(
        detailedCart: DetailedCart,
        context: PaymentEvalContext
    ): PaymentEvalResult {
        // disable UC credit if trade credit is enabled
        context.buyerOrg?.orgAccount?.orgId?.let {
            if (tradeCreditClient.isCreditEnabledForBuyer(it).executeAwait())
                return defaultEvalResult.copy(payableAmountInPaise = detailedCart.totalAmount())
        }
        val orderDetail = detailedCart.cart.items.groupBy { item ->
            item.product as ListingProductItem
            item.product.sellerId
        }.map {  (sellerId, items) ->
            val itemsAmountInPaise = items.sumOf { item ->
                detailedCart.getItemTotalWithTax(item)
            }
            CreditEligibleReqOrderDetail(
                sellerId = sellerId,
                orderId = sellerId,
                amountPaisa = itemsAmountInPaise,
                orderLines = items.map { item ->
                    item.product as ListingProductItem
                    CreditEligibleReqOrderLineDetail(
                        orderLineId = item.id,
                        listingId = item.product.listingId,
                        vertical = "", // TODO:
                        amountPaisa = detailedCart.getItemTotalWithTax(item),
                    )
                }
            )
        }
        val request = CreditEligibleRequestV2(
            buyerOrgId = detailedCart.cart.buyerId,
            prepaidOnlyOrder = false,
            ordersDetail = orderDetail,
        )
        logger.info("Credit request: $request")
        val creditResponse = creditServiceClient.checkCreditEligibilityV2(request).executeAwait(1)
        logger.info("Credit response: $creditResponse")
        if (creditResponse.nonCreditOrderIds.isNotEmpty()) {
            return defaultEvalResult.copy(payableAmountInPaise = detailedCart.totalAmount())
        }
        val isCreditEligible = creditResponse.creditEligibleOrdersResponse.eligible
        val isRA =
            creditResponse.creditEligibleOrdersResponse.metaData.getOrElse("running_account") { "false" }.toBoolean()
        val isNoPod = creditResponse.creditEligibleOrdersResponse.metaData.getOrElse("no_pod") { "false" }.toBoolean()
        val showCreditPaymentMethodOld = isCreditEligible || isRA || isNoPod
        val showCreditPaymentMethod = creditResponse.creditEligibleOrdersResponse.showCreditPaymentMethodInCart()
        val isIotaCreditBuyer = creditResponse.creditEligibleOrdersResponse.metaData
                .getOrElse("iota_pilot_enabled") { "true" }.toBoolean()
        val isHoReCaABBuyer =
            context.cartContext.getBuyerOrgIdentityModel()?.getBuyerProfile() == BuyerProfile.HORECA_AB
        val isCreditRecoveryBuyer = creditResponse.creditEligibleOrdersResponse.metaData["state"] == "RECOVERY"

        val allowOtherMethods = isIotaCreditBuyer && !isHoReCaABBuyer && !isCreditRecoveryBuyer
        logger.info(
            "Credit Payment Check for orgId ${detailedCart.cart.buyerId}: " +
            "showCreditPaymentMethodNew -> $showCreditPaymentMethod, " +
            "showCreditPaymentMethodOld -> $showCreditPaymentMethodOld, " +
            "showCreditPaymentMethod ->  $showCreditPaymentMethod, " +
            "isIotaCreditBuyer -> $isIotaCreditBuyer, " +
            "isHoReCaABBuyer -> $isHoReCaABBuyer, " +
            "isCreditRecoveryBuyer -> $isCreditRecoveryBuyer, " +
            "allowOtherMethods -> $allowOtherMethods "
        )
        return if (showCreditPaymentMethod) {
            PaymentEvalResult(
                collectionType = PaymentCollectionType.CREDIT,
                allowOtherMethods = allowOtherMethods,
                isValidMethod = true,
                metaData = creditResponse.creditEligibleOrdersResponse.metaData,
                payableAmountInPaise = detailedCart.totalAmount(),
                remainingAmountInPaise = 0L,
                paymentBps = 0L,
            )
        } else {
            defaultEvalResult.copy(payableAmountInPaise = detailedCart.totalAmount())
        }
    }
}
