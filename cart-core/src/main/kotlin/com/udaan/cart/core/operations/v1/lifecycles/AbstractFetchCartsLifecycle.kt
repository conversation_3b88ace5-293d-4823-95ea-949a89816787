package com.udaan.cart.core.operations.v1.lifecycles

import arrow.core.Either
import arrow.core.NonEmptyList
import arrow.core.continuations.either
import com.udaan.cart.core.common.helpers.Lifecycle
import com.udaan.cart.core.common.helpers.LifecycleStage
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.hooks.Hook
import com.udaan.cart.core.operations.v1.contexts.FetchCartsContext
import com.udaan.cart.models.default.FetchCartReqDto

abstract class AbstractFetchCartsLifecycle<R> {
    protected abstract suspend fun doFetch(
        context: FetchCartsContext<R>,
    ): Either<OpError, FetchCartsContext<R>>

    suspend fun execute(
        context: FetchCartsContext<R>,
    ): Either<OpError, FetchCartsContext<R>> {
        val stages = listOf(
            LifecycleStage<OpError, FetchCartsContext<R>>(
                pre = emptyList(),
                execute = { currentContext ->
                    doFetch(currentContext)
                },
                post = emptyList(),
            )
        )
        return either {
            Lifecycle(stages = stages)
                .execute(context)
                .bind()
        }
    }
}