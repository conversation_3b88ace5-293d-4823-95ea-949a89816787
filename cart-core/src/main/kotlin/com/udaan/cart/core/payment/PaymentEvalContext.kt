package com.udaan.cart.core.payment

import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.domain.models.DetailedCart
import com.udaan.catalog.models.ModelV2.TradeListing
import com.udaan.proto.models.ModelV1.OrgUnit
import com.udaan.proto.representations.OrgV1.OrgAccountExtendedResponse

data class PaymentEvalContext(
    val detailedCart: DetailedCart,
    val listingsMap: Map<String, TradeListing>,
    val buyerOrg: OrgAccountExtendedResponse?,
    val buyerOrgUnit: OrgUnit?,
    val cartContext: CartContext
)
