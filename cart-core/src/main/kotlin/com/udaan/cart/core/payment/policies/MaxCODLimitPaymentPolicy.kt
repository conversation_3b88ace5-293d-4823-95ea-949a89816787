package com.udaan.cart.core.payment.policies

import com.google.inject.Inject
import com.udaan.cart.core.payment.PaymentEvalContext

class MaxCODLimitPaymentPolicy @Inject constructor(
    private val policyExemptionValidator: PolicyExemptionValidator
) : AbstractPrePaymentPolicy() {
    companion object {
        private const val POLICY_NAME = "MaxCODLimitPaymentPolicy"
    }

    override fun getName(): String = POLICY_NAME

    override suspend fun evaluate(paymentContext: PaymentEvalContext): PrePaymentResult {
        val buyerId = paymentContext.detailedCart.cart.buyerId
        return if (policyExemptionValidator.isExempted(POLICY_NAME, buyerId)) {
            defaultResult()
        } else {
            MaxCollectibleUtil.evaluateCollectibleInfo(paymentContext.detailedCart.totalAmount())
                ?.let { collectibleInfo ->
                    prepareResult(
                        bps = collectibleInfo.bps,
                        description = null,
                        instruction = collectibleInfo.message,
                        totalAmount = paymentContext.detailedCart.totalAmount()
                    )
                } ?: defaultResult()
        }
    }
}
