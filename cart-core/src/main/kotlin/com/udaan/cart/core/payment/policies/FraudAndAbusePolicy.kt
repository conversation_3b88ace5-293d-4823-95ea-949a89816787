package com.udaan.cart.core.payment.policies

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.payment.PaymentEvalContext
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.listing.builder.enrichments.catalog.CategoryConfigHelper
import com.udaan.tradequality.client.CartProfilingClient
import com.udaan.tradequality.models.riskProfiling.CartRiskRequest
import com.udaan.tradequality.models.riskProfiling.OrderMetaData
import com.udaan.tradequality.models.riskProfiling.OrderRiskPrepaymentDetails
import com.udaan.user.client.RedisOrgRepository

@Singleton
class FraudAndAbusePolicy @Inject constructor(
    private val categoryConfigHelper: CategoryConfigHelper,
    private val cartProfilingClient: CartProfilingClient,
) : AbstractPrePaymentPolicy() {
    companion object {
        private val logger by logger()
        private const val POLICY_NAME = "FraudAndAbusePolicy"
    }

    override fun getName(): String = POLICY_NAME

    override suspend fun evaluate(paymentContext: PaymentEvalContext): PrePaymentResult {
        if (paymentContext.listingsMap.values.isEmpty()) {
            return defaultResult()
        }
        return kotlin.runCatching {
            val categories = paymentContext.listingsMap.values.parallelMap { listing ->
                categoryConfigHelper.getCategory(listing, null, null)
            }.filterNotNull()
            val categoryGroupId =
                paymentContext.cartContext.getDetailedCart(paymentContext.buyerOrgUnit?.orgUnitId)?.getCategoryGroupId()

            val sellers = paymentContext.listingsMap.values.map { it.orgId }.toSet()
            if (sellers.size > 1) {
                logger.info("[FraudAndAbusePolicy] Category Group: $categoryGroupId Multiple sellers in cart: $sellers")
            }
            val orderMetadata = OrderMetaData(
                numOrderLines = paymentContext.detailedCart.cart.items.size,
                orderQuantity = paymentContext.detailedCart.cart.items.sumOf { it.quantity },
                orderTimestamp = System.currentTimeMillis(),
                sellerId = sellers.first(),
                totalAmountInPaise = paymentContext.detailedCart.totalAmount(),
                verticalIds = paymentContext.listingsMap.values.map { listing -> listing.vertical }.distinct()
                    .filterNotNull()
                    .filter { x: String? -> x != "" }
            )

            val request = CartRiskRequest(
                cartId = paymentContext.detailedCart.cart.id,
                buyerOrgId = paymentContext.detailedCart.cart.buyerId,
                categoryGroupId = categoryGroupId ?: "",
                categoryIds = categories,
                metaData = orderMetadata,
            )
            val cartRiskyRes = cartProfilingClient.isCartRisky(request).executeAwait()
            logger.info("[FraudAndAbusePolicy] Cart risk res $cartRiskyRes")
            if (cartRiskyRes.orderRisky) {
                cartRiskyRes.prepaymentDetails?.let { orderRiskPaymentDetails ->
                    prepareResult(
                        bps = evaluateBps(orderRiskPaymentDetails),
                        description = null,
                        totalAmount = paymentContext.detailedCart.totalAmount(),
                        instruction = orderRiskPaymentDetails.prepaymentInfoMessage,
                    )
                } ?: defaultResult()
            } else defaultResult()
        }.getOrElse { e ->
            logger.error("[FraudAndAbusePolicy] Failed to validate fraud and abuse policy", e)
            defaultResult()
        }
    }

    private fun evaluateBps(riskDetails: OrderRiskPrepaymentDetails): Int {
        // In case of prepayment required, ask for complete 100% prepayment
        return if (riskDetails.prepaymentPercentage != 0) {
            HUNDRED_PERCENT_BPS
        } else {
            riskDetails.prepaymentPercentage * 100
        }
    }
}
