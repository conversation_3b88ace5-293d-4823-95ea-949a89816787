package com.udaan.cart.core.hooks

import arrow.core.Either
import com.udaan.cart.core.common.models.OpError
import com.udaan.cart.core.context.BaseContext
import com.udaan.cart.core.context.BaseOperationContext
import com.udaan.cart.core.context.CartContext

interface Hook<E: OpError, T: BaseContext> {
    suspend fun execute(context: T): Either<E, T>
}

interface BaseCartHook<E: OpError> : Hook<E, CartContext> {
    override suspend fun execute(
        context: CartContext
    ): Either<E, CartContext>
}

interface BaseOperationHook<E: OpError, T : BaseOperationContext> : Hook<E, T> {
    override suspend fun execute(context: T): Either<E, T>
}
