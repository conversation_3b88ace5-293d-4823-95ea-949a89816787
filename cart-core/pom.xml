<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.udaan.cart</groupId>
        <artifactId>cart-parent</artifactId>
        <version>2.0-SNAPSHOT</version>
    </parent>

    <artifactId>cart-core</artifactId>
    <packaging>jar</packaging>

    <name>Udaan cart Core</name>

    <dependencies>

        <!-- Udaan cart -->
        <dependency>
            <groupId>com.udaan.cart</groupId>
            <artifactId>cart-models</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <!-- Udaan -->
        <dependency>
            <groupId>com.udaan.config</groupId>
            <artifactId>config-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.common</groupId>
            <artifactId>common-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.common</groupId>
            <artifactId>common-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.common</groupId>
            <artifactId>common-server</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.users</groupId>
            <artifactId>user-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.users</groupId>
            <artifactId>user-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.udaan-orderform</groupId>
            <artifactId>orderform-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.udaan-orderform</groupId>
            <artifactId>orderform-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.udaan-orderform</groupId>
            <artifactId>orderform-models</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.compliance</groupId>
            <artifactId>client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.incentives</groupId>
            <artifactId>promotions-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.constraint</groupId>
            <artifactId>constraint-models</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.constraint</groupId>
            <artifactId>constraint-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.invoicing</groupId>
            <artifactId>invoicing-math</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.invoicing</groupId>
            <artifactId>invoicing-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.listing</groupId>
            <artifactId>listing-builder</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.dropslot</groupId>
            <artifactId>dropslot-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.tradequality</groupId>
            <artifactId>trade-quality-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.order-service</groupId>
            <artifactId>order-read-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.tradecredit</groupId>
            <artifactId>trade-credit-client</artifactId>
        </dependency>

        <!-- Clients, Drivers etc. -->

        <!-- Languages & Frameworks -->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-coroutines-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.jetbrains.kotlin</groupId>
                    <artifactId>kotlin-stdlib-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-coroutines-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.inject</groupId>
            <artifactId>guice</artifactId>
        </dependency>
        <dependency>
            <groupId>io.arrow-kt</groupId>
            <artifactId>arrow-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.jetbrains.kotlin</groupId>
                    <artifactId>kotlin-stdlib-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jdbi</groupId>
            <artifactId>jdbi3-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jdbi</groupId>
            <artifactId>jdbi3-postgres</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jdbi</groupId>
            <artifactId>jdbi3-sqlobject</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jdbi</groupId>
            <artifactId>jdbi3-kotlin</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jdbi</groupId>
            <artifactId>jdbi3-kotlin-sqlobject</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jdbi</groupId>
            <artifactId>jdbi3-jackson2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito.kotlin</groupId>
            <artifactId>mockito-kotlin</artifactId>
        </dependency>
        <!-- Logging -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jul-to-slf4j</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Utils -->

        <!-- Testing -->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test-junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.mockk</groupId>
            <artifactId>mockk</artifactId>
            <version>1.12.4</version>
        </dependency>

        <!-- Codecs -->
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-csv</artifactId>
        </dependency>

    </dependencies>

    <build>
        <testResources>
            <testResource>
                <directory>${project.basedir}/src/test/resources</directory>
            </testResource>
        </testResources>

        <plugins>

            <plugin>
                <artifactId>kotlin-maven-plugin</artifactId>
                <groupId>org.jetbrains.kotlin</groupId>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <source>src/main/kotlin</source>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <phase>process-test-sources</phase>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>
</project>
