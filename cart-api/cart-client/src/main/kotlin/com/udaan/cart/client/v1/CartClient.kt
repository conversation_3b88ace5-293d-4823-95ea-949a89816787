package com.udaan.cart.client.v1

import com.udaan.cart.models.common.DeliverySlotDto
import com.udaan.cart.models.default.*
import com.udaan.common.client.UdaanClientConfig
import com.udaan.common.client.UdaanServiceClient

class CartClient(config: UdaanClientConfig) : UdaanServiceClient(config) {
    fun fetchCart(fetchCartReqDto: FetchCartReqDto) =
        postResourceWithBody<Response<CartResDto>>(
            "/cart/app/v1/fetch",
            fetchCartReqDto,
        )

    fun fetchCarts(fetchCartsReqDto: FetchCartReqDto) =
        postResourceWithBody<Response<CartsResDto>>(
            "/cart/app/v1/fetch-multiple",
            fetchCartsReqDto,
        )

    fun editCart(editCartReqDto: EditCartReqDto) =
        putResourceWithBody<CartResDto>(
            "/cart/app/v1/edit",
            editCartReqDto,
        )

    fun deleteCart(deleteCartReqDto: DeleteCartReqDto) =
        putResourceWithBody<Response<String>>(
            "/cart/app/v1/delete",
            deleteCartReqDto
        )

    fun fetchDeliverySlots(fetchCartReqDto: FetchCartReqDto) =
        postResourceWithBody<Response<DeliverySlotDto>>(
            path = "/cart/app/v1/delivery-slots",
            fetchCartReqDto,
        )

    fun fetchPaymentMethods(paymentMethodsReqDto: PaymentMethodsReqDto) =
        postResourceWithBody<Response<PaymentMethodsResDto>>(
            path = "/cart/app/v1/payment-methods",
            paymentMethodsReqDto,
        )

    fun checkout(checkoutRequestDto: CheckoutRequestDto) =
        postResourceWithBody<Response<CheckoutResponseDto>>(
            path = "/cart/app/v1/checkout",
            checkoutRequestDto,
        )

    /**
     * Client to update creditLineId for all active carts associated to a buyer
     *
     * @property updateCreditLineReqDto - data class of the request object
     * @property    buyerId - buyerId whose creditLines details needs to be changed
     * @property    creditLineId - the new creditLineId which needs to be updated
     * @property    platformId - platform associated to the request
     * @property    cartSelection - the identifier logic to determine the carts
     */
    fun updateCreditLine(updateCreditLineReqDto: UpdateCreditLineReqDto) =
        putResourceWithBody<Response<String>>(
            path = "/cart/app/v1/updateCreditLine",
            updateCreditLineReqDto
    )
}
