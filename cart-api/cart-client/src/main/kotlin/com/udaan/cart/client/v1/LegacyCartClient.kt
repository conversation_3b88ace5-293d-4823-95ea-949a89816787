package com.udaan.cart.client.v1

import com.udaan.cart.models.default.*
import com.udaan.common.client.UdaanClientConfig
import com.udaan.common.client.UdaanServiceClient
import com.udaan.orderform.models.dto.MultiSellerDto

class LegacyCartClient(config: UdaanClientConfig) : UdaanServiceClient(config) {
    fun fetchHorecaPaymentMethods(fetchCartReqDto: FetchCartReqDto) =
        postResourceWithBody<Response<MultiSellerDto.PaymentBasedCartResponse>>(
            path = "/cart/apps/v1/legacy/horeca/payment-methods",
            fetchCartReqDto,
        )

    fun pacmanCheckout(placeReqDto: LegacyMultiSellerPlaceReqDto) =
        postResourceWithBody<Response<MultiSellerDto.PlaceMultiOrderResponse>>(
            path = "/cart/apps/v1/legacy/pacman/checkout",
            placeReqDto,
        )

    fun fetchMarioPaymentMethods(fetchCartReqDto: FetchCartReqDto) =
        postResourceWithBody<Response<CartPaymentResDto>>(
            path = "/cart/apps/v1/legacy/mario/payment-methods",
            fetchCartReqDto,
        )

    fun markPlaced(markPlacedReqDto: MarkPlacedReqDto) =
        postResourceWithBody<Response<Any>>(
            path = "/cart/apps/v1/legacy/cart/mark-completed",
            markPlacedReqDto,
        )
}
