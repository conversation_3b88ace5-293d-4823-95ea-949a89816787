<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.udaan</groupId>
        <artifactId>udaan-parent</artifactId>
        <version>[3.0,4.0)</version>
        <relativePath/>
    </parent>

    <groupId>com.udaan.cart</groupId>
    <artifactId>cart-api</artifactId>
    <version>2.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>Udaan cart API</name>

    <modules>
        <module>cart-models</module>
        <module>cart-client</module>
    </modules>

    <properties>
        <versions.udaan-common>[2.0,3.0)</versions.udaan-common>
        <versions.udaan-user>[2.0,3.0)</versions.udaan-user>
        <versions.orderform>[2.0,3.0)</versions.orderform>
        <versions.rewards>[1.0,2.0)</versions.rewards>
        <versions.constraint>[1.0,2.0)</versions.constraint>
    </properties>

    <dependencyManagement>
        <dependencies>

            <!-- Udaan Common -->
            <dependency>
                <groupId>com.udaan.common</groupId>
                <artifactId>common-api</artifactId>
                <version>${versions.udaan-common}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.common</groupId>
                <artifactId>common-client</artifactId>
                <version>${versions.udaan-common}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.users</groupId>
                <artifactId>user-model</artifactId>
                <version>${versions.udaan-user}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.udaan-orderform</groupId>
                <artifactId>orderform-models</artifactId>
                <version>${versions.orderform}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.rewards</groupId>
                <artifactId>rewards-models</artifactId>
                <version>${versions.rewards}</version>
            </dependency>
            <dependency>
                <groupId>com.udaan.constraint</groupId>
                <artifactId>constraint-models</artifactId>
                <version>${versions.constraint}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <sourceDirectory>src/main/kotlin</sourceDirectory>
        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>1.3.0.Final</version>
            </extension>
        </extensions>
    </build>
</project>
