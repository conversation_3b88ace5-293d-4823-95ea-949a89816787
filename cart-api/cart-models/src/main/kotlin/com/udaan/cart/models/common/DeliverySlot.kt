package com.udaan.cart.models.common

import com.udaan.cart.models.BaseResponseDto

data class DeliverySlotDetails(
    val slotId: String,
    val available: Boolean,
    val currentActive: Boolean,
    val delayedSla: Boolean?,
    val startTimestamp: Long,
    val endTimestamp: Long,
)

data class CategoryDeliverySlot(
    val category: String,
    val slots: List<DeliverySlotDetails>,

)

data class ItemLevelDeliverySlot (
    val listingId: String,
    val salesUnitId: String,
    val category: String,
    val slots: List<DeliverySlotDetails>
)

data class DeliverySlotDto(
    @Deprecated("Deprecated. Use itemDeliverySlots") val deliverySlots: List<CategoryDeliverySlot>,
    val itemDeliverySlots: List<ItemLevelDeliverySlot>? = null
): BaseResponseDto