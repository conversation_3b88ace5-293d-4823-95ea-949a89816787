package com.udaan.cart.models.default

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.cart.models.BaseRequestDto
import com.udaan.cart.models.common.CartSelection
import com.udaan.orderform.models.dto.MultiSellerDto
import com.udaan.proto.models.ModelV1

@JsonIgnoreProperties(ignoreUnknown = true)
data class LegacyMultiSellerPlaceReqDto(
    val buyerId: String,
    val platformId: ModelV1.SellingPlatform,
    val placeMultiOrder: MultiSellerDto.PlaceMultiOrderRequest,
) : BaseRequestDto {
    override fun getReqBuyerId(): String = buyerId

    override fun getReqPlatformId(): ModelV1.SellingPlatform = platformId

    override fun getReqCartSelection(): CartSelection? = null
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class MarkPlacedReqDto(
    val buyerId: String,
    val platformId: ModelV1.SellingPlatform,
    val orderIds: List<String>,
) : BaseRequestDto {
    override fun getReqBuyerId(): String = buyerId

    override fun getReqPlatformId(): ModelV1.SellingPlatform = platformId

    override fun getReqCartSelection(): CartSelection? = null
}
