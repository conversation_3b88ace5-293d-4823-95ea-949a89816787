package com.udaan.cart.models.common

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo

enum class CartSelectionType {
    BUYER_BASED_SELECTION,
    CATEGORY_GROUP_SELECTION,
    CATEGORY_GROUP_SELLER_SELECTION,
    KEY_BASED_SELECTION,
    CATEGORY_GROUP_ORG_UNIT_SELECTION,
    CATEGORY_GROUP_CATEGORY_SELECTION,
    CATEGORY_GROUP_BRAND_SELECTION,
    CATEGORY_GROUP_KEY_BASED_SELECTION,
    CATEGORY_GROUP_BUYER_SELECTION,
}

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = BuyerBasedSelection::class, name = "BUYER_BASED_SELECTION"),
    JsonSubTypes.Type(value = CategoryGroupSelection::class, name = "CATEGORY_GROUP_SELECTION"),
    JsonSubTypes.Type(value = CategoryGroupSellerSelection::class, name = "CATEGORY_GROUP_SELLER_SELECTION"),
    JsonSubTypes.Type(value = KeyBasedSelection::class, name = "KEY_BASED_SELECTION"),
    JsonSubTypes.Type(value = CategoryGroupOrgUnitSelection::class, name = "CATEGORY_GROUP_ORG_UNIT_SELECTION"),
    JsonSubTypes.Type(value = CategoryGroupCategorySelection::class, name = "CATEGORY_GROUP_CATEGORY_SELECTION"),
    JsonSubTypes.Type(value = CategoryGroupBrandSelection::class, name = "CATEGORY_GROUP_BRAND_SELECTION"),
    JsonSubTypes.Type(value = CategoryGroupKeyBasedSelection::class, name = "CATEGORY_GROUP_KEY_BASED_SELECTION"),
    JsonSubTypes.Type(value = CategoryGroupBuyerSelection::class, name = "CATEGORY_GROUP_BUYER_SELECTION"),
)
sealed class CartSelection(val type: CartSelectionType)

class BuyerBasedSelection : CartSelection(CartSelectionType.BUYER_BASED_SELECTION)
data class CategoryGroupSelection(
    val categoryGroupId: String,
) : CartSelection(CartSelectionType.CATEGORY_GROUP_SELECTION)

data class CategoryGroupCategorySelection(
    val categoryGroupId: String,
    val category: String,
) : CartSelection(CartSelectionType.CATEGORY_GROUP_CATEGORY_SELECTION)

data class CategoryGroupSellerSelection(
    val sellerId: String,
    val categoryGroupId: String,
) : CartSelection(CartSelectionType.CATEGORY_GROUP_SELLER_SELECTION)

data class CategoryGroupBrandSelection(
    val brandId: String,
    val categoryGroupId: String,
) : CartSelection(CartSelectionType.CATEGORY_GROUP_BRAND_SELECTION)

data class CategoryGroupKeyBasedSelection(
    val categoryGroupId: String,
    val key: String,
) : CartSelection(CartSelectionType.CATEGORY_GROUP_KEY_BASED_SELECTION)

data class KeyBasedSelection(
    val key: String
) : CartSelection(CartSelectionType.KEY_BASED_SELECTION)

data class CategoryGroupOrgUnitSelection(
    val categoryGroupId: String,
    val orgUnitId: String,
) : CartSelection(CartSelectionType.CATEGORY_GROUP_ORG_UNIT_SELECTION)

// use only for Adhoc use-cases; i.e., when selection key isn't available and we want to query all carts of buyer
// and categoryGroupId. By default, always use via cartSelectionKey
data class CategoryGroupBuyerSelection(
    val categoryGroupId: String,
) : CartSelection(CartSelectionType.CATEGORY_GROUP_BUYER_SELECTION)
