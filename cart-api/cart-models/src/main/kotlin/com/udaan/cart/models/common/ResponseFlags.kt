package com.udaan.cart.models.common

enum class ResponseFlag {
    APPLY_PROMOS,
    APPLY_LATEST_PRICES,
    CHECK_VIOLATIONS,
    CHECK_INVENTORY_AVAILABILITY,

    @Deprecated("This should not be in use")
    BUILD_ITEM_LEVEL_DELIVERY_SLOT
}

typealias ResponseFlags = Map<ResponseFlag, Boolean>

class ResponseFlagsDataBuilder {
    private val flags = mutableMapOf<ResponseFlag, Boolean>()

    companion object {
        /**
         * Returns cart with all blocks like promotions, violations, inventory checks etc
         */
        fun detailedCartFlags(): ResponseFlags {
            return ResponseFlagsDataBuilder()
                .applyLatestPrices()
                .applyPromos()
                .checkViolations()
                .checkInventoryAvailability()
                .build()
        }

        /**
         * Returns cart with the latest prices only
         */
        fun miniCartFlags(): ResponseFlags {
            return ResponseFlagsDataBuilder()
                .applyLatestPrices()
                .build()
        }

        /**
         * Returns cart from db without any changes or additional info
         */
        fun emptyFlags(): ResponseFlags {
            return ResponseFlagsDataBuilder()
                .build()
        }
    }

    fun applyPromos(): ResponseFlagsDataBuilder {
        this.flags[ResponseFlag.APPLY_PROMOS] = true
        return this
    }

    fun disablePromos(): ResponseFlagsDataBuilder {
        this.flags[ResponseFlag.APPLY_PROMOS] = false
        return this
    }

    fun applyLatestPrices(): ResponseFlagsDataBuilder {
        this.flags[ResponseFlag.APPLY_LATEST_PRICES] = true
        return this
    }

    fun checkViolations(): ResponseFlagsDataBuilder {
        this.flags[ResponseFlag.CHECK_VIOLATIONS] = true
        return this
    }

    fun checkInventoryAvailability(): ResponseFlagsDataBuilder {
        this.flags[ResponseFlag.CHECK_INVENTORY_AVAILABILITY] = true
        return this
    }

    @Deprecated("Deprecating this method")
    fun buildItemLevelDeliverySlots(): ResponseFlagsDataBuilder {
        return this
    }

    fun build(): ResponseFlags {
        return flags
    }
}
