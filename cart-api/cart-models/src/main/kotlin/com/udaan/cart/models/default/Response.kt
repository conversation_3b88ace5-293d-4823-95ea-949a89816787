package com.udaan.cart.models.default

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.udaan.cart.models.BaseResponseDto
import com.udaan.cart.models.common.*
import com.udaan.constraint.models.dtos.DeliveryChargeOffer
import com.udaan.constraint.models.dtos.DeliveryChargeResponseDto
import com.udaan.orderform.cart.models.dto.OrderSLA
import com.udaan.rewards.models.SchemeDetailsV2

@JsonIgnoreProperties(ignoreUnknown = true)
data class InventoryInfo(
    val available: Boolean,
    // Don't depend on this value as the source for this info (Orchestrator) is passing 0 for all cases
    val availableQuantity: Long,
    val isActive: Boolean = true,
)

enum class CashbackType {
    INSTANT_CASHBACK,
}

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "cashbackType"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = InstantCashback::class, name = "INSTANT_CASHBACK")

)
sealed class CashbackDetail

data class InstantCashback(
    val totalCashbackInPaise: Long
): CashbackDetail()


@JsonIgnoreProperties(ignoreUnknown = true)
data class CartResDto @JvmOverloads constructor(
    val cartId: String,
    val buyerId: String,
    val platformId: String,
    val cartSelection: CartSelection,
    val cartItems: List<CartItem>,
    val priceDetails: PriceDetails,
    val offers: List<Offer> = emptyList(),
    val unavailabilityMap: Map<String, InventoryInfo> = emptyMap(),
    val violations: List<ViolationDto> = emptyList(),
    val additionalData: AdditionalData,
    val schemeDetailsV2: Set<SchemeDetailsV2> = emptySet(),
    val deliveryChargeResponse: DeliveryChargeResponseDto? = null,
    @Deprecated("use deliveryChargeOffers from deliveryChargeResponseDto.deliveryChargeOffers") val deliveryChargeOffers: List<DeliveryChargeOffer> = emptyList(),
    val cashbackDetails: List<CashbackDetail> = emptyList(),
) : BaseResponseDto {
    @Suppress("LongParameterList")
    fun copy(
        cartId: String,
        buyerId: String,
        platformId: String,
        cartSelection: CartSelection,
        cartItems: List<CartItem>,
        priceDetails: PriceDetails,
        offers: List<Offer> = emptyList(),
        unavailabilityMap: Map<String, InventoryInfo> = emptyMap(),
        violations: List<ViolationDto> = emptyList(),
        additionalData: AdditionalData
    ) = CartResDto(
        cartId = cartId,
        buyerId = buyerId,
        platformId = platformId,
        cartSelection = cartSelection,
        cartItems = cartItems,
        priceDetails = priceDetails,
        offers = offers,
        unavailabilityMap = unavailabilityMap,
        violations = violations,
        additionalData = additionalData,
        schemeDetailsV2 = emptySet()
    )

    @Suppress("LongParameterList")
    fun copy(
        cartId: String,
        buyerId: String,
        platformId: String,
        cartSelection: CartSelection,
        cartItems: List<CartItem>,
        priceDetails: PriceDetails,
        offers: List<Offer> = emptyList(),
        unavailabilityMap: Map<String, InventoryInfo> = emptyMap(),
        violations: List<ViolationDto> = emptyList(),
        additionalData: AdditionalData,
        schemeDetailsV2: Set<SchemeDetailsV2>
    ) = CartResDto(
        cartId = cartId,
        buyerId = buyerId,
        platformId = platformId,
        cartSelection = cartSelection,
        cartItems = cartItems,
        priceDetails = priceDetails,
        offers = offers,
        unavailabilityMap = unavailabilityMap,
        violations = violations,
        additionalData = additionalData,
        schemeDetailsV2 = schemeDetailsV2,
        deliveryChargeOffers = emptyList()
    )

    @Suppress("LongParameterList")
    fun copy(
        cartId: String,
        buyerId: String,
        platformId: String,
        cartSelection: CartSelection,
        cartItems: List<CartItem>,
        priceDetails: PriceDetails,
        offers: List<Offer> = emptyList(),
        unavailabilityMap: Map<String, InventoryInfo> = emptyMap(),
        violations: List<ViolationDto> = emptyList(),
        additionalData: AdditionalData,
        schemeDetailsV2: Set<SchemeDetailsV2>,
        deliveryChargeOffers: List<DeliveryChargeOffer>
        ) = CartResDto(
        cartId = cartId,
        buyerId = buyerId,
        platformId = platformId,
        cartSelection = cartSelection,
        cartItems = cartItems,
        priceDetails = priceDetails,
        offers = offers,
        unavailabilityMap = unavailabilityMap,
        violations = violations,
        additionalData = additionalData,
        schemeDetailsV2 = schemeDetailsV2,
        deliveryChargeResponse = null
    )

    @Suppress("LongParameterList")
    fun copy(
        cartId: String,
        buyerId: String,
        platformId: String,
        cartSelection: CartSelection,
        cartItems: List<CartItem>,
        priceDetails: PriceDetails,
        offers: List<Offer> = emptyList(),
        unavailabilityMap: Map<String, InventoryInfo> = emptyMap(),
        violations: List<ViolationDto> = emptyList(),
        additionalData: AdditionalData,
        schemeDetailsV2: Set<SchemeDetailsV2>,
        deliveryChargeOffers: List<DeliveryChargeOffer>,
        deliveryChargeResponse: DeliveryChargeResponseDto?
    ) = CartResDto(
        cartId = cartId,
        buyerId = buyerId,
        platformId = platformId,
        cartSelection = cartSelection,
        cartItems = cartItems,
        priceDetails = priceDetails,
        offers = offers,
        unavailabilityMap = unavailabilityMap,
        violations = violations,
        additionalData = additionalData,
        schemeDetailsV2 = schemeDetailsV2,
        deliveryChargeResponse = deliveryChargeResponse,
        cashbackDetails = emptyList()
    )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class CartsResDto(
    val carts: List<CartResDto>
) : BaseResponseDto

@JsonIgnoreProperties(ignoreUnknown = true)
data class CartPaymentResDto(
    val cart: CartResDto,
    val paymentMethods: OrderFormPaymentMethod,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class Response<R>(
    val status: Boolean,
    val data: R?,
    val error: String? = null,
) : BaseResponseDto

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class PaymentMethodsResDto(
    val paymentMethods: List<PaymentMethodDto>
): BaseResponseDto

data class RewardsPaymentDetails(
    val coins: Int,
    val rewardAmountInPaise: Long,
    val payableAmountInPaise: Long
)

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class PaymentMethodDto(
    val collectionType: PaymentCollectionType,
    @Deprecated("This field is deprecated, refer title and ctaTitle otherwise fallback this")
    val displayName: String,
    val description: String,
    val instructions: String,
    val offers: List<String>,
    val payableAmountInPaise: Long,
    val remainingPayableAmountInPaise: Long,
    val prepaymentBps: Long = 0,
    val metaData: Map<String, Any>,
    val title: String? = null,
    val ctaTitle: String? = null,
    val rewardsPaymentDetails: RewardsPaymentDetails? = null,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class CheckoutResponseDto @JvmOverloads constructor(
    val placedOrderIds: List<String>,
    val prePaymentId: String? = null,
    val orderSlasMap: Map<String, OrderSLA>? = null,
    val mainOrderId: String? = null,
    val otp: String? = null,
): BaseResponseDto {
    fun copy(
        placedOrderIds: List<String>,
        prePaymentId: String? = null,
        orderSlasMap: Map<String, OrderSLA>? = null,
        mainOrderId: String? = null,
    ) = CheckoutResponseDto(
        placedOrderIds = placedOrderIds,
        prePaymentId = prePaymentId,
        orderSlasMap = orderSlasMap,
        mainOrderId = mainOrderId,
        otp = null
    )
}
