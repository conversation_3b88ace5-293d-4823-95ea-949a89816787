package com.udaan.cart.models.common

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo

enum class ProductType {
    LISTING,
    COMBO,
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class PUReqDto(
    val puType: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class FreebieInfo(
    val parentCartLineId: String,
)

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = ListingProduct::class, name = "LISTING"),
    JsonSubTypes.Type(value = ComboProduct::class, name = "COMBO"),
)
sealed class Product(val type: ProductType)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ListingProduct(
    val listingId: String,
    val salesUnitId: String,
    val quantity: Int,
    val puInfo: PUReqDto? = null,
    val freebieInfo: FreebieInfo? = null,
    // specifies credit payment tenure given for vertical of a listing
    val creditTenure: String? = null,
    val creditLineId: String? = null
) : Product(ProductType.LISTING)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ComboProduct(
    val comboId: String,
    val listings: List<ComboListing>,
    val quantity: Int,
) : Product(ProductType.COMBO)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ComboListing(
    val listingId: String,
    val salesUnitId: String,
)
