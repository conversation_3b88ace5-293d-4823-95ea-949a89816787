package com.udaan.cart.models.common

data class ShopTimings @JvmOverloads constructor(
    val openingTime: String,
    val closingTime: String,
    val preferredDeliverySlotHour: String = ""
) {

    fun copy(
        openingTime: String,
        closingTime: String,
    ) = ShopTimings(
        openingTime = openingTime,
        closingTime = closingTime,
        preferredDeliverySlotHour = ""
    )
}
