package com.udaan.cart.models.common

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

enum class OfferType {
    FREEBIE,
    DISCOUNT,
    DISCOUNTED_ITEM,
    LOGISTICS,
    UNKNOWN
}

enum class PromoType {
    PROMOTION,
    COUPON,
    UNKNOWN,
}

enum class OfferStatus {
    APPLIED,
    APPLICABLE,
    AVAILABLE,
}

enum class OfferApplicabilityLevel {
    LINE_ITEM,
    CART,
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class OfferProduct(
    val listingId: String,
    val salesUnitId: String,
    val unit: Int,
    val priceInPaisa: Long? = null, // Used in case of freebie to represent offer item' price
    val discountInPaise: Long? = null,
    val discountBps: Double? = null,
)

enum class DistanceToRedemptionUnit {
    PAISE,
    UNITS
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class DistanceToRedemption(
    val unit: DistanceToRedemptionUnit,
    val currentValue: Long,
    val targetValue: Long,
    val message: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class OfferMetaData @JvmOverloads constructor(
    val products: List<OfferProduct> = emptyList(),
    val itemId: List<String> = emptyList(),
    val isDeal: Boolean = false,
    val couponId: String? = null,
    val couponCode: String? = null,
    val offerExpiry: Long? = null,
    val distanceToRedemption: DistanceToRedemption? = null,
    val tierIndex: Int? = null
) {
    fun copy(
        products: List<OfferProduct>,
        itemId: List<String>,
        isDeal: Boolean,
        couponId: String?,
        couponCode: String?,
        offerExpiry: Long?,
        distanceToRedemption: DistanceToRedemption?
    ) = OfferMetaData(
        products = products,
        itemId= itemId,
        isDeal = isDeal,
        couponId = couponId,
        couponCode = couponCode,
        offerExpiry = offerExpiry,
        distanceToRedemption = distanceToRedemption,
        tierIndex = tierIndex
    )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class Offer(
    val promotionId: String,
    val offerType: OfferType,
    val promoType: PromoType,
    val status: OfferStatus,
    val applicabilityLevel: OfferApplicabilityLevel,
    val title: String,
    val summary: String,
    val description: String,
    val termsAndConditions: String,
    val metaData: OfferMetaData,
)
