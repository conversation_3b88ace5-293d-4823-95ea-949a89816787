package com.udaan.cart.models.common

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class PUResDto(
        val userPuType: String,
        val assortment: Int? = null,
        val multiplier: Int? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class PriceDetails @JvmOverloads constructor(
        val subTotal: Long,
        val discount: Long,
        val taxTotal: Long,
        val totalAmount: Long,
        val prePromoSubTotal: Long,
        val prePromoTaxTotal: Long,
        val prePromoTotalAmount: Long,
        val puInfo: PUResDto? = null,
        val taxBps: Int? = null
) {
    fun copy(
            subTotal: Long,
            discount: Long,
            taxTotal: Long,
            totalAmount: Long,
            prePromoSubTotal: Long,
            prePromoTaxTotal: Long,
            prePromoTotalAmount: Long,
            puInfo: PUResDto? = null,
    ) = PriceDetails(
            subTotal = subTotal,
            discount = discount,
            taxTotal = taxTotal,
            totalAmount = totalAmount,
            prePromoSubTotal = prePromoSubTotal,
            prePromoTaxTotal = prePromoTaxTotal,
            prePromoTotalAmount = prePromoTotalAmount,
            puInfo = puInfo,
            taxBps = null
    )
}
