package com.udaan.cart.models.common

enum class PaymentCollectionType {
    <PERSON><PERSON><PERSON><PERSON>,
    TOKEN,
    COD,
    CREDIT,
    TRADE_CREDIT
}

enum class MetadataVariables {
    STATUS,
    STATE,
    BREACH_AMOUNT,
    CART_SELECTION_KEY,
    <PERSON><PERSON><PERSON><PERSON><PERSON>UND_IMAGE,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>ING_OVERDUE,
    LINE_TYPE,
    ERROR,
    TNC_LINK,
    CPOD_TC_AVAILABLE,
    CPOD_TC_AVAILABLE_PURCHASE_AMOUNT,
    TC_LINE_ID,
    DISCLAIMER,  // populated in preorder service
    CREDIT_LIMIT, // used by preorder-svc
    ORDER_AMOUNT,
    STATUS_CALLOUT,
    <PERSON><PERSON><PERSON><PERSON>LE_PURCHASE_AMOUNT,
    CHECKOUT_EXPERIENCE_TYPE,
    DISABLE_CPOD_EXPERIENCE,
    CPOD_BUYER
}

enum class PaymentMethodStatus {
    ACTIVE,
    INACTIVE,
    TNC
}

enum class CheckoutExperienceType {
    NON_CPOD,
    CPOD,
    CPOD_OVERDUE
}
