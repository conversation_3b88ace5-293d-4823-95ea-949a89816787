package com.udaan.cart.models.default

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.cart.models.BaseRequestDto
import com.udaan.cart.models.common.*
import com.udaan.proto.models.ModelV1.SellingPlatform

@JsonIgnoreProperties(ignoreUnknown = true)
data class CreateCartReqDto(
    val buyerId: String,
    val platformId: SellingPlatform,
    val cartSelection: CartSelection,
) : BaseRequestDto {
    override fun getReqBuyerId(): String = buyerId

    override fun getReqPlatformId(): SellingPlatform = platformId

    override fun getReqCartSelection(): CartSelection = cartSelection
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class FetchCartReqDto(
    val buyerId: String,
    val platformId: SellingPlatform,
    val requester: Requester,
    val cartId: String?,
    val buyerOrgUnitId: String? = null,
    val cartSelection: CartSelection?,
    val couponIds: Set<String> = emptySet(),
    val responseFlags: ResponseFlags = ResponseFlagsDataBuilder.emptyFlags(),
    val fetchBehaviour: FetchBehaviour? = null,
) : BaseRequestDto {
    override fun getReqBuyerId() = buyerId
    override fun getReqPlatformId() = platformId
    override fun getReqCartSelection() = cartSelection
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class EditCartReqDto(
    val buyerId: String,
    val platformId: SellingPlatform,
    val requester: Requester,
    val products: List<Product>,
    val buyerOrgUnitId: String? = null,
    val cartId: String? = null,
    val cartSelection: CartSelection?,
    val additionalData: AdditionalData = emptyMap(),
    val couponIds: Set<String> = emptySet(),
    val adPromoInfo: AdPromoInfo? = null,
    val responseFlags: ResponseFlags = ResponseFlagsDataBuilder.emptyFlags(),
    val requestVersion: Long? = null,
    val editBehaviour: EditBehaviour? = null,
    val fetchBehaviour: FetchBehaviour? = null,
) : BaseRequestDto {
    override fun getReqBuyerId() = buyerId
    override fun getReqPlatformId() = platformId
    override fun getReqCartSelection() = cartSelection

    fun getListingProducts(): List<ListingProduct> {
        val listingProducts = products.filterIsInstance<ListingProduct>()
        val productsGroup = listingProducts.map { listingProduct ->
            "${listingProduct.listingId}:${listingProduct.salesUnitId}" to listingProduct
        }.groupBy { it.first }
        return productsGroup.map { (_, itemsPair) ->
            val items = itemsPair.map { it.second }
            ListingProduct(
                listingId = items.first().listingId,
                salesUnitId = items.first().salesUnitId,
                quantity = items.sumOf { it.quantity },
                puInfo = items.firstOrNull { it.puInfo != null }?.puInfo,
                creditTenure = items.firstOrNull()?.creditTenure,
                creditLineId = items.firstOrNull()?.creditLineId
            )
        }
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class DeleteCartReqDto(
    val buyerId: String,
    val platformId: SellingPlatform,
    val requester: Requester,
    val cartId: String,
    val cartSelection: CartSelection? = null,
) : BaseRequestDto {
    override fun getReqBuyerId() = buyerId
    override fun getReqPlatformId() = platformId
    override fun getReqCartSelection() = cartSelection
}

enum class RequestExtraDataKeys {
    CPOD_EXP_ENABLED,
    CPOD_V2_SUPPORTED,
    CPOD_OVERDUE_EXP_ENABLED,
    OTP_ENABLED
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class PaymentMethodsReqDto @JvmOverloads constructor(
    val buyerId: String,
    val platformId: SellingPlatform,
    val requester: Requester,
    val cartId: String?,
    val buyerOrgUnitId: String? = null,
    val cartSelection: CartSelection?,
    val couponIds: Set<String> = emptySet(),
    val deliverySlot: String? = null,
    val extraData: Map<RequestExtraDataKeys, Any> = emptyMap()
) : BaseRequestDto {
    override fun getReqBuyerId() = buyerId
    override fun getReqPlatformId() = platformId
    override fun getReqCartSelection() = cartSelection

    fun copy(
        buyerId: String,
        platformId: SellingPlatform,
        requester: Requester,
        cartId: String?,
        buyerOrgUnitId: String? = null,
        cartSelection: CartSelection?,
        couponIds: Set<String> = emptySet(),
        deliverySlot: String? = null,
    ): PaymentMethodsReqDto {
        return PaymentMethodsReqDto(
            buyerId,
            platformId,
            requester,
            cartId,
            buyerOrgUnitId,
            cartSelection,
            couponIds,
            deliverySlot
        )
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class CheckoutRequestDto @JvmOverloads constructor(
    val buyerId: String,
    val platformId: SellingPlatform,
    val requester: Requester,
    val cartId: String? = null,
    val cartSelection: CartSelection?,
    val collectionType: PaymentCollectionType,
    val buyerOrgUnitId: String,
    val billToOrgUnitId: String? = null,
    val deliverySlotId: String? = null,
    val useRewardsWorth: Int? = null,
    val instruction: String? = null,
    val itemLevelDeliverySlot: Map<String, String>? = null,
    val extraData: Map<RequestExtraDataKeys, Any> = emptyMap()
) : BaseRequestDto {
    override fun getReqBuyerId() = buyerId
    override fun getReqPlatformId() = platformId
    override fun getReqCartSelection() = cartSelection

    fun copy(
        buyerId: String,
        platformId: SellingPlatform,
        requester: Requester,
        cartId: String? = null,
        cartSelection: CartSelection?,
        collectionType: PaymentCollectionType,
        buyerOrgUnitId: String,
        billToOrgUnitId: String? = null,
        deliverySlotId: String? = null,
        useRewardsWorth: Int? = null,
        instruction: String? = null,
        itemLevelDeliverySlot: Map<String, String>? = null
    ): CheckoutRequestDto {
        return CheckoutRequestDto(
            buyerId,
            platformId,
            requester,
            cartId,
            cartSelection,
            collectionType,
            buyerOrgUnitId,
            billToOrgUnitId,
            deliverySlotId,
            useRewardsWorth,
            instruction,
            itemLevelDeliverySlot
        )
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class UpdateCreditLineReqDto(
    val buyerId: String,
    val creditLineId: String,
    val creditTenure: String? = "DAY_7",
    val platformId: SellingPlatform,
    val cartSelection: CartSelection?,
) : BaseRequestDto {
    override fun getReqBuyerId() = buyerId
    override fun getReqPlatformId() = platformId
    override fun getReqCartSelection() = cartSelection
}
