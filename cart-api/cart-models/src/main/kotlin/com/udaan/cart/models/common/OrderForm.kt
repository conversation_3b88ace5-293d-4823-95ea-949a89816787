package com.udaan.cart.models.common

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.order_mgt.models.pg.PaymentGatewayV1
import com.udaan.order_mgt.representations.OrderV2
import com.udaan.order_mgt.representations.OrderV2.PaymentInstrumentOffer
import com.udaan.order_mgt.representations.OrderV2.RewardPoints
import com.udaan.order_mgt.representations.fulfillment_view.FulfillmentViewV1
import com.udaan.order_mgt.representations.logicstics_view.LogisticsViewV1

@JsonIgnoreProperties(ignoreUnknown = true)
data class OFRateCard(
    val localChargeInPaisePerKg: Long,
    val regionalChargeInPaisePerKg: Long,
    val nationalChargeInPaisePerKg: Long,
    val regionalFixedBaseDeliveryChargeInPaise: Long,
    val regionalOdaDeliveryChargeInPaise: Long,
    val nationalFixedBaseDeliveryChargeInPaise: Long,
    val nationalOdaDeliveryChargeInPaise: Long,
)

fun Map<String, LogisticsViewV1.RateCard>.toOFRateCard(): Map<String, OFRateCard> {
    return this.map { (key, value) ->
        key to OFRateCard(
            localChargeInPaisePerKg = value.localChargeInPaisePerKg,
            regionalChargeInPaisePerKg = value.regionalChargeInPaisePerKg,
            nationalChargeInPaisePerKg = value.nationalChargeInPaisePerKg,
            regionalFixedBaseDeliveryChargeInPaise = value.regionalFixedBaseDeliveryChargeInPaise,
            regionalOdaDeliveryChargeInPaise = value.regionalOdaDeliveryChargeInPaise,
            nationalFixedBaseDeliveryChargeInPaise = value.nationalFixedBaseDeliveryChargeInPaise,
            nationalOdaDeliveryChargeInPaise = value.nationalOdaDeliveryChargeInPaise,
        )
    }.toMap()
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class OFDeliveryEstimate(
    val minHoursToDispatch: Int,
    val maxHoursToDispatch: Int,
    val minHoursToDeliver: Int,
    val maxHoursToDeliver: Int,
    val minHoursToRto: Int,
    val maxHoursToRto: Int,
    val hideDeliveryText: Boolean,
    val deliveryDay: String,
)

fun Map<String, LogisticsViewV1.DeliveryEstimate>.toOFDeliveryEstimate(): Map<String, OFDeliveryEstimate> {
    return this.map { (key, value) ->
        key to OFDeliveryEstimate(
            minHoursToDispatch = value.minHoursToDispatch,
            maxHoursToDispatch = value.maxHoursToDispatch,
            minHoursToDeliver = value.minHoursToDeliver,
            maxHoursToDeliver = value.maxHoursToDeliver,
            minHoursToRto = value.minHoursToRto,
            maxHoursToRto = value.maxHoursToRto,
            hideDeliveryText = value.hideDeliveryText,
            deliveryDay = value.deliveryDay,
        )
    }.toMap()
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class OFPaymentInstrumentOffer(
    val description: String,
    val tnc: String,
)

fun PaymentInstrumentOffer.toOFPaymentInstrumentOffer(): OFPaymentInstrumentOffer {
    return OFPaymentInstrumentOffer(
        description = this.description,
        tnc = this.tnc,
    )
}

fun List<PaymentInstrumentOffer>.toOFPaymentInstrumentOffer(): List<OFPaymentInstrumentOffer> {
    return this.map {
        it.toOFPaymentInstrumentOffer()
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class OFPaymentOption(
    val priority: Int,
    val instrumentId: PaymentGatewayV1.PaymentInstrument,
    val displayName: String,
    val eligible: Boolean,
    val metaData: Map<String, String>,
    val availableAmountInPaisa: Long,
    val onlinePaymentType: OrderV2.PaymentOption.OnlinePaymentType,
    val offers: List<OFPaymentInstrumentOffer>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class OFPaymentOptions(
    val paymentOption: List<OFPaymentOption>
)

fun Map<String, OrderV2.PaymentOptions>.toOFPaymentOptions(): Map<String, OFPaymentOptions> {
    return this.map { (key, value) ->
        key to OFPaymentOptions(
            paymentOption = value.paymentOptionsList.map { paymentOption ->
                OFPaymentOption(
                    priority = paymentOption.priority,
                    instrumentId = paymentOption.instrumentId,
                    displayName = paymentOption.displayName,
                    eligible = paymentOption.eligible,
                    metaData = paymentOption.metaDataMap,
                    availableAmountInPaisa = paymentOption.availableAmountInPaisa,
                    onlinePaymentType = paymentOption.onlinePaymentType,
                    offers = paymentOption.offersList.toOFPaymentInstrumentOffer(),
                )
            }
        )
    }.toMap()
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class OFDeliverySlot(
    val slotId: String,
    val startMinuteOfDay: Int,
    val endMinuteOfDay: Int,
    val deliveryDay: Int,
    val slotAvailable: Boolean,
    val slotText: String,
    val excludedPincodes: List<String>,
    val pincodePrefix: Int,
    val category: String,
    val currentActive: Boolean,
    val isDelayedPromise: Boolean
)

fun FulfillmentViewV1.DeliverySlot.toOFDeliverySlot(): OFDeliverySlot {
    return OFDeliverySlot(
        slotId = this.slotId,
        startMinuteOfDay = this.startMinuteOfDay,
        endMinuteOfDay = this.endMinuteOfDay,
        deliveryDay = this.deliveryDay,
        slotAvailable = this.slotAvailable,
        slotText = this.slotText,
        excludedPincodes = this.excludedPincodesList,
        pincodePrefix = this.pincodePrefix,
        category = this.category,
        currentActive = this.currentActive,
        isDelayedPromise = this.isDelayedPromise,
    )
}

fun List<FulfillmentViewV1.DeliverySlot>.toOFDeliverySlot(): List<OFDeliverySlot> {
    return this.map {
        it.toOFDeliverySlot()
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class OFRewardPoints(
    val totalPoints: Int,
    val burnEligiblePoints: Int,
    val earnEligiblePoints: Int,
)

fun RewardPoints.toOFRewardPoints(): OFRewardPoints {
    return OFRewardPoints(
        totalPoints = this.totalPoints,
        burnEligiblePoints = this.burnEligiblePoints,
        earnEligiblePoints = this.earnEligiblePoints,
    )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class OrderFormPaymentMethod(
    val rateCards: Map<String, OFRateCard>,
    val deliveryEstimates: Map<String, OFDeliveryEstimate>,
    val paymentOptions: Map<String, OFPaymentOptions>,
    val estimatedChargesPerKgInPaisa: Map<String, Long>,
    val udaanInitiatedPrepayment: Boolean,
    val prepaymentPercentage: Long,
    val messagePrepayment: String,
    val deliverySlots: List<OFDeliverySlot>,
    val prepaymentAmount: Long,
    val rewardPoints: OFRewardPoints,
    val ineligibleOrgUnitIds: List<String> = emptyList(),
)

fun OrderV2.PrepareOrderResponse.toOrderFormPaymentMethod(): OrderFormPaymentMethod {
    return OrderFormPaymentMethod(
        rateCards = this.rateCardsMap.toOFRateCard(),
        deliveryEstimates = this.deliveryEstimatesMap.toOFDeliveryEstimate(),
        paymentOptions = this.paymentOptionsMap.toOFPaymentOptions(),
        estimatedChargesPerKgInPaisa = this.estimatedChargesPerKgInPaisaMap,
        udaanInitiatedPrepayment = this.udaanInitiatedPrepayment,
        prepaymentPercentage = this.prepaymentPercentage,
        messagePrepayment = this.messagePrepayment,
        deliverySlots = this.deliverySlotsList.toOFDeliverySlot(),
        prepaymentAmount = this.prepaymentAmount,
        rewardPoints = this.rewardPoints.toOFRewardPoints(),
        ineligibleOrgUnitIds = this.ineligibleOrgUnitIdsList,
    )
}
