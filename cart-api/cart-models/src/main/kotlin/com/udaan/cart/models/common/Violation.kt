package com.udaan.cart.models.common

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo

enum class ViolationType {
    MOV,
    MOQ_LISTING,
    MOQ_SALES_UNIT,
    MAX_UNIT,
    INVENTORY_MISMATCH,
    MAX_INVENTORY,
    MAX_GROUP_INVENTORY,
    ORG_UNIT_SHOP_TIMINGS_NOT_FOUND
}

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = MovViolation::class, name = "MOV"),
    JsonSubTypes.Type(value = MaxUnitViolation::class, name = "MAX_UNIT"),
    JsonSubTypes.Type(value = MoqViolation::class, name = "MOQ_LISTING"),
    JsonSubTypes.Type(value = MoqViolation::class, name = "MOQ_SALES_UNIT"),
    JsonSubTypes.Type(value = InventoryMismatchViolation::class, name = "INVENTORY_MISMATCH"),
    JsonSubTypes.Type(value = MaxInventoryViolation::class, name = "MAX_INVENTORY"),
    JsonSubTypes.Type(value = MaxGroupInventoryViolation::class, name = "MAX_GROUP_INVENTORY"),
)
sealed class ViolationMetaData(type: ViolationType)

@JsonIgnoreProperties(ignoreUnknown = true)
data class MovViolation(
    val amountInPaise: Long
) : ViolationMetaData(ViolationType.MOV)

@JsonIgnoreProperties(ignoreUnknown = true)
data class MoqViolation(
    val listingId: String,
    val salesUnitId: String,
    val listingMoq: Int,
    val salesUnitMoq: Int
) : ViolationMetaData(ViolationType.MOQ_LISTING)

@JsonIgnoreProperties(ignoreUnknown = true)
data class MaxUnitViolation(
    val listingId: String,
    val salesUnitId: String,
    val maxUnits: Int
) : ViolationMetaData(ViolationType.MAX_UNIT)

@JsonIgnoreProperties(ignoreUnknown = true)
data class InventoryMismatchViolation(
    val listingId: String,
    val salesUnitId: String,
    val requestedQuantity: Int,
    val appliedQuantity: Int,
): ViolationMetaData(ViolationType.INVENTORY_MISMATCH)

@JsonIgnoreProperties(ignoreUnknown = true)
data class MaxInventoryViolation(
    val listingId: String,
    val salesUnitId: String,
    val maxQuantity: Int,
): ViolationMetaData(ViolationType.MAX_INVENTORY)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ListingSalesUnitData(
    val listingId: String,
    val salesUnitId: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class MaxGroupInventoryViolation(
    val items: List<ListingSalesUnitData>,
    val maxQuantity: Double,
    val measureUnit: MeasureUnit,
    val groupType: MaxGroupType,
): ViolationMetaData(ViolationType.MAX_GROUP_INVENTORY)

@JsonIgnoreProperties(ignoreUnknown = true)
data class OrgUnitShopTimingsNotFoundViolation (
    val shopTimings: ShopTimings
) : ViolationMetaData(ViolationType.ORG_UNIT_SHOP_TIMINGS_NOT_FOUND)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ViolationDto(
    val type: ViolationType,
    val metaData: ViolationMetaData
)

enum class MaxGroupType {
    VERTICAL,
    PRODUCT_GROUP,
    SALES_UNIT,
    NONE,
}

enum class MeasureUnit {
    WEIGHT_KG,
    UNITS,
    NONE,
}
