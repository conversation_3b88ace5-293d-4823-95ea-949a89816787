<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.udaan.cart</groupId>
        <artifactId>cart-api</artifactId>
        <version>2.0-SNAPSHOT</version>
    </parent>

    <artifactId>cart-models</artifactId>
    <packaging>jar</packaging>

    <name>Udaan cart Models</name>

    <dependencies>

        <!-- Udaan Common -->
        <dependency>
            <groupId>com.udaan.common</groupId>
            <artifactId>common-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.users</groupId>
            <artifactId>user-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.udaan-orderform</groupId>
            <artifactId>orderform-models</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.constraint</groupId>
            <artifactId>constraint-models</artifactId>
        </dependency>

        <!-- Testing -->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test-junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Codecs -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.udaan.rewards</groupId>
            <artifactId>rewards-models</artifactId>
        </dependency>

        <!-- Utils -->

    </dependencies>

    <build>
        <plugins>

            <plugin>
                <artifactId>kotlin-maven-plugin</artifactId>
                <groupId>org.jetbrains.kotlin</groupId>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>process-sources</phase>
                        <goals> <goal>compile</goal> </goals>
                        <configuration>
                            <sourceDirs>
                                <source>src/main/kotlin</source>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <phase>process-test-sources</phase>
                        <goals> <goal>test-compile</goal> </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>

</project>
