user-service:
  baseUrl: "http://user-service"
  httpClientConfig:
    requestTimeout: 10000

credit:
  baseUrl: "http://credit-service"
  httpClientConfig:
    requestTimeout: 20000
    threadPoolName: AsyncHttpClient-credit

resilient-catalog-read-path:
  baseUrl: "http://catalog-readpath-service"
  httpClientConfig:
    connectTimeout: 1000
    readTimeout: 5000
    requestTimeout: 5000
    threadPoolName: AsyncHttpClient-catalog
  resiliencyConfig:
    operationTimeoutMillis: 5000
    circuitbreaker-config:
      failureThreshold: 60
      minimumCalls: 500
      slidingWindowSize: 600
      slowCallDurationMillis: 5000

resiliency-price-read-path:
  baseUrl: "http://pricing-service"
  httpClientConfig:
    requestTimeout: 15000
  resiliencyConfig:
    operationTimeoutMillis: 15000
    circuitbreaker-config:
      failureThreshold: 60
      minimumCalls: 200
      slidingWindowSize: 300
      slowCallDurationMillis: 15000

orderform-service:
  baseUrl: "http://orderform-service"
  httpClientConfig:
    requestTimeout: 50000


pacman-orderform-service:
  baseUrl: "http://pacman-orderform-service"
  httpClientConfig:
    requestTimeout: 50000

chat:
  baseUrl: "http://chat-service"
  httpClientConfig:
    requestTimeout: 10000
    threadPoolName: AsyncHttpClient-chat

compliance-service:
  baseUrl: "http://compliance-service"
  httpClientConfig:
    requestTimeout: 20000

orchestrator-service:
  baseUrl: "http://orchestrator-service"
  httpClientConfig:
    requestTimeout: 10000

constraint-service:
  baseUrl: "http://constraint-service"
  httpClientConfig:
    requestTimeout: 15000

promotions-service:
  baseUrl: "http://promotions-service"
  httpClientConfig:
    requestTimeout: 30000

fulfillment-service:
  baseUrl: "http://fulfilment-service"
  httpClientConfig:
    requestTimeout: 10000

dropslot-service:
  baseUrl: "http://dropslot-service"
  httpClientConfig:
    requestTimeout: 10000

trade-quality-service:
  baseUrl: "http://trade-quality-service"
  httpClientConfig:
    requestTimeout: 10000

config-service:
  baseUrl: "http://config-service"
  httpClientConfig:
    requestTimeout: 5000

rewards-service:
  baseUrl: "http://rewards-service"
  httpClientConfig:
    requestTimeout: 10000

order-read-service:
  baseUrl: "http://order-read-service"
  httpClientConfig:
    requestTimeout: 10000

trade-credit-service:
  baseUrl: "http://trade-credit-service"
  httpClientConfig:
    requestTimeout: 30000

server:
  rootPath: /
  applicationConnectors:
    - type: http
      port: 8080
  adminConnectors:
    - type: http
      port: 8081

logging:
  level: WARN
  loggers:
    "io.dropwizard": INFO
    "org.eclipse.jetty": INFO
    "com.udaan": INFO
    "com.udaan.cart": DEBUG
  appenders:
    - type: console
      threshold: INFO
      timeZone: "UTC"
      logFormat: "[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%X{TRACE-ID}] [%level] [%logger] %msg %n"

dcos:
  cpus: 2 # Per instance of this service, number of cpus needed (e.g. 0.5, or 1.5)
  mem: 4096 # Memory allocated to container in MBs
  servicePort: 17000  # Service port as found on https://sites.google.com/a/udaan.com/tech/services
  instancesDev: 1 # Number of instances to be spawned during deployment
  instancesProd: 1 # Number of instances to be spawned during deployment
  jvmArgs: >-
    -server -Xms1536m -Xmx3072m -XX:+PrintGCDetails 
    -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=9010
    -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false
    -Dkotlinx.coroutines.io.parallelism=2048 -Dkotlinx.coroutines.scheduler.max.pool.size=2048
  mainClass: com.udaan.cart.service.CartApplicationKt
  env:
    dev:
      istioEnabled: true
    prod:
      istioEnabled: true
      hpaSpec:
        maxReplicas: 2
        minReplicas: 1
        targetMemoryUtilizationPercentage: 80
  healthcheck: default
  # persistentDiskSize: 10240  # Size of the persistent disk needed. Use this only if you know that persistent disks "bind" your service locally to a machine
  # maxLaunchDelaySeconds: 120 # Maximum number of seconds to wait for the service to come up
  # minimumHealthCapacity: 1.0 # See the upgradeStrategy section under https://mesosphere.github.io/marathon/docs/rest-api.html
  # maximumOverCapacity: 1.0 # See the upgradeStrategy section under https://mesosphere.github.io/marathon/docs/rest-api.html
errorCodeConfig:
  serviceName: CART_SERVICE
