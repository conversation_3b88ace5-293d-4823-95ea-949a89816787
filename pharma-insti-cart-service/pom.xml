<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.udaan.cart</groupId>
        <artifactId>cart-parent</artifactId>
        <version>2.0-SNAPSHOT</version>
    </parent>

    <artifactId>pharma-insti-cart-service</artifactId>
    <packaging>jar</packaging>

    <name>Pharma Insti Cart Service</name>

    <properties>
        <app.main.class>com.udaan.cart.service.AppServerKt</app.main.class>
    </properties>

    <dependencies>
        <!-- Udaan cart -->
        <dependency>
            <groupId>com.udaan.cart</groupId>
            <artifactId>cart-service</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.1.1</version>
                <executions>
                    <execution>
                        <id>unpack-dependencies</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>unpack-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/classes</outputDirectory>
                            <excludeTransitive>true</excludeTransitive>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <artifactId>exec-maven-plugin</artifactId>
                <groupId>org.codehaus.mojo</groupId>
                <configuration>
                    <executable>./bin/update-server-config</executable>
                    <workingDirectory>${project.basedir}</workingDirectory>
                </configuration>
                <executions>
                    <execution>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
