name: 'PR description checker'
on:
  pull_request:
    types:
      - opened
      - edited
      - reopened
      - labeled
      - unlabeled
      - synchronize
      - ready_for_review
      - converted_to_draft
jobs:
  check-pr-description:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: jadrol/pr-description-checker-action@v1.0.0
        id: description-checker
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          template-path: "./pull_request_template.md"