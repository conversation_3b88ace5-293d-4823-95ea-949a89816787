name: main
'on':
  push:
    branches:
      - master
      - main
jobs:
  setup:
    runs-on:
      - self-hosted
      - runner-controller
    outputs:
      runner_name: ${{ steps.start_runner.outputs.runner_name }}
    steps:
      - id: start_runner
        env:
          WORKER_TYPE: kotlin-jdk21
        run: start-runner
  sonar:
    needs:
      - setup
    uses: udaan-com/shared-workflows/.github/workflows/sonarqube-config.yml@master
    with:
      runner_name: ${{ needs.setup.outputs.runner_name }}
  main:
    needs:
      - setup
      - sonar
    runs-on: ${{ needs.setup.outputs.runner_name }}
    env:
      MAVEN_OPTS: -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN
        -Dorg.slf4j.simpleLogger.showDateTime=true -Djava.awt.headless=true -DinstallAtEnd=true
        -DdeployAtEnd=true
      MAVEN_CLI_OPTS: -U -B -e -fae -V
      BUILDAH_FORMAT: docker
      SKIP_ABI_TEST: ${{ contains(github.event.head_commit.message, '#skip-abi-test') }}
    steps:
      - name: checkout
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Test-API
        if: ${{ !contains(github.event.head_commit.message, '#no_build')  }}
        run: |
          echo "Building and running tests for cart-api"
          mvn-cd-build mvn -P '!default,repo-proxy,abi-check' ${MAVEN_CLI_OPTS} -Djapicmp.skip=${SKIP_ABI_TEST} clean test install
        working-directory: cart-api
      - name: Archive Build Artifacts
        uses: actions/upload-artifact@v4
        if: ${{ (success() || failure()) }}
        with:
          name: japicmp
          path: "**/target/japicmp"
          retention-days: 90
      - name: Sonarqube Analysis
        if: ${{ !contains(github.event.head_commit.message, '#no_build') }}
        run: |
          echo ${{ needs.sonar.outputs.SONAR_URL }}
          mvn sonar:sonar -Dsonar.host.url=${{ needs.sonar.outputs.SONAR_URL }} -Dsonar.login=${{ secrets.SONAR_KEY }} -Dsonar.projectVersion=${{ steps.vars.outputs.sha_short }} -Dsonar.sources=src/main -Dsonar.java.binaries=.

      - name: Deploy-API
        if: ${{ !contains(github.event.head_commit.message, '#no_build') }}
        run: |
          echo "Deploying cart-api"
          mvn-cd-build mvn -P '!default,repo-proxy' ${MAVEN_CLI_OPTS} deploy -DskipTests
        working-directory: cart-api

      - name: Test
        if: ${{ !contains(github.event.head_commit.message, '#no_build')  }}
        run: |
          echo "Building and running tests"
          mvn-cd-build mvn -P '!default,repo-proxy' ${MAVEN_CLI_OPTS} clean test

      - name: Prepare-Copy-Dependencies
        if: ${{ !contains(github.event.head_commit.message, '#no_build') }}
        run: |
          echo "Copying dependencies and creating slim jar"
          mvn-cd-build mvn -P '!default,repo-proxy' ${MAVEN_CLI_OPTS} -DskipTests -DexcludeScope=provided dependency:copy-dependencies package

      - name: Push-Docker-Images
        if: ${{ !contains(github.event.head_commit.message, '#no_build') }}
        run: |
          echo "Pushing jobs image to docker registry"
          mvn-cd-build 'cd cart-jobs/target && MODULE=cart-jobs buildctl image'
          echo "Pushing service image to docker registry"
          mvn-cd-build 'cd cart-service/target && MODULE=cart-service buildctl image'

      - name: Deploy-All-Dev-Service
        run: |-
          if ${{ contains(github.event.head_commit.message, '#no_build') }} ;  then

          echo "Deploying service to dev k8s"
          mvn-cd-build 'cd cart-service/src && buildctl deploy dev'

          else

          echo "Deploying service to dev k8s"
          mvn-cd-build 'cd cart-service/target && buildctl deploy dev'

          fi

      - name: Deploy-All-Prod-Service
        if: ${{ (contains(github.event.head_commit.message, '#deploy-to-prod')) }}
        run: |-
          if ${{ contains(github.event.head_commit.message, '#no_build') }} ;  then

          echo "Deploying service to prod k8s"
          mvn-cd-build 'cd cart-service/src && buildctl deploy prod'

          else

          echo "Deploying service to prod k8s"
          mvn-cd-build 'cd cart-service/target && buildctl deploy prod'

          fi

      - name: Trim-mvn-parent-versions
        if: ${{ !contains(github.event.head_commit.message, '#no_build') }}
        run: |
          echo "Trimming all versions other than the last 5"
          mvn-cd-build 'trim-pom-versions pom.xml 5'

      - name: Trim-mvn-versions
        if: ${{ !contains(github.event.head_commit.message, '#no_build') }}
        run: |
          echo "Trimming all versions other than the last 5"
          mvn-cd-build 'trim-pom-versions pom.xml 5'
        working-directory: cart-api

    timeout-minutes: 25
  teardown:
    needs:
      - setup
      - sonar
      - main
    if: always()
    runs-on:
      - self-hosted
      - runner-controller
    steps:
      - name: teardown runner
        run: stop-runner ${{ needs.setup.outputs.runner_name }}